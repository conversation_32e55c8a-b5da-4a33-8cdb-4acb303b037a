🔍 COMPREHENSIVE TRANSLATION SCAN REPORT
============================================================

📊 SUMMARY: Found 1026 hardcoded English texts in 61 files

📄 templates\admin_billing.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           Admin Billing - Payment System

  Line   7: 'Payment' → 'Malipo'
           Admin Billing - Payment System

  Line   7: 'Billing' → 'Malipo'
           Admin Billing - Payment System

  Line  17: 'Billing' → 'Malipo'
           Billing Management

  Line  36: 'Pending' → 'Inasubiri'
           <p class="mb-0">Pending Bills</p>

  Line  60: 'Month' → 'Mwezi'
           <p class="mb-0">Revenue This Month</p>

  Line  66: 'Filter' → 'Chuja'
           <!-- Filter -->

  Line  71: 'Status' → 'Hali'
           <label for="status" class="form-label">Status Filter</label>

  Line  71: 'Filter' → 'Chuja'
           <label for="status" class="form-label">Status Filter</label>

  Line  73: 'All' → 'Yote'
           <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All</opti...

  Line  81: 'Filter' → 'Chuja'
           <i class="fas fa-search me-1"></i>Filter

  Line 106: 'Amount' → 'Kiasi'
           <th>Amount (TZS)</th>

  Line 107: 'Date' → 'Tarehe'
           <th>Due Date</th>

  Line 109: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line 129: 'Pending' → 'Inasubiri'
           {% if bill.status == 'Pending' %}bg-warning text-dark

  Line 137: 'View' → 'Ona'
           <a href="{{ url_for('bill_detail', bill_id=bill.id) }}" class="btn btn-outline-p...

  Line 140: 'Pending' → 'Inasubiri'
           {% if bill.status == 'Pending' %}

📄 templates\admin_companies.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           Manage Companies - Admin

  Line   7: 'Manage' → 'Simamia'
           Manage Companies - Admin

  Line   7: 'Companies' → 'Makampuni'
           Manage Companies - Admin

  Line  13: 'Manage' → 'Simamia'
           <h1><i class="fas fa-building me-2"></i>Manage Companies</h1>

  Line  13: 'Companies' → 'Makampuni'
           <h1><i class="fas fa-building me-2"></i>Manage Companies</h1>

  Line  15: 'Company' → 'Kampuni'
           <i class="fas fa-plus me-2"></i>Add New Company

  Line  15: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-2"></i>Add New Company

  Line  19: 'Statistics' → 'Takwimu'
           <!-- Statistics Cards -->

  Line  34: 'Active' → 'Hai'
           <h4 style="color: white; font-weight: 700;">{{ companies|selectattr('subscriptio...

  Line  59: 'Companies' → 'Makampuni'
           <!-- Companies Table -->

  Line  63: 'Companies' → 'Makampuni'
           <i class="fas fa-list me-2"></i>Companies List

  Line  76: 'Billing' → 'Malipo'
           <th>Next Billing</th>

  Line  76: 'Next' → 'Ifuatayo'
           <th>Next Billing</th>

  Line  78: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line 117: 'Active' → 'Hai'
           {% if company.subscription_status == 'Active' %}bg-success

  Line 145: 'Total' → 'Jumla'
           <small class="text-muted">Total volume</small>

  Line 151: 'Edit' → 'Hariri'
           <i class="fas fa-edit"></i> Edit

  Line 157: 'Active' → 'Hai'
           {% if company.subscription_status == 'Active' %}

  Line 164: 'Active' → 'Hai'
           onclick="toggleStatus('{{ company.id }}', 'Active')">

  Line 178: 'Companies' → 'Makampuni'
           <h4 class="text-muted">No Companies Yet</h4>

  Line 181: 'Company' → 'Kampuni'
           <i class="fas fa-plus me-2"></i>Add First Company

  Line 181: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-2"></i>Add First Company

📄 templates\admin_company_form.html
----------------------------------------
  Line   9: 'Company' → 'Kampuni'
           {{ 'Edit Company' if company else 'Create New Company' }}

  Line   9: 'Company' → 'Kampuni'
           {{ 'Edit Company' if company else 'Create New Company' }}

  Line   9: 'Edit' → 'Hariri'
           {{ 'Edit Company' if company else 'Create New Company' }}

  Line   9: 'Create' → 'Unda'
           {{ 'Edit Company' if company else 'Create New Company' }}

  Line  12: 'Companies' → 'Makampuni'
           <i class="fas fa-arrow-left me-2"></i>Back to Companies

  Line  12: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-2"></i>Back to Companies

  Line  23: 'Company' → 'Kampuni'
           <i class="fas fa-info-circle me-2"></i>Company Information

  Line  30: 'Company' → 'Kampuni'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  30: 'Name' → 'Jina'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  73: 'Number' → 'Nambari'
           <label for="company_tin" class="form-label">TIN Number</label>

  Line  88: 'Company' → 'Kampuni'
           <img src="{{ url_for('static', filename=company.logo_filename) }}" alt="Company ...

  Line  91: 'Upload' → 'Pakia'
           <div class="form-text">Upload a logo for your company (PNG, JPG, max 1MB).</div>

  Line 102: 'Payment' → 'Malipo'
           <i class="fas fa-mobile-alt me-2"></i>Payment Method Configuration

  Line 198: 'Billing' → 'Malipo'
           <!-- Subscription & Billing -->

  Line 203: 'Billing' → 'Malipo'
           <i class="fas fa-credit-card me-2"></i>Subscription & Billing

  Line 222: 'Billing' → 'Malipo'
           <label for="billing_cycle" class="form-label">Billing Cycle</label>

  Line 235: 'Status' → 'Hali'
           <label class="form-label">Subscription Status</label>

  Line 249: 'Billing' → 'Malipo'
           <label class="form-label">Next Billing Date</label>

  Line 249: 'Next' → 'Ifuatayo'
           <label class="form-label">Next Billing Date</label>

  Line 249: 'Date' → 'Tarehe'
           <label class="form-label">Next Billing Date</label>

  Line 257: 'Total' → 'Jumla'
           <label class="form-label">Total Transaction Volume</label>

  Line 288: 'Payment' → 'Malipo'
           <span class="badge bg-warning text-dark ms-2">Awaiting Payment</span>

  Line 318: 'Payment' → 'Malipo'
           Payment pending. Please complete payment to activate Dynamic POS.

  Line 320: 'Admin' → 'Msimamizi'
           <button type="submit" class="btn btn-sm btn-outline-success">Unlock POS (Admin)<...

  Line 342: 'Cancel' → 'Ghairi'
           <i class="fas fa-times me-2"></i>Cancel

  Line 346: 'Company' → 'Kampuni'
           {{ 'Update Company' if company else 'Create Company' }}

  Line 346: 'Company' → 'Kampuni'
           {{ 'Update Company' if company else 'Create Company' }}

  Line 346: 'Update' → 'Sasisha'
           {{ 'Update Company' if company else 'Create Company' }}

  Line 346: 'Create' → 'Unda'
           {{ 'Update Company' if company else 'Create Company' }}

  Line 405: 'Fee' → 'Ada'
           <strong>Setup Fee:</strong> TZS ${tier.setup_fee.toLocaleString()}

  Line 408: 'Fee' → 'Ada'
           <strong>${billingCycle} Fee:</strong> TZS ${fee.toLocaleString()}

  Line 411: 'Fee' → 'Ada'
           <strong>Transaction Fee:</strong> ${tier.transaction_fee_percentage}%

  Line 417: 'Features' → 'Vipengele'
           <strong>Features:</strong>

  Line 421: 'Support' → 'Msaada'
           ${tier.features.priority_support ? '<li><i class="fas fa-check text-success me-1...

  Line 422: 'Dashboard' → 'Dashibodi'
           ${tier.features.analytics_dashboard ? '<li><i class="fas fa-check text-success m...

  Line 422: 'Analytics' → 'Uchambuzi'
           ${tier.features.analytics_dashboard ? '<li><i class="fas fa-check text-success m...

📄 templates\admin_dashboard.html
----------------------------------------
  Line  71: 'Actions' → 'Vitendo'
           <!-- Quick Actions -->

  Line  76: 'Admin' → 'Msimamizi'
           <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick Admin Actions</h5>

  Line  76: 'Actions' → 'Vitendo'
           <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick Admin Actions</h5>

  Line  81: 'Invoices' → 'Ankara'
           <i class="fas fa-file-invoice me-2"></i>Manage Invoices

  Line  81: 'Manage' → 'Simamia'
           <i class="fas fa-file-invoice me-2"></i>Manage Invoices

  Line  84: 'Payments' → 'Malipo'
           <i class="fas fa-clock me-2"></i>Pending Payments

  Line  84: 'Pending' → 'Inasubiri'
           <i class="fas fa-clock me-2"></i>Pending Payments

  Line  87: 'Payments' → 'Malipo'
           <i class="fas fa-list me-2"></i>All Payments

  Line  87: 'All' → 'Yote'
           <i class="fas fa-list me-2"></i>All Payments

  Line  99: 'Company' → 'Kampuni'
           <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Billing & Company Manage...

  Line  99: 'Billing' → 'Malipo'
           <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Billing & Company Manage...

  Line 104: 'Manage' → 'Simamia'
           <i class="fas fa-building me-2"></i>Manage Companies

  Line 104: 'Companies' → 'Makampuni'
           <i class="fas fa-building me-2"></i>Manage Companies

  Line 110: 'View' → 'Ona'
           <i class="fas fa-file-invoice-dollar me-2"></i>View Bills

  Line 119: 'Manage' → 'Simamia'
           Manage all client companies, pricing tiers, and billing from one place.

  Line 144: 'Features' → 'Vipengele'
           <h6>Platform Features:</h6>

  Line 148: 'Admin' → 'Msimamizi'
           <li><i class="fas fa-check text-success me-2"></i>Admin Verification</li>

  Line 149: 'Status' → 'Hali'
           <li><i class="fas fa-check text-success me-2"></i>Status Tracking</li>

📄 templates\admin_invoices.html
----------------------------------------
  Line   7: 'Invoice' → 'Ankara'
           Invoice Management - Payment System

  Line   7: 'Payment' → 'Malipo'
           Invoice Management - Payment System

  Line  13: 'Invoice' → 'Ankara'
           <h1><i class="fas fa-file-invoice me-2"></i>Invoice Management</h1>

  Line  16: 'Invoice' → 'Ankara'
           <i class="fas fa-plus me-1"></i>Create Invoice

  Line  16: 'Create' → 'Unda'
           <i class="fas fa-plus me-1"></i>Create Invoice

  Line  19: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  19: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  29: 'Status' → 'Hali'
           <label for="status" class="form-label">Status Filter</label>

  Line  29: 'Filter' → 'Chuja'
           <label for="status" class="form-label">Status Filter</label>

  Line  31: 'All' → 'Yote'
           <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statu...

  Line  41: 'Search' → 'Tafuta'
           placeholder="Search by customer name or invoice number..." value="{{ search }}">

  Line  45: 'Filter' → 'Chuja'
           <i class="fas fa-search me-1"></i>Filter

  Line  55: 'Invoices' → 'Ankara'
           <!-- Invoices Table -->

  Line  59: 'Invoices' → 'Ankara'
           <i class="fas fa-table me-2"></i>Invoices

  Line  69: 'Invoice' → 'Ankara'
           <th>Invoice #</th>

  Line  70: 'Customer' → 'Mteja'
           <th>Customer</th>

  Line  71: 'Amount' → 'Kiasi'
           <th>Amount (TZS)</th>

  Line  74: 'Date' → 'Tarehe'
           <th>Due Date</th>

  Line  76: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line 114: 'Invoice' → 'Ankara'
           class="btn btn-outline-primary" title="View Invoice">

  Line 114: 'View' → 'Ona'
           class="btn btn-outline-primary" title="View Invoice">

  Line 119: 'Payment' → 'Malipo'
           class="btn btn-outline-info" title="Payment Instructions">

  Line 125: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 127: 'Payment' → 'Malipo'
           class="btn btn-outline-success" title="View Payment">

  Line 127: 'View' → 'Ona'
           class="btn btn-outline-success" title="View Payment">

  Line 148: 'Create' → 'Unda'
           Create your first invoice to get started.

  Line 152: 'Invoice' → 'Ankara'
           <i class="fas fa-plus me-1"></i>Create Invoice

  Line 152: 'Create' → 'Unda'
           <i class="fas fa-plus me-1"></i>Create Invoice

📄 templates\admin_monitoring.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           System Monitoring - Admin Dashboard

  Line   7: 'Dashboard' → 'Dashibodi'
           System Monitoring - Admin Dashboard

  Line  24: 'Status' → 'Hali'
           <!-- System Health Status -->

  Line  61: 'Metrics' → 'Vipimo'
           <!-- Detailed Metrics -->

  Line  66: 'Companies' → 'Makampuni'
           <h6 class="mb-0"><i class="fas fa-building me-2"></i>Companies</h6>

  Line  97: 'Metrics' → 'Vipimo'
           <!-- Revenue Metrics -->

  Line 102: 'Metrics' → 'Vipimo'
           <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Revenue Metrics</h6>

  Line 149: 'Status' → 'Hali'
           <small class="d-block mt-1">Overall Status</small>

  Line 159: 'Date' → 'Tarehe'
           <small class="text-muted">Last updated: ${new Date(data.timestamp).toLocaleStrin...

  Line 177: 'Active' → 'Hai'
           <small>Active Companies</small>

  Line 177: 'Companies' → 'Makampuni'
           <small>Active Companies</small>

  Line 181: 'Payments' → 'Malipo'
           <small>Today's Payments</small>

  Line 181: 'Today' → 'Leo'
           <small>Today's Payments</small>

  Line 187: 'Companies' → 'Makampuni'
           // Companies metrics

  Line 190: 'Companies' → 'Makampuni'
           <span class="badge bg-primary">${data.companies.total}</span> Total Companies

  Line 190: 'Total' → 'Jumla'
           <span class="badge bg-primary">${data.companies.total}</span> Total Companies

  Line 193: 'Active' → 'Hai'
           <span class="badge bg-success">${data.companies.active}</span> Active

  Line 201: 'Payments' → 'Malipo'
           // Payments metrics

  Line 204: 'Payments' → 'Malipo'
           <span class="badge bg-primary">${data.payments.total}</span> Total Payments

  Line 204: 'Total' → 'Jumla'
           <span class="badge bg-primary">${data.payments.total}</span> Total Payments

  Line 207: 'Pending' → 'Inasubiri'
           <span class="badge bg-warning">${data.payments.pending}</span> Pending

  Line 210: 'Confirmed' → 'Imethibitishwa'
           <span class="badge bg-success">${data.payments.confirmed}</span> Confirmed

  Line 215: 'Invoices' → 'Ankara'
           // Invoices metrics

  Line 218: 'Invoices' → 'Ankara'
           <span class="badge bg-primary">${data.invoices.total}</span> Total Invoices

  Line 218: 'Total' → 'Jumla'
           <span class="badge bg-primary">${data.invoices.total}</span> Total Invoices

📄 templates\admin_payments.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Payment Management - Payment System

  Line   7: 'Payment' → 'Malipo'
           Payment Management - Payment System

  Line  13: 'Payment' → 'Malipo'
           <h1><i class="fas fa-list me-2"></i>Payment Confirmations</h1>

  Line  15: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  15: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  24: 'Status' → 'Hali'
           <label for="status" class="form-label">Status Filter</label>

  Line  24: 'Filter' → 'Chuja'
           <label for="status" class="form-label">Status Filter</label>

  Line  26: 'All' → 'Yote'
           <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statu...

  Line  36: 'Search' → 'Tafuta'
           placeholder="Search by customer name or transaction ID..." value="{{ search }}">

  Line  40: 'Filter' → 'Chuja'
           <i class="fas fa-search me-1"></i>Filter

  Line  50: 'Payments' → 'Malipo'
           <!-- Payments Table -->

  Line  54: 'Payment' → 'Malipo'
           <i class="fas fa-table me-2"></i>Payment Confirmations

  Line  65: 'Customer' → 'Mteja'
           <th>Customer</th>

  Line  66: 'Amount' → 'Kiasi'
           <th>Amount (TZS)</th>

  Line  71: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line  99: 'Pending' → 'Inasubiri'
           {% if payment.status == 'Pending' %}bg-warning text-dark

  Line 100: 'Confirmed' → 'Imethibitishwa'
           {% elif payment.status == 'Confirmed' %}bg-success

  Line 101: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}bg-danger

  Line 110: 'View' → 'Ona'
           <i class="fas fa-eye me-1"></i>View

  Line 126: 'Payment' → 'Malipo'
           Payment confirmations will appear here once customers submit them.

📄 templates\admin_pos_requests.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           Pending POS Unlock Requests - Admin

  Line   7: 'Pending' → 'Inasubiri'
           Pending POS Unlock Requests - Admin

  Line  13: 'Pending' → 'Inasubiri'
           <h2 class="mb-4"><i class="fas fa-cash-register me-2 text-warning"></i>Pending P...

  Line  47: 'Dashboard' → 'Dashibodi'
           <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-link">&larr; Ba...

  Line  47: 'Back' → 'Rudi'
           <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-link">&larr; Ba...

📄 templates\admin_pricing_tiers.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           Admin Pricing Tiers - Payment System

  Line   7: 'Payment' → 'Malipo'
           Admin Pricing Tiers - Payment System

  Line  25: 'Add' → 'Ongeza'
           Add New Tier

  Line  36: 'All' → 'Yote'
           All Pricing Tiers <span class="badge bg-secondary ms-2">{{ tiers|length }} recor...

  Line  52: 'Fee' → 'Ada'
           Setup Fee

  Line  59: 'Fee' → 'Ada'
           Monthly Fee

  Line  66: 'Fee' → 'Ada'
           Annual Fee

  Line  73: 'Fee' → 'Ada'
           Transaction Fee (%)

  Line  80: 'Fee' → 'Ada'
           Fixed Fee

  Line  96: 'Actions' → 'Vitendo'
           Actions

  Line 193: 'Add' → 'Ongeza'
           Add New Tier

📄 templates\admin_users.html
----------------------------------------
  Line   7: 'Admin' → 'Msimamizi'
           Manage Users - Admin

  Line   7: 'Manage' → 'Simamia'
           Manage Users - Admin

  Line   7: 'Users' → 'Watumiaji'
           Manage Users - Admin

  Line  13: 'Admin' → 'Msimamizi'
           <h1><i class="fas fa-users me-2"></i>Manage Admin Users</h1>

  Line  13: 'Manage' → 'Simamia'
           <h1><i class="fas fa-users me-2"></i>Manage Admin Users</h1>

  Line  13: 'Users' → 'Watumiaji'
           <h1><i class="fas fa-users me-2"></i>Manage Admin Users</h1>

  Line  15: 'User' → 'Mtumiaji'
           <i class="fas fa-user-plus me-2"></i>Add New User

  Line  15: 'Add' → 'Ongeza'
           <i class="fas fa-user-plus me-2"></i>Add New User

  Line  19: 'Statistics' → 'Takwimu'
           <!-- Statistics Cards -->

  Line  26: 'Users' → 'Watumiaji'
           <p class="mb-0">Total Users</p>

  Line  26: 'Total' → 'Jumla'
           <p class="mb-0">Total Users</p>

  Line  65: 'Users' → 'Watumiaji'
           <!-- Users Table -->

  Line  69: 'Admin' → 'Msimamizi'
           <i class="fas fa-list me-2"></i>Admin Users

  Line  69: 'Users' → 'Watumiaji'
           <i class="fas fa-list me-2"></i>Admin Users

  Line  78: 'User' → 'Mtumiaji'
           <th>User</th>

  Line  82: 'Login' → 'Ingia'
           <th>Last Login</th>

  Line  84: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line 113: 'Admin' → 'Msimamizi'
           <i class="fas fa-crown me-1"></i>Super Admin

  Line 115: 'Admin' → 'Msimamizi'
           <i class="fas fa-user me-1"></i>Admin

  Line 121: 'Active' → 'Hai'
           {{ 'Active' if user.is_active else 'Disabled' }}

  Line 146: 'Edit' → 'Hariri'
           <i class="fas fa-edit"></i> Edit

  Line 169: 'Users' → 'Watumiaji'
           <h4 class="text-muted">No Users Found</h4>

  Line 172: 'User' → 'Mtumiaji'
           <i class="fas fa-user-plus me-2"></i>Add First User

  Line 172: 'Add' → 'Ongeza'
           <i class="fas fa-user-plus me-2"></i>Add First User

  Line 185: 'User' → 'Mtumiaji'
           <i class="fas fa-info-circle me-2"></i>User Roles

  Line 190: 'Admin' → 'Msimamizi'
           <strong><i class="fas fa-crown text-danger me-2"></i>Super Admin:</strong>

  Line 193: 'Manage' → 'Simamia'
           <li>Manage users and companies</li>

  Line 199: 'Admin' → 'Msimamizi'
           <strong><i class="fas fa-user text-primary me-2"></i>Admin:</strong>

  Line 201: 'Manage' → 'Simamia'
           <li>Manage payments and invoices</li>

  Line 202: 'View' → 'Ona'
           <li>View company information</li>

  Line 224: 'Login' → 'Ingia'
           <li><strong>Login Monitoring:</strong> Monitor last login times regularly</li>

📄 templates\advanced_landing_builder.html
----------------------------------------
  Line 301: 'Email' → 'Barua pepe'
           <button class="btn btn-light btn-sm flex-fill" onclick="shareEmail()" title="Sha...

  Line 302: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope text-primary"></i> Email

  Line 324: 'Save' → 'Hifadhi'
           <button class="btn btn-success flex-fill" onclick="publishLandingPage()" title="...

  Line 381: 'Save' → 'Hifadhi'
           <!-- Save Button -->

  Line 384: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-2"></i>Save Landing Page

  Line 415: 'About' → 'Kuhusu'
           <!-- About Section -->

  Line 418: 'About' → 'Kuhusu'
           <h2>About Our Services</h2>

  Line 423: 'Payment' → 'Malipo'
           <!-- Payment Methods Section -->

  Line 460: 'Contact' → 'Wasiliana'
           <!-- Contact Section -->

  Line 463: 'Contact' → 'Wasiliana'
           <h2>Contact Information</h2>

  Line 643: 'Payment' → 'Malipo'
           const subject = `Payment Page - ${companyName}`;

  Line 663: 'Save' → 'Hifadhi'
           // Save current customizations

  Line 689: 'Create' → 'Unda'
           // Create toast notification

  Line 716: 'Add' → 'Ongeza'
           // Add CSS for toast animation

📄 templates\analytics_dashboard.html
----------------------------------------
  Line   3: 'Dashboard' → 'Dashibodi'
           {% block title %}Analytics Dashboard - {{ company.company_name }}{% endblock %}

  Line   3: 'Analytics' → 'Uchambuzi'
           {% block title %}Analytics Dashboard - {{ company.company_name }}{% endblock %}

  Line   7: 'Performance' → 'Utendaji'
           /* Performance optimizations for charts */

  Line  67: 'Dashboard' → 'Dashibodi'
           Analytics Dashboard

  Line  67: 'Analytics' → 'Uchambuzi'
           Analytics Dashboard

  Line 102: 'Metrics' → 'Vipimo'
           <!-- Key Metrics Cards -->

  Line 169: 'Payment' → 'Malipo'
           <!-- Payment Methods Breakdown -->

  Line 173: 'Payment' → 'Malipo'
           <i class="fas fa-chart-pie me-2"></i>Payment Methods

  Line 185: 'Payment' → 'Malipo'
           <!-- Payment Methods Table -->

  Line 189: 'Payment' → 'Malipo'
           <i class="fas fa-mobile-alt me-2"></i>Payment Method Details

  Line 196: 'Count' → 'Idadi'
           <th>Count</th>

  Line 218: 'Status' → 'Hali'
           <!-- Usage Status -->

  Line 247: 'Fee' → 'Ada'
           <div class="small text-muted">Transaction Fee</div>

  Line 251: 'Month' → 'Mwezi'
           <div class="small text-muted">This Month</div>

  Line 274: 'Performance' → 'Utendaji'
           <!-- Chart.js with Performance Optimizations -->

  Line 277: 'Performance' → 'Utendaji'
           // Performance optimized chart initialization

  Line 286: 'Performance' → 'Utendaji'
           // Transaction Trend Chart with Performance Optimizations

  Line 306: 'Create' → 'Unda'
           // Create chart with performance optimizations

  Line 310: 'Date' → 'Tarehe'
           labels: dates.map(date => new Date(date).toLocaleDateString()),

  Line 336: 'Performance' → 'Utendaji'
           // Performance optimizations

  Line 394: 'Performance' → 'Utendaji'
           // Performance settings

  Line 407: 'Payment' → 'Malipo'
           // Payment Methods Chart with Performance Optimizations

  Line 407: 'Performance' → 'Utendaji'
           // Payment Methods Chart with Performance Optimizations

  Line 424: 'Create' → 'Unda'
           // Create optimized doughnut chart

  Line 433: 'Success' → 'Mafanikio'
           '#10b981', // Success green

  Line 434: 'Warning' → 'Onyo'
           '#f59e0b', // Warning amber

  Line 436: 'Info' → 'Taarifa'
           '#3b82f6', // Info blue

  Line 448: 'Performance' → 'Utendaji'
           // Performance optimizations

  Line 481: 'Performance' → 'Utendaji'
           // Performance settings

  Line 495: 'Error' → 'Hitilafu'
           // Error handling for chart initialization

📄 templates\api_management.html
----------------------------------------
  Line  23: 'Manage' → 'Simamia'
           Manage API access for {{ company.company_name }}

  Line  59: 'Payment' → 'Malipo'
           <li><i class="fas fa-check text-success me-1"></i>Payment Processing</li>

  Line  60: 'Analytics' → 'Uchambuzi'
           <li><i class="fas fa-check text-success me-1"></i>Analytics API</li>

  Line  71: 'Payment' → 'Malipo'
           <li><i class="fas fa-check text-success me-1"></i>Payment Processing</li>

  Line  72: 'Analytics' → 'Uchambuzi'
           <li><i class="fas fa-check text-success me-1"></i>Analytics API</li>

  Line  92: 'Status' → 'Hali'
           <!-- API Status Card -->

  Line  96: 'Status' → 'Hali'
           <i class="fas fa-signal me-2"></i>API Status

  Line 140: 'Continue' → 'Endelea'
           onclick="return confirm('This will generate new credentials and invalidate the o...

  Line 195: 'Documentation' → 'Nyaraka'
           <!-- API Documentation -->

  Line 200: 'Documentation' → 'Nyaraka'
           <i class="fas fa-book me-2"></i>API Documentation

  Line 213: 'All' → 'Yote'
           <p class="small text-muted">All API endpoints are relative to:</p>

  Line 233: 'Create' → 'Unda'
           <td>Create a new payment confirmation</td>

  Line 252: 'View' → 'Ona'
           <i class="fas fa-external-link-alt me-1"></i>View Full Documentation

  Line 252: 'Documentation' → 'Nyaraka'
           <i class="fas fa-external-link-alt me-1"></i>View Full Documentation

📄 templates\base.html
----------------------------------------
  Line  10: 'Payment' → 'Malipo'
           Payment Confirmation System

  Line 104: 'Payment' → 'Malipo'
           &copy; 2024 EXLIPA Payment Solutions Ltd. Haki zote zimehifadhiwa.

  Line 106: 'Payment' → 'Malipo'
           &copy; 2024 EXLIPA Payment Solutions Ltd. All rights reserved.

  Line 106: 'All' → 'Yote'
           &copy; 2024 EXLIPA Payment Solutions Ltd. All rights reserved.

📄 templates\bulk_operations.html
----------------------------------------
  Line  52: 'Update' → 'Sasisha'
           Update status of multiple payments at once

  Line  73: 'Export' → 'Hamisha'
           Export payment or invoice data to external systems

  Line  94: 'Import' → 'Leta'
           Import data from external systems to EXLIPA

  Line 107: 'Payments' → 'Malipo'
           <!-- Bulk Payments Panel -->

  Line 131: 'Payment' → 'Malipo'
           <!-- Payment list will be loaded here -->

  Line 155: 'Confirmed' → 'Imethibitishwa'
           <option value="Confirmed">

  Line 158: 'Pending' → 'Inasubiri'
           <option value="Pending">

  Line 161: 'Rejected' → 'Imekataliwa'
           <option value="Rejected">

  Line 189: 'Export' → 'Hamisha'
           <!-- Data Export Panel -->

  Line 277: 'Import' → 'Leta'
           <!-- Data Import Panel -->

  Line 291: 'Download' → 'Pakua'
           Download CSV template to see the correct data format

  Line 413: 'Payment' → 'Malipo'
           Payment #001 - John Doe - TSh 25,000

  Line 419: 'Payment' → 'Malipo'
           Payment #002 - Jane Smith - TSh 50,000

  Line 425: 'Payment' → 'Malipo'
           Payment #003 - Bob Johnson - TSh 15,000

  Line 528: 'Create' → 'Unda'
           // Create and download template

📄 templates\check_payment_status.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Check Payment Status - EXLIPA

  Line   7: 'Status' → 'Hali'
           Check Payment Status - EXLIPA

  Line 159: 'Back' → 'Rudi'
           <!-- Back Link -->

  Line 162: 'Back' → 'Rudi'
           <span>Back to Home</span>

  Line 162: 'Home' → 'Nyumbani'
           <span>Back to Home</span>

  Line 165: 'Status' → 'Hali'
           <!-- Status Check Card -->

  Line 170: 'Payment' → 'Malipo'
           Check Payment Status

  Line 170: 'Status' → 'Hali'
           Check Payment Status

  Line 175: 'Info' → 'Taarifa'
           <!-- Info Box -->

  Line 181: 'Status' → 'Hali'
           <!-- Status Check Form -->

  Line 207: 'Name' → 'Jina'
           <i class="fas fa-user me-2"></i>Your Name

  Line 222: 'Help' → 'Msaada'
           <!-- Additional Help -->

  Line 226: 'Contact' → 'Wasiliana'
           Having trouble? Contact support at <strong><EMAIL></strong>

📄 templates\clean_pricing.html
----------------------------------------
  Line 282: 'Features' → 'Vipengele'
           <!-- Key Features -->

  Line 322: 'Analytics' → 'Uchambuzi'
           Analytics dashboard

  Line 400: 'Cancel' → 'Ghairi'
           No contracts • Cancel anytime

  Line 408: 'FAQ' → 'Maswali Yanayoulizwa Mara kwa Mara'
           <!-- Simple FAQ -->

  Line 488: 'Cancel' → 'Ghairi'
           No contracts required. Cancel anytime with no penalties.

  Line 497: 'Contact' → 'Wasiliana'
           <!-- Contact -->

  Line 518: 'Contact' → 'Wasiliana'
           Contact Us

📄 templates\company_dashboard.html
----------------------------------------
  Line   7: 'Company' → 'Kampuni'
           Company Dashboard - EXLIPA

  Line   7: 'Dashboard' → 'Dashibodi'
           Company Dashboard - EXLIPA

  Line  19: 'Welcome' → 'Karibu'
           Welcome, {{ current_user.full_name or current_user.username }}

  Line  26: 'Company' → 'Kampuni'
           Company: <strong>{{ company.company_name }}</strong>

  Line  30: 'Active' → 'Hai'
           {% if company.subscription_status == 'Active' %}bg-success

  Line  61: 'Team' → 'Timu'
           Team

  Line  78: 'Pending' → 'Inasubiri'
           Activation Pending

  Line  82: 'Locked' → 'Imefungwa'
           <span class="text-danger">Locked</span>

  Line  95: 'Actions' → 'Vitendo'
           Quick Actions

  Line 104: 'Invoices' → 'Ankara'
           View Invoices

  Line 104: 'View' → 'Ona'
           View Invoices

  Line 112: 'Payments' → 'Malipo'
           View Payments

  Line 112: 'View' → 'Ona'
           View Payments

  Line 120: 'Manage' → 'Simamia'
           Manage Team

  Line 120: 'Team' → 'Timu'
           Manage Team

  Line 128: 'Edit' → 'Hariri'
           Edit Profile

  Line 128: 'Profile' → 'Wasifu'
           Edit Profile

  Line 147: 'Locked' → 'Imefungwa'
           POS Locked

  Line 188: 'Company' → 'Kampuni'
           Company Info

  Line 188: 'Info' → 'Taarifa'
           Company Info

  Line 225: 'Billing' → 'Malipo'
           Billing & Invoicing

  Line 235: 'Payment' → 'Malipo'
           Payment Tracking

  Line 245: 'Team' → 'Timu'
           Team Management

📄 templates\company_invoices.html
----------------------------------------
  Line   6: 'Invoices' → 'Ankara'
           My Invoices - Exlipa

  Line  11: 'Invoices' → 'Ankara'
           <h2 class="mb-4"><i class="fas fa-file-invoice me-2 text-primary"></i>My Invoice...

  Line  17: 'Invoice' → 'Ankara'
           <th>Invoice #</th>

  Line  43: 'Dashboard' → 'Dashibodi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

  Line  43: 'Back' → 'Rudi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

📄 templates\company_landing.html
----------------------------------------
  Line   7: 'Settings' → 'Mipangilio'
           Landing Page Settings - EXLIPA

  Line 107: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line 107: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line 118: 'Payment' → 'Malipo'
           <i class="fas fa-share-alt me-2 text-primary"></i>Share Payment Page

  Line 156: 'Status' → 'Hali'
           <!-- Page Status -->

  Line 179: 'Company' → 'Kampuni'
           placeholder="Your Company - Pay Online">

  Line 184: 'Description' → 'Maelezo'
           <label for="landing_page_description" class="form-label">Page Description</label...

  Line 191: 'Message' → 'Ujumbe'
           <label for="custom_message" class="form-label">Custom Message</label>

  Line 197: 'Settings' → 'Mipangilio'
           <!-- Current Settings Display -->

  Line 208: 'Edit' → 'Hariri'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 208: 'Profile' → 'Wasifu'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 208: 'Settings' → 'Mipangilio'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 218: 'Edit' → 'Hariri'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 218: 'Profile' → 'Wasifu'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 218: 'Settings' → 'Mipangilio'
           <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a...

  Line 223: 'Payment' → 'Malipo'
           <!-- Payment Methods Display -->

  Line 264: 'Add' → 'Ongeza'
           <a href="/company-profile">Add payment methods</a> to enable customer payments.

  Line 268: 'Submit' → 'Wasilisha'
           <!-- Submit Button -->

  Line 272: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-1"></i>Save Landing Page

  Line 287: 'Save' → 'Hifadhi'
           console.log('Save button found:', !!saveButton);

  Line 299: 'Save' → 'Hifadhi'
           console.log('Save button clicked!');

  Line 353: 'Payment' → 'Malipo'
           <button class="btn btn-success btn-sm" disabled>Confirm Payment</button>

  Line 353: 'Confirm' → 'Thibitisha'
           <button class="btn btn-success btn-sm" disabled>Confirm Payment</button>

  Line 460: 'Payment' → 'Malipo'
           const subject = `Payment Page - ${companyName}`;

  Line 497: 'Add' → 'Ongeza'
           // Add CSS for simple toast animation

📄 templates\company_login.html
----------------------------------------
  Line   7: 'Login' → 'Ingia'
           Login - Exlipa

  Line  17: 'Login' → 'Ingia'
           <i class="fas fa-user me-2"></i>Login

  Line  23: 'Username' → 'Jina la Mtumiaji'
           <label for="username" class="form-label">Username</label>

  Line  32: 'Login' → 'Ingia'
           <i class="fas fa-sign-in-alt me-1"></i>Login

  Line  39: 'Forgot' → 'Umesahau'
           <a href="{{ url_for('reset_request') }}" class="text-decoration-none">Forgot pas...

  Line  44: 'Payment' → 'Malipo'
           <a href="{{ url_for('pricing') }}" class="btn btn-link p-0">View Packages & Paym...

  Line  44: 'View' → 'Ona'
           <a href="{{ url_for('pricing') }}" class="btn btn-link p-0">View Packages & Paym...

  Line  46: 'Company' → 'Kampuni'
           <a href="{{ url_for('register') }}" class="btn btn-outline-primary btn-sm mt-2">...

📄 templates\company_payments.html
----------------------------------------
  Line   6: 'Payments' → 'Malipo'
           My Payments - Exlipa

  Line  11: 'Payments' → 'Malipo'
           <h2 class="mb-4"><i class="fas fa-credit-card me-2 text-success"></i>My Payments...

  Line  32: 'Confirmed' → 'Imethibitishwa'
           <span class="badge {% if payment.status == 'Confirmed' %}bg-success{% elif payme...

  Line  32: 'Pending' → 'Inasubiri'
           <span class="badge {% if payment.status == 'Confirmed' %}bg-success{% elif payme...

  Line  43: 'Dashboard' → 'Dashibodi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

  Line  43: 'Back' → 'Rudi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

📄 templates\company_profile.html
----------------------------------------
  Line   7: 'Company' → 'Kampuni'
           Company Profile - EXLIPA

  Line   7: 'Profile' → 'Wasifu'
           Company Profile - EXLIPA

  Line  18: 'Company' → 'Kampuni'
           <h2 class="mb-1"><i class="fas fa-building me-2 text-primary"></i>Company Profil...

  Line  18: 'Profile' → 'Wasifu'
           <h2 class="mb-1"><i class="fas fa-building me-2 text-primary"></i>Company Profil...

  Line  19: 'Update' → 'Sasisha'
           <p class="text-muted mb-0">Update your company information and payment details</...

  Line  24: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  24: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  28: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  28: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  34: 'Profile' → 'Wasifu'
           <!-- Profile Form -->

  Line  39: 'Company' → 'Kampuni'
           <h5 class="fw-bold mb-3"><i class="fas fa-info-circle me-2 text-info"></i>Compan...

  Line  45: 'Company' → 'Kampuni'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  45: 'Name' → 'Jina'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  53: 'Phone' → 'Simu'
           <label for="company_phone" class="form-label">Phone Number</label>

  Line  53: 'Number' → 'Nambari'
           <label for="company_phone" class="form-label">Phone Number</label>

  Line  67: 'Company' → 'Kampuni'
           <label for="company_website" class="form-label">Company Website</label>

  Line 101: 'Payment' → 'Malipo'
           <!-- Payment Information -->

  Line 109: 'Number' → 'Nambari'
           <label for="mpesa_till" class="form-label">M-Pesa Till Number</label>

  Line 140: 'Submit' → 'Wasilisha'
           <!-- Submit Button -->

  Line 144: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-1"></i>Save Changes

  Line 151: 'Info' → 'Taarifa'
           <!-- Info Panel -->

  Line 154: 'Profile' → 'Wasifu'
           <h5 class="fw-bold mb-3"><i class="fas fa-info-circle me-2 text-info"></i>Profil...

  Line 159: 'Contact' → 'Wasiliana'
           <small class="text-muted">Contact admin to change email address</small>

  Line 163: 'Status' → 'Hali'
           <label class="form-label fw-bold">Subscription Status</label>

  Line 164: 'Active' → 'Hai'
           <span class="badge bg-{{ 'success' if company.subscription_status == 'Active' el...

  Line 170: 'Billing' → 'Malipo'
           <label class="form-label fw-bold">Billing Tier</label>

  Line 184: 'Help' → 'Msaada'
           <h6 class="fw-bold mb-2">Need Help?</h6>

  Line 186: 'Contact' → 'Wasiliana'
           Contact our support team at <strong><EMAIL></strong> if you need as...

📄 templates\comprehensive_pricing.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           EXLIPA Pricing Plans - Complete Payment Solutions for Tanzania

  Line   7: 'Complete' → 'Kamili'
           EXLIPA Pricing Plans - Complete Payment Solutions for Tanzania

  Line 329: 'Save' → 'Hifadhi'
           <div class="annual-savings">Save TSh {{ "{:,}".format((tier.monthly_fee * 2)|int...

  Line 332: 'Features' → 'Vipengele'
           <!-- Features -->

  Line 334: 'Features' → 'Vipengele'
           <!-- Transaction Features -->

  Line 336: 'Features' → 'Vipengele'
           <h6><i class="fas fa-exchange-alt"></i> Transaction Features</h6>

  Line 363: 'Features' → 'Vipengele'
           <!-- Core Features -->

  Line 365: 'Features' → 'Vipengele'
           <h6><i class="fas fa-cogs"></i> Core Features</h6>

  Line 386: 'Features' → 'Vipengele'
           <!-- Advanced Features -->

  Line 388: 'Features' → 'Vipengele'
           <h6><i class="fas fa-star"></i> Advanced Features</h6>

  Line 393: 'Analytics' → 'Uchambuzi'
           <span>Analytics dashboard</span>

  Line 396: 'Analytics' → 'Uchambuzi'
           <span>Analytics dashboard</span>

  Line 429: 'Support' → 'Msaada'
           <!-- Support & Services -->

  Line 431: 'Support' → 'Msaada'
           <h6><i class="fas fa-headset"></i> Support & Services</h6>

  Line 435: 'Email' → 'Barua pepe'
           <span>Email support</span>

  Line 474: 'Cancel' → 'Ghairi'
           <small class="text-muted">No long-term contracts • Cancel anytime</small>

  Line 485: 'Complete' → 'Kamili'
           <h2>Complete Feature Comparison</h2>

  Line 496: 'Payment' → 'Malipo'
           <!-- Payment Processing -->

  Line 497: 'Fee' → 'Ada'
           <div class="comparison-cell feature-name">Transaction Fee</div>

  Line 507: 'Amount' → 'Kiasi'
           <div class="comparison-cell feature-name">Max Transaction Amount</div>

  Line 512: 'Payment' → 'Malipo'
           <!-- Payment Methods -->

  Line 554: 'Analytics' → 'Uchambuzi'
           <!-- Analytics & Reporting -->

  Line 555: 'Analytics' → 'Uchambuzi'
           <div class="comparison-cell feature-name">Basic Analytics</div>

  Line 560: 'Analytics' → 'Uchambuzi'
           <div class="comparison-cell feature-name">Advanced Analytics</div>

  Line 565: 'Reports' → 'Ripoti'
           <div class="comparison-cell feature-name">Custom Reports</div>

  Line 576: 'Support' → 'Msaada'
           <div class="comparison-cell feature-name">Webhook Support</div>

  Line 586: 'Support' → 'Msaada'
           <!-- Support -->

  Line 587: 'Email' → 'Barua pepe'
           <div class="comparison-cell feature-name">Email Support</div>

  Line 587: 'Support' → 'Msaada'
           <div class="comparison-cell feature-name">Email Support</div>

  Line 592: 'Phone' → 'Simu'
           <div class="comparison-cell feature-name">Priority Phone Support</div>

  Line 592: 'Support' → 'Msaada'
           <div class="comparison-cell feature-name">Priority Phone Support</div>

  Line 602: 'Features' → 'Vipengele'
           <!-- Advanced Features -->

  Line 603: 'Support' → 'Msaada'
           <div class="comparison-cell feature-name">Multi-location Support</div>

  Line 620: 'FAQ' → 'Maswali Yanayoulizwa Mara kwa Mara'
           <!-- FAQ Section -->

  Line 666: 'Contact' → 'Wasiliana'
           <!-- Contact Section -->

  Line 668: 'Help' → 'Msaada'
           <h3>Need Help Choosing the Right Plan?</h3>

  Line 672: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-2"></i>Email Us

📄 templates\confirmation_success.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Payment Confirmation Submitted - Payment System

  Line   7: 'Payment' → 'Malipo'
           Payment Confirmation Submitted - Payment System

  Line  18: 'Payment' → 'Malipo'
           Payment Confirmation Submitted!

  Line  23: 'Number' → 'Nambari'
           <h5><i class="fas fa-receipt me-2"></i>Your Reference Number</h5>

  Line  34: 'Next' → 'Ifuatayo'
           <h6>What's Next?</h6>

  Line  43: 'Time' → 'Muda'
           <h6>Verification Time</h6>

  Line  53: 'Save' → 'Hifadhi'
           <li><i class="fas fa-save text-primary me-2"></i>Save your reference number: <st...

  Line  54: 'Contact' → 'Wasiliana'
           <li><i class="fas fa-phone text-success me-2"></i>Contact us if you need assista...

  Line  61: 'Back' → 'Rudi'
           <i class="fas fa-home me-1"></i>Back to Home

  Line  61: 'Home' → 'Nyumbani'
           <i class="fas fa-home me-1"></i>Back to Home

  Line  64: 'Payment' → 'Malipo'
           <i class="fas fa-plus me-1"></i>Submit Another Payment

  Line  64: 'Submit' → 'Wasilisha'
           <i class="fas fa-plus me-1"></i>Submit Another Payment

  Line  72: 'Help' → 'Msaada'
           <h6 class="text-muted">Need Help?</h6>

  Line  75: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-1"></i> Email: <EMAIL><br>

  Line  87: 'Complete' → 'Kamili'
           <i class="fas fa-user-plus me-1"></i>Complete Registration

  Line  93: 'Payment' → 'Malipo'
           <h6><i class="fas fa-hourglass-half me-2"></i>Awaiting Payment Verification</h6>

  Line  97: 'Payment' → 'Malipo'
           <i class="fas fa-search me-1"></i>Check Payment Status

  Line  97: 'Status' → 'Hali'
           <i class="fas fa-search me-1"></i>Check Payment Status

  Line  99: 'Payment' → 'Malipo'
           <a href="mailto:<EMAIL>?subject=Payment%20Verification%20-%20{{ ref...

  Line 100: 'Contact' → 'Wasiliana'
           <i class="fas fa-envelope me-1"></i>Contact Support

  Line 100: 'Support' → 'Msaada'
           <i class="fas fa-envelope me-1"></i>Contact Support

📄 templates\confirm_payment.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Confirm Payment - Payment System

  Line   7: 'Payment' → 'Malipo'
           Confirm Payment - Payment System

  Line   7: 'Confirm' → 'Thibitisha'
           Confirm Payment - Payment System

  Line  21: 'Payment' → 'Malipo'
           Confirm Your Payment

  Line  21: 'Confirm' → 'Thibitisha'
           Confirm Your Payment

  Line  42: 'Name' → 'Jina'
           <i class="fas fa-user me-1"></i>Your Full Name *

  Line  51: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-1"></i>Your Email Address *

  Line  64: 'Name' → 'Jina'
           <i class="fas fa-user-check me-1"></i>Mobile Money Registered Name *

  Line  74: 'Amount' → 'Kiasi'
           <i class="fas fa-money-bill me-1"></i>Amount Paid (TZS) *

  Line  84: 'Payment' → 'Malipo'
           <i class="fas fa-mobile-alt me-1"></i>Payment Method Used *

  Line 147: 'Payment' → 'Malipo'
           <a href="{{ url_for('payment_instructions') }}">View Payment Instructions</a>

  Line 147: 'View' → 'Ona'
           <a href="{{ url_for('payment_instructions') }}">View Payment Instructions</a>

📄 templates\create_invoice.html
----------------------------------------
  Line   7: 'Invoice' → 'Ankara'
           Create Invoice - Payment System

  Line   7: 'Payment' → 'Malipo'
           Create Invoice - Payment System

  Line   7: 'Create' → 'Unda'
           Create Invoice - Payment System

  Line  17: 'Invoice' → 'Ankara'
           <i class="fas fa-file-invoice me-2"></i>Create New Invoice

  Line  17: 'Create' → 'Unda'
           <i class="fas fa-file-invoice me-2"></i>Create New Invoice

  Line  26: 'Customer' → 'Mteja'
           <i class="fas fa-user me-1"></i>Customer Name *

  Line  26: 'Name' → 'Jina'
           <i class="fas fa-user me-1"></i>Customer Name *

  Line  35: 'Customer' → 'Mteja'
           <i class="fas fa-envelope me-1"></i>Customer Email

  Line  35: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-1"></i>Customer Email

  Line  47: 'Customer' → 'Mteja'
           <i class="fas fa-phone me-1"></i>Customer Phone

  Line  47: 'Phone' → 'Simu'
           <i class="fas fa-phone me-1"></i>Customer Phone

  Line  56: 'Amount' → 'Kiasi'
           <i class="fas fa-money-bill me-1"></i>Amount (TZS) *

  Line  66: 'Description' → 'Maelezo'
           <i class="fas fa-clipboard-list me-1"></i>Service/Product Description *

  Line  74: 'Payment' → 'Malipo'
           <i class="fas fa-calendar me-1"></i>Payment Due In (Days)

  Line  79: 'Week' → 'Wiki'
           <option value="7" selected>7 Days (1 Week)</option>

  Line  81: 'Month' → 'Mwezi'
           <option value="30">30 Days (1 Month)</option>

  Line  87: 'Invoice' → 'Ankara'
           <i class="fas fa-plus me-2"></i>Create Invoice

  Line  87: 'Create' → 'Unda'
           <i class="fas fa-plus me-2"></i>Create Invoice

  Line 100: 'Create' → 'Unda'
           <li><i class="fas fa-check text-success me-2"></i>Create invoice with customer d...

  Line 102: 'Customer' → 'Mteja'
           <li><i class="fas fa-mobile text-warning me-2"></i>Customer makes payment via mo...

  Line 103: 'Customer' → 'Mteja'
           <li><i class="fas fa-check-circle text-primary me-2"></i>Customer confirms payme...

📄 templates\free_signup.html
----------------------------------------
  Line 126: 'Features' → 'Vipengele'
           <!-- Free Plan Features -->

  Line 305: 'Login' → 'Ingia'
           Already have an account? <a href="{{ url_for('user_admin_login') }}" class="text...

📄 templates\free_signup_simple.html
----------------------------------------
  Line  65: 'Payment' → 'Malipo'
           100% FREE - No Payment Required!

  Line  67: 'Create' → 'Unda'
           <p class="lead">Create your free account and start accepting payments immediatel...

  Line  94: 'Name' → 'Jina'
           <i class="fas fa-user me-2"></i>Full Name *

  Line 102: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-2"></i>Email Address *

  Line 110: 'Company' → 'Kampuni'
           <i class="fas fa-building me-2"></i>Company Name *

  Line 110: 'Name' → 'Jina'
           <i class="fas fa-building me-2"></i>Company Name *

  Line 118: 'Phone' → 'Simu'
           <i class="fas fa-phone me-2"></i>Phone Number

  Line 118: 'Number' → 'Nambari'
           <i class="fas fa-phone me-2"></i>Phone Number

  Line 126: 'Password' → 'Nywila'
           <i class="fas fa-lock me-2"></i>Password *

  Line 129: 'Create' → 'Unda'
           placeholder="Create a secure password" minlength="6">

  Line 135: 'Confirm' → 'Thibitisha'
           <i class="fas fa-lock me-2"></i>Confirm Password *

  Line 135: 'Password' → 'Nywila'
           <i class="fas fa-lock me-2"></i>Confirm Password *

  Line 138: 'Confirm' → 'Thibitisha'
           placeholder="Confirm your password" minlength="6">

  Line 169: 'Create' → 'Unda'
           Create My Free Account

  Line 176: 'Login' → 'Ingia'
           <p>Already have an account? <a href="/user-admin-login" class="text-warning">Log...

  Line 183: 'Password' → 'Nywila'
           // Password confirmation validation

  Line 198: 'Add' → 'Ongeza'
           // Add event listeners for password validation

📄 templates\free_signup_success.html
----------------------------------------
  Line  11: 'Success' → 'Mafanikio'
           <!-- Success Message -->

  Line  11: 'Message' → 'Ujumbe'
           <!-- Success Message -->

  Line  72: 'Next' → 'Ifuatayo'
           <!-- What's Next -->

  Line  99: 'Complete' → 'Kamili'
           Complete your company setup

  Line 235: 'Add' → 'Ongeza'
           // Add confetti animation CSS

📄 templates\index.html
----------------------------------------
  Line 587: 'Payment' → 'Malipo'
           Modern <span class="payment-highlight">Payment</span> System for Businesses

  Line 590: 'Payment' → 'Malipo'
           Secure & Reliable Payment Solutions for Tanzanian Companies

  Line 590: 'Companies' → 'Makampuni'
           Secure & Reliable Payment Solutions for Tanzanian Companies

  Line 611: 'Payment' → 'Malipo'
           Check Payment Status

  Line 611: 'Status' → 'Hali'
           Check Payment Status

  Line 660: 'Payment' → 'Malipo'
           💳 Payment Gateway

  Line 689: 'Complete' → 'Kamili'
           Complete point-of-sale system with inventory management, automated billing and s...

  Line 704: 'Company' → 'Kampuni'
           🏢 Company Management

  Line 711: 'Manage' → 'Simamia'
           Manage multiple companies, teams, and customer payment pages from one dashboard.

  Line 724: 'Analytics' → 'Uchambuzi'
           Business Analytics

  Line 771: 'Add' → 'Ongeza'
           Add your logo, colors and unique design to your payment pages and receipts.

  Line 863: 'Email' → 'Barua pepe'
           Email support

  Line 917: 'All' → 'Yote'
           All Starter features

  Line 992: 'All' → 'Yote'
           All Business features

  Line 1046: 'Every' → 'Kila'
           EXLIPA is Tanzania's first payment platform using SMS verification for enhanced ...

  Line 1126: 'Payments' → 'Malipo'
           Instant Payments

  Line 1178: 'Support' → 'Msaada'
           24/7 Support

  Line 1204: 'Reports' → 'Ripoti'
           Detailed Reports

  Line 1228: 'Companies' → 'Makampuni'
           Trusted by Leading Companies

  Line 1303: 'Customer' → 'Mteja'
           Customer support

  Line 1342: 'View' → 'Ona'
           View Packages

  Line 1349: 'Payment' → 'Malipo'
           Check Payment

📄 templates\index_multilingual.html
----------------------------------------
  Line  17: 'Welcome' → 'Karibu'
           <h1 class="display-2 fw-bold mb-3 elegant-gradient-text animate-fadein">Welcome ...

  Line  19: 'Payment' → 'Malipo'
           Modern Payment Gateway for Tanzanian Businesses

  Line  32: 'View' → 'Ona'
           View Packages & Pricing

  Line  40: 'Payment' → 'Malipo'
           Check Payment Status

  Line  40: 'Status' → 'Hali'
           Check Payment Status

  Line  55: 'Features' → 'Vipengele'
           <!-- Features Grid -->

  Line 104: 'Team' → 'Timu'
           Team Management

  Line 124: 'Analytics' → 'Uchambuzi'
           Business Analytics

  Line 190: 'Today' → 'Leo'
           Start Today

📄 templates\master_admin_login.html
----------------------------------------
  Line   7: 'Master' → 'Mkuu'
           Master Admin Login - EXLIPA

  Line   7: 'Admin' → 'Msimamizi'
           Master Admin Login - EXLIPA

  Line   7: 'Login' → 'Ingia'
           Master Admin Login - EXLIPA

  Line  19: 'Master' → 'Mkuu'
           Master Admin Login

  Line  19: 'Admin' → 'Msimamizi'
           Master Admin Login

  Line  19: 'Login' → 'Ingia'
           Master Admin Login

  Line  39: 'Username' → 'Jina la Mtumiaji'
           Username

  Line  54: 'Master' → 'Mkuu'
           Master Admin Login

  Line  54: 'Admin' → 'Msimamizi'
           Master Admin Login

  Line  54: 'Login' → 'Ingia'
           Master Admin Login

  Line  68: 'Master' → 'Mkuu'
           Master Admin access only

  Line  68: 'Admin' → 'Msimamizi'
           Master Admin access only

📄 templates\mobile_dashboard.html
----------------------------------------
  Line 217: 'User' → 'Mtumiaji'
           {{ current_user.username if current_user.is_authenticated else 'User' }}

  Line 232: 'Metrics' → 'Vipimo'
           <!-- Key Metrics Swipe Cards -->

  Line 267: 'Actions' → 'Vitendo'
           <!-- Quick Actions Grid -->

  Line 310: 'Payment' → 'Malipo'
           <div class="fw-bold">Payment Received</div>

  Line 319: 'Customer' → 'Mteja'
           <div class="fw-bold">New Customer</div>

  Line 358: 'Add' → 'Ongeza'
           // Add haptic feedback simulation

  Line 424: 'Notification' → 'Arifa'
           // Notification toggle

📄 templates\offline.html
----------------------------------------
  Line 266: 'Date' → 'Tarehe'
           const now = (new Date()).getTime();

📄 templates\onboarding.html
----------------------------------------
  Line  17: 'Welcome' → 'Karibu'
           <h3 class="mb-0">Welcome to Exlipa! Let's Set Up Your Company</h3>

  Line  17: 'Company' → 'Kampuni'
           <h3 class="mb-0">Welcome to Exlipa! Let's Set Up Your Company</h3>

  Line  46: 'Team' → 'Timu'
           <label for="team_emails" class="form-label">Invite Team Members (optional)</labe...

  Line  46: 'Members' → 'Wanachama'
           <label for="team_emails" class="form-label">Invite Team Members (optional)</labe...

  Line  48: 'Team' → 'Timu'
           <div class="form-text">Team members will receive an invite to join your company ...

  Line  50: 'Finish' → 'Maliza'
           <button type="submit" class="btn btn-info w-100">Finish Onboarding</button>

📄 templates\onboarding_wizard.html
----------------------------------------
  Line  26: 'Welcome' → 'Karibu'
           <!-- Step 1: Welcome -->

  Line  36: 'Welcome' → 'Karibu'
           Welcome to EXLIPA! 🎉

  Line  85: 'Company' → 'Kampuni'
           <!-- Step 2: Company Information -->

  Line 233: 'Complete' → 'Kamili'
           <!-- Step 4: Complete -->

  Line 339: 'Update' → 'Sasisha'
           // Update navigation buttons

📄 templates\payment_detail.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Payment Details - Payment System

  Line   7: 'Payment' → 'Malipo'
           Payment Details - Payment System

  Line  13: 'Payment' → 'Malipo'
           <h1><i class="fas fa-file-invoice me-2"></i>Payment Details</h1>

  Line  16: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to List

  Line  18: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line  32: 'Complete' → 'Kamili'
           <i class="fas fa-user-plus me-1"></i>Complete Registration

  Line  45: 'Confirmed' → 'Imethibitishwa'
           {% elif payment.status == 'Confirmed' %}

  Line  58: 'Payment' → 'Malipo'
           <!-- Payment Information -->

  Line  63: 'Payment' → 'Malipo'
           <i class="fas fa-info-circle me-2"></i>Payment Information

  Line  65: 'Pending' → 'Inasubiri'
           {% if payment.status == 'Pending' %}bg-warning text-dark

  Line  66: 'Confirmed' → 'Imethibitishwa'
           {% elif payment.status == 'Confirmed' %}bg-success

  Line  67: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}bg-danger

  Line  81: 'Customer' → 'Mteja'
           <dt class="col-sm-5">Customer Name:</dt>

  Line  81: 'Name' → 'Jina'
           <dt class="col-sm-5">Customer Name:</dt>

  Line  85: 'Customer' → 'Mteja'
           <dt class="col-sm-5">Customer Email:</dt>

  Line  85: 'Email' → 'Barua pepe'
           <dt class="col-sm-5">Customer Email:</dt>

  Line  93: 'Amount' → 'Kiasi'
           <dt class="col-sm-5">Amount:</dt>

  Line 115: 'Name' → 'Jina'
           <dt class="col-sm-5">Mobile Money Name:</dt>

  Line 118: 'Name' → 'Jina'
           <small class="text-muted d-block">Name registered with mobile money</small>

  Line 132: 'Admin' → 'Msimamizi'
           <dd class="col-sm-7">Admin User #{{ payment.processed_by }}</dd>

  Line 132: 'User' → 'Mtumiaji'
           <dd class="col-sm-7">Admin User #{{ payment.processed_by }}</dd>

  Line 140: 'Description' → 'Maelezo'
           <h6>Service/Product Description:</h6>

  Line 145: 'Pending' → 'Inasubiri'
           {% if payment.status == 'Pending' %}

  Line 182: 'Amount' → 'Kiasi'
           <label for="sms_amount" class="form-label">Amount in SMS (TZS)</label>

  Line 184: 'Amount' → 'Kiasi'
           placeholder="Amount from SMS" min="0" step="0.01" required>

  Line 192: 'Name' → 'Jina'
           <label for="sms_sender_name" class="form-label">Sender Name in SMS</label>

  Line 194: 'Name' → 'Jina'
           placeholder="Name from SMS" required>

  Line 210: 'Add' → 'Ongeza'
           rows="3" placeholder="Add any notes about the verification process..."></textare...

  Line 215: 'Payment' → 'Malipo'
           <i class="fas fa-check me-1"></i>Confirm Payment

  Line 215: 'Confirm' → 'Thibitisha'
           <i class="fas fa-check me-1"></i>Confirm Payment

  Line 218: 'Payment' → 'Malipo'
           <i class="fas fa-times me-1"></i>Reject Payment

  Line 246: 'Add' → 'Ongeza'
           placeholder="Add internal notes about this payment...">{{ payment.internal_notes...

  Line 249: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-1"></i>Save Notes

  Line 256: 'Actions' → 'Vitendo'
           <!-- Actions Panel -->

  Line 260: 'Actions' → 'Vitendo'
           <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>

  Line 263: 'Pending' → 'Inasubiri'
           {% if payment.status == 'Pending' %}

  Line 264: 'Payment' → 'Malipo'
           <!-- Confirm Payment -->

  Line 264: 'Confirm' → 'Thibitisha'
           <!-- Confirm Payment -->

  Line 269: 'Payment' → 'Malipo'
           <i class="fas fa-check me-2"></i>Confirm Payment

  Line 269: 'Confirm' → 'Thibitisha'
           <i class="fas fa-check me-2"></i>Confirm Payment

  Line 273: 'Payment' → 'Malipo'
           <!-- Reject Payment -->

  Line 283: 'Payment' → 'Malipo'
           <i class="fas fa-times me-2"></i>Reject Payment

  Line 287: 'Confirmed' → 'Imethibitishwa'
           {% elif payment.status == 'Confirmed' %}

  Line 304: 'Payment' → 'Malipo'
           <h6>Payment Processed</h6>

  Line 310: 'Download' → 'Pakua'
           <i class="fas fa-download me-2"></i>Download Receipt

  Line 314: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}

  Line 317: 'Payment' → 'Malipo'
           <h6>Payment Rejected</h6>

  Line 317: 'Rejected' → 'Imekataliwa'
           <h6>Payment Rejected</h6>

  Line 325: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status in ['Confirmed', 'Processed'] %}

  Line 336: 'Download' → 'Pakua'
           <i class="fas fa-download me-1"></i>Download

📄 templates\payment_instructions.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Payment Instructions - Payment System

  Line   7: 'Payment' → 'Malipo'
           Payment Instructions - Payment System

  Line  20: 'Invoice' → 'Ankara'
           <h5><i class="fas fa-file-invoice me-2"></i>Invoice Details</h5>

  Line  23: 'Invoice' → 'Ankara'
           <strong>Invoice Number:</strong> <code>{{ invoice.invoice_number }}</code><br>

  Line  23: 'Number' → 'Nambari'
           <strong>Invoice Number:</strong> <code>{{ invoice.invoice_number }}</code><br>

  Line  24: 'Customer' → 'Mteja'
           <strong>Customer:</strong> {{ invoice.customer_name }}<br>

  Line  25: 'Amount' → 'Kiasi'
           <strong>Amount to Pay:</strong> <span class="h5 text-success">TZS {{ "{:,.0f}"|f...

  Line  29: 'Date' → 'Tarehe'
           <strong>Due Date:</strong> {{ invoice.due_date.strftime('%d/%m/%Y') }}

  Line  37: 'Payment' → 'Malipo'
           <strong>Important:</strong> After making your payment, you will receive an SMS t...

  Line  38: 'Payment' → 'Malipo'
           Please save this Payment ID as you'll need it to confirm your payment on our web...

  Line  55: 'Number' → 'Nambari'
           <strong>Till Number:</strong> <span class="badge bg-success fs-6">123456</span>

  Line  58: 'Payment' → 'Malipo'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line  58: 'Name' → 'Jina'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line  71: 'Number' → 'Nambari'
           <div>Enter Till Number: <strong>123456</strong></div>

  Line  74: 'Amount' → 'Kiasi'
           <div>Enter Amount</div>

  Line  77: 'Confirm' → 'Thibitisha'
           <div>Confirm and enter your PIN</div>

  Line  97: 'Payment' → 'Malipo'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line  97: 'Name' → 'Jina'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line 110: 'Amount' → 'Kiasi'
           <div>Enter Amount</div>

  Line 113: 'Confirm' → 'Thibitisha'
           <div>Confirm and enter your PIN</div>

  Line 133: 'Payment' → 'Malipo'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line 133: 'Name' → 'Jina'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line 146: 'Amount' → 'Kiasi'
           <div>Enter Amount</div>

  Line 149: 'Confirm' → 'Thibitisha'
           <div>Confirm and enter your PIN</div>

  Line 173: 'Payment' → 'Malipo'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line 173: 'Name' → 'Jina'
           <strong>Business Name:</strong> Exlipa Payment Solutions Ltd

  Line 186: 'Amount' → 'Kiasi'
           <div>Enter Amount</div>

  Line 189: 'Add' → 'Ongeza'
           <div>Add invoice number as reference</div>

  Line 192: 'Confirm' → 'Thibitisha'
           <div>Confirm and enter your PIN</div>

  Line 208: 'Payment' → 'Malipo'
           <li><strong>Payment ID/Transaction ID:</strong> After payment, you'll receive an...

  Line 208: 'Payment' → 'Malipo'
           <li><strong>Payment ID/Transaction ID:</strong> After payment, you'll receive an...

  Line 209: 'Payment' → 'Malipo'
           <li><strong>Business Name Verification:</strong> Ensure the business name shown ...

  Line 209: 'Name' → 'Jina'
           <li><strong>Business Name Verification:</strong> Ensure the business name shown ...

  Line 210: 'Payments' → 'Malipo'
           <li><strong>QR Code Payments:</strong> For Tigo Scan to Pay, ensure you scan the...

  Line 211: 'Amount' → 'Kiasi'
           <li><strong>Amount Accuracy:</strong> Double-check the amount before confirming ...

  Line 219: 'Payment' → 'Malipo'
           <i class="fas fa-check me-2"></i>Confirm Your Payment

  Line 219: 'Confirm' → 'Thibitisha'
           <i class="fas fa-check me-2"></i>Confirm Your Payment

  Line 225: 'Help' → 'Msaada'
           <h6>Need Help?</h6>

  Line 228: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-1"></i> Email: <EMAIL>

📄 templates\payment_status_result.html
----------------------------------------
  Line   7: 'Payment' → 'Malipo'
           Payment Status - EXLIPA

  Line   7: 'Status' → 'Hali'
           Payment Status - EXLIPA

  Line 200: 'Back' → 'Rudi'
           <!-- Back Link -->

  Line 203: 'Payment' → 'Malipo'
           <span>Check Another Payment</span>

  Line 206: 'Payment' → 'Malipo'
           <!-- Payment Status Card -->

  Line 206: 'Status' → 'Hali'
           <!-- Payment Status Card -->

  Line 210: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 212: 'Pending' → 'Inasubiri'
           {% elif payment.status == 'Pending' %}

  Line 214: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}

  Line 222: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 223: 'Payment' → 'Malipo'
           Payment Confirmed

  Line 223: 'Confirmed' → 'Imethibitishwa'
           Payment Confirmed

  Line 224: 'Pending' → 'Inasubiri'
           {% elif payment.status == 'Pending' %}

  Line 225: 'Payment' → 'Malipo'
           Payment Pending Review

  Line 225: 'Pending' → 'Inasubiri'
           Payment Pending Review

  Line 226: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}

  Line 227: 'Payment' → 'Malipo'
           Payment Rejected

  Line 227: 'Rejected' → 'Imekataliwa'
           Payment Rejected

  Line 229: 'Payment' → 'Malipo'
           Payment Processed

  Line 234: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 236: 'Pending' → 'Inasubiri'
           {% elif payment.status == 'Pending' %}

  Line 238: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}

  Line 246: 'Payment' → 'Malipo'
           <!-- Payment Details -->

  Line 280: 'Status' → 'Hali'
           <!-- Status-specific Actions and Messages -->

  Line 280: 'Actions' → 'Vitendo'
           <!-- Status-specific Actions and Messages -->

  Line 281: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 290: 'Complete' → 'Kamili'
           <span>Complete Registration</span>

  Line 306: 'Pending' → 'Inasubiri'
           {% elif payment.status == 'Pending' %}

  Line 312: 'Rejected' → 'Imekataliwa'
           {% elif payment.status == 'Rejected' %}

  Line 315: 'Payment' → 'Malipo'
           <strong>Payment Issue.</strong>

  Line 326: 'All' → 'Yote'
           <strong>All done!</strong> Your payment has been fully processed and your accoun...

  Line 330: 'Actions' → 'Vitendo'
           <!-- Additional Actions -->

  Line 333: 'Back' → 'Rudi'
           <span>Back to Home</span>

  Line 333: 'Home' → 'Nyumbani'
           <span>Back to Home</span>

  Line 336: 'Support' → 'Msaada'
           <!-- Support Contact -->

  Line 336: 'Contact' → 'Wasiliana'
           <!-- Support Contact -->

  Line 340: 'Contact' → 'Wasiliana'
           Need help? Contact us at <strong><EMAIL></strong> or <strong>+255 1...

📄 templates\performance_dashboard.html
----------------------------------------
  Line  18: 'Dashboard' → 'Dashibodi'
           Performance Dashboard

  Line  18: 'Performance' → 'Utendaji'
           Performance Dashboard

  Line  39: 'Performance' → 'Utendaji'
           <!-- Performance Metrics -->

  Line  39: 'Metrics' → 'Vipimo'
           <!-- Performance Metrics -->

  Line  75: 'Status' → 'Hali'
           <!-- Performance Status -->

  Line  75: 'Performance' → 'Utendaji'
           <!-- Performance Status -->

  Line 106: 'Performance' → 'Utendaji'
           Performance is good but could be optimized

  Line 123: 'Performance' → 'Utendaji'
           <!-- Performance Tips -->

  Line 150: 'Add' → 'Ongeza'
           Add database indexes for common queries

  Line 184: 'Performance' → 'Utendaji'
           <!-- Real-time Performance -->

  Line 213: 'Actions' → 'Vitendo'
           <!-- Performance Actions -->

  Line 213: 'Performance' → 'Utendaji'
           <!-- Performance Actions -->

  Line 266: 'Update' → 'Sasisha'
           // Update colors based on usage

  Line 274: 'Performance' → 'Utendaji'
           // Performance actions

  Line 297: 'Date' → 'Tarehe'
           timestamp: new Date().toISOString(),

📄 templates\pos_dashboard.html
----------------------------------------
  Line  21: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back

  Line  60: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-1"></i>Add

  Line  71: 'Add' → 'Ongeza'
           <p class="text-muted">Add products to start selling</p>

  Line  73: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-1"></i>Add First Product

  Line  93: 'Customer' → 'Mteja'
           <label class="form-label">Customer (Optional)</label>

  Line  94: 'Customer' → 'Mteja'
           <input type="text" id="customer-name" class="form-control form-control-sm" place...

  Line  95: 'Phone' → 'Simu'
           <input type="text" id="customer-phone" class="form-control form-control-sm mt-1"...

  Line 117: 'Total' → 'Jumla'
           <span>Total:</span>

  Line 134: 'Complete' → 'Kamili'
           <i class="fas fa-credit-card me-1"></i>Complete Sale

  Line 143: 'Today' → 'Leo'
           <h6 class="mb-1">Today's Sales</h6>

  Line 151: 'Payment' → 'Malipo'
           <!-- Cash Payment Modal -->

  Line 156: 'Payment' → 'Malipo'
           <h5 class="modal-title"><i class="fas fa-money-bill-wave text-success me-2"></i>...

  Line 173: 'Total' → 'Jumla'
           <span class="text-muted">Total:</span>

  Line 180: 'Amount' → 'Kiasi'
           <label for="amount-received" class="form-label">Amount Received</label>

  Line 185: 'Change' → 'Badilisha'
           <label class="form-label">Change Due</label>

  Line 190: 'Amount' → 'Kiasi'
           Amount received is less than total due

  Line 198: 'Complete' → 'Kamili'
           <i class="fas fa-check me-2"></i>Complete Sale

  Line 215: 'Number' → 'Nambari'
           <p class="mb-2">Sale Number: <strong id="sale-number"></strong></p>

  Line 216: 'Total' → 'Jumla'
           <p class="mb-2">Total: <strong id="sale-total"></strong></p>

  Line 259: 'Add' → 'Ongeza'
           // Add to cart functionality

  Line 298: 'Update' → 'Sasisha'
           // Update cart display

  Line 363: 'Update' → 'Sasisha'
           // Update quantity

  Line 444: 'Update' → 'Sasisha'
           // Update today's sales (simple reload for now)

  Line 453: 'Error' → 'Hitilafu'
           console.error('Error:', error);

  Line 458: 'Payment' → 'Malipo'
           // Cash Payment Modal Functions

  Line 479: 'Amount' → 'Kiasi'
           // Amount received input handler

  Line 496: 'Complete' → 'Kamili'
           // Complete cash sale

  Line 532: 'Change' → 'Badilisha'
           change > 0 ? `Change given: TZS ${change.toLocaleString()}` : '';

  Line 545: 'Error' → 'Hitilafu'
           console.error('Error:', error);

📄 templates\pos_products.html
----------------------------------------
  Line  14: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-1"></i>Add Product

  Line  17: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to POS

  Line 110: 'Total' → 'Jumla'
           <p class="text-muted mb-0">Total Products</p>

  Line 119: 'Active' → 'Hai'
           <p class="text-muted mb-0">Active Products</p>

  Line 145: 'Total' → 'Jumla'
           <p class="text-muted mb-0">Total Value</p>

  Line 157: 'Add' → 'Ongeza'
           <i class="fas fa-plus me-2"></i>Add Your First Product

  Line 169: 'Confirm' → 'Thibitisha'
           <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-danger me-2">...

  Line 169: 'Delete' → 'Futa'
           <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-danger me-2">...

  Line 178: 'Delete' → 'Futa'
           <button type="button" class="btn btn-danger" id="confirm-delete">Delete Product<...

📄 templates\pos_product_form.html
----------------------------------------
  Line  24: 'Name' → 'Jina'
           <label for="name" class="form-label">Product Name *</label>

  Line  55: 'Price' → 'Bei'
           <label for="price" class="form-label">Price (TZS) *</label>

  Line  96: 'Active' → 'Hai'
           Product is Active

  Line  99: 'Inactive' → 'Haifanyi Kazi'
           <small class="text-muted">Inactive products won't appear in the POS</small>

  Line 105: 'Actions' → 'Vitendo'
           <!-- Form Actions -->

  Line 109: 'Cancel' → 'Ghairi'
           <i class="fas fa-arrow-left me-1"></i>Cancel

  Line 181: 'Update' → 'Sasisha'
           // Update preview if it exists

📄 templates\pos_sales.html
----------------------------------------
  Line   9: 'Reports' → 'Ripoti'
           <h2 class="mb-1"><i class="fas fa-chart-line me-2 text-success"></i>Sales Report...

  Line  14: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to POS

  Line  26: 'Today' → 'Leo'
           <p class="text-muted mb-0">Today's Sales</p>

  Line  35: 'Month' → 'Mwezi'
           <p class="text-muted mb-0">This Month</p>

  Line  44: 'Total' → 'Jumla'
           <p class="text-muted mb-0">Total Sales</p>

  Line  56: 'Time' → 'Muda'
           <option>All Time</option>

  Line  56: 'All' → 'Yote'
           <option>All Time</option>

  Line  58: 'Week' → 'Wiki'
           <option>This Week</option>

  Line  59: 'Month' → 'Mwezi'
           <option>This Month</option>

  Line  62: 'Export' → 'Hamisha'
           <i class="fas fa-download me-1"></i>Export

  Line  73: 'Date' → 'Tarehe'
           <th>Date & Time</th>

  Line  73: 'Time' → 'Muda'
           <th>Date & Time</th>

  Line 102: 'Customer' → 'Mteja'
           <span class="text-muted">Walk-in Customer</span>

  Line 125: 'View' → 'Ona'
           onclick="viewSaleDetails({{ sale.id }})" title="View Details">

  Line 226: 'Date' → 'Tarehe'
           <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>

  Line 226: 'Date' → 'Tarehe'
           <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>

  Line 227: 'Time' → 'Muda'
           <p><strong>Time:</strong> ${new Date().toLocaleTimeString()}</p>

  Line 227: 'Date' → 'Tarehe'
           <p><strong>Time:</strong> ${new Date().toLocaleTimeString()}</p>

  Line 228: 'Payment' → 'Malipo'
           <p><strong>Payment:</strong> Cash</p>

  Line 231: 'Customer' → 'Mteja'
           <h6 class="text-muted">Customer Information</h6>

  Line 232: 'Customer' → 'Mteja'
           <p><strong>Name:</strong> Walk-in Customer</p>

  Line 232: 'Name' → 'Jina'
           <p><strong>Name:</strong> Walk-in Customer</p>

  Line 233: 'Phone' → 'Simu'
           <p><strong>Phone:</strong> -</p>

  Line 244: 'Price' → 'Bei'
           <th>Unit Price</th>

  Line 270: 'Total' → 'Jumla'
           <span>Total:</span>

📄 templates\pricing.html
----------------------------------------
  Line 109: 'Features' → 'Vipengele'
           <!-- Features -->

  Line 148: 'Features' → 'Vipengele'
           <!-- Key Features -->

  Line 199: 'Payment' → 'Malipo'
           Start Free - No Payment!

  Line 217: 'Back' → 'Rudi'
           <!-- Back Button -->

  Line 224: 'Back' → 'Rudi'
           Back to Home

  Line 224: 'Home' → 'Nyumbani'
           Back to Home

  Line 398: 'Features' → 'Vipengele'
           /* Features Section */

📄 templates\public_company_landing.html
----------------------------------------
  Line 409: 'Payment' → 'Malipo'
           /* Payment guidance styles */

  Line 479: 'Payment' → 'Malipo'
           <i class="fas fa-check-circle me-2"></i>Confirm Your Payment

  Line 479: 'Confirm' → 'Thibitisha'
           <i class="fas fa-check-circle me-2"></i>Confirm Your Payment

  Line 486: 'Name' → 'Jina'
           <i class="fas fa-user me-2"></i>Your Full Name

  Line 496: 'Amount' → 'Kiasi'
           <i class="fas fa-money-bill-wave me-2"></i>Amount Paid (TZS)

  Line 504: 'Payment' → 'Malipo'
           <i class="fas fa-mobile-alt me-2"></i>Payment Method Used

  Line 563: 'Number' → 'Nambari'
           <i class="fas fa-receipt me-2"></i>Transaction ID / Reference Number

  Line 576: 'Phone' → 'Simu'
           <i class="fas fa-phone me-2"></i>Phone Number Used for Payment

  Line 576: 'Payment' → 'Malipo'
           <i class="fas fa-phone me-2"></i>Phone Number Used for Payment

  Line 576: 'Number' → 'Nambari'
           <i class="fas fa-phone me-2"></i>Phone Number Used for Payment

  Line 600: 'Submit' → 'Wasilisha'
           <span>Submit Confirmation</span>

  Line 609: 'Payment' → 'Malipo'
           <strong>Payment methods are being configured.</strong><br>

  Line 617: 'Payment' → 'Malipo'
           // Payment method selection in confirmation form

  Line 627: 'Add' → 'Ongeza'
           // Add selected class to chosen option

  Line 646: 'Payment' → 'Malipo'
           btnText.textContent = 'Submit Payment Confirmation';

  Line 646: 'Submit' → 'Wasilisha'
           btnText.textContent = 'Submit Payment Confirmation';

  Line 720: 'Payment' → 'Malipo'
           // Payment method selection guidance

  Line 727: 'Create' → 'Unda'
           // Create guidance div if it doesn't exist

  Line 740: 'Payment' → 'Malipo'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>M-Pesa Payment:</...

  Line 740: 'Amount' → 'Kiasi'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>M-Pesa Payment:</...

  Line 740: 'Number' → 'Nambari'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>M-Pesa Payment:</...

  Line 743: 'Payment' → 'Malipo'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Tigo Pesa Payment...

  Line 743: 'Amount' → 'Kiasi'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Tigo Pesa Payment...

  Line 743: 'Number' → 'Nambari'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Tigo Pesa Payment...

  Line 746: 'Payment' → 'Malipo'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Airtel Money Paym...

  Line 746: 'Amount' → 'Kiasi'
           guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Airtel Money Paym...

📄 templates\register.html
----------------------------------------
  Line  17: 'Company' → 'Kampuni'
           <h3 class="mb-0">Create Your Company Account</h3>

  Line  17: 'Create' → 'Unda'
           <h3 class="mb-0">Create Your Company Account</h3>

  Line  26: 'Login' → 'Ingia'
           <i class="fas fa-sign-in-alt me-2"></i>Login to Existing Account

  Line  34: 'Name' → 'Jina'
           <label for="full_name" class="form-label">Full Name *</label>

  Line  39: 'Email' → 'Barua pepe'
           <label for="email" class="form-label">Email *</label>

  Line  44: 'Password' → 'Nywila'
           <label for="password" class="form-label">Password *</label>

  Line  46: 'Password' → 'Nywila'
           <div class="invalid-feedback">Password must be at least 6 characters.</div>

  Line  49: 'Company' → 'Kampuni'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  49: 'Name' → 'Jina'
           <label for="company_name" class="form-label">Company Name *</label>

  Line  58: 'Company' → 'Kampuni'
           <label for="company_website" class="form-label">Company Website</label>

📄 templates\request_pos.html
----------------------------------------
  Line  19: 'Add' → 'Ongeza'
           <strong>POS Add-on Price:</strong> <span class="text-success">TZS {{ "{:,}".form...

  Line  19: 'Price' → 'Bei'
           <strong>POS Add-on Price:</strong> <span class="text-success">TZS {{ "{:,}".form...

  Line  31: 'Payment' → 'Malipo'
           <label for="payment_reference" class="form-label">Payment Reference / Transactio...

  Line  35: 'Submit' → 'Wasilisha'
           <button type="submit" class="btn btn-primary">Submit POS Unlock Request</button>

  Line  39: 'Dashboard' → 'Dashibodi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

  Line  39: 'Back' → 'Rudi'
           <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to...

📄 templates\reset_password.html
----------------------------------------
  Line   7: 'Password' → 'Nywila'
           Set New Password - Exlipa

  Line  16: 'Password' → 'Nywila'
           <h4><i class="fas fa-unlock-alt me-2"></i>Set New Password</h4>

  Line  21: 'Password' → 'Nywila'
           <label for="password" class="form-label">New Password</label>

  Line  30: 'Password' → 'Nywila'
           <i class="fas fa-save me-1"></i>Set Password

  Line  37: 'Login' → 'Ingia'
           <a href="{{ url_for('user_admin_login') }}" class="text-decoration-none">Back to...

  Line  37: 'Back' → 'Rudi'
           <a href="{{ url_for('user_admin_login') }}" class="text-decoration-none">Back to...

📄 templates\reset_request.html
----------------------------------------
  Line   7: 'Reset' → 'Weka upya'
           Reset Password - Exlipa

  Line   7: 'Password' → 'Nywila'
           Reset Password - Exlipa

  Line  16: 'Forgot' → 'Umesahau'
           <h4><i class="fas fa-key me-2"></i>Forgot Password?</h4>

  Line  16: 'Password' → 'Nywila'
           <h4><i class="fas fa-key me-2"></i>Forgot Password?</h4>

  Line  21: 'Email' → 'Barua pepe'
           <label for="email" class="form-label">Email address</label>

  Line  26: 'Reset' → 'Weka upya'
           <i class="fas fa-paper-plane me-1"></i>Send Reset Link

  Line  33: 'Login' → 'Ingia'
           <a href="{{ url_for('user_admin_login') }}" class="text-decoration-none">Back to...

  Line  33: 'Back' → 'Rudi'
           <a href="{{ url_for('user_admin_login') }}" class="text-decoration-none">Back to...

📄 templates\simple_landing.html
----------------------------------------
  Line  40: 'Company' → 'Kampuni'
           placeholder="Your Company - Pay Online">

  Line  45: 'Description' → 'Maelezo'
           <label for="landing_page_description" class="form-label">Page Description</label...

  Line  53: 'Message' → 'Ujumbe'
           <label for="custom_message" class="form-label">Custom Message</label>

  Line  62: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-1"></i>Save Landing Page

  Line  71: 'Company' → 'Kampuni'
           <h5>Company Info</h5>

  Line  71: 'Info' → 'Taarifa'
           <h5>Company Info</h5>

  Line  72: 'Name' → 'Jina'
           <p><strong>Name:</strong> {{ company.company_name }}</p>

  Line  73: 'Email' → 'Barua pepe'
           <p><strong>Email:</strong> {{ company.company_email }}</p>

  Line  74: 'Status' → 'Hali'
           <p><strong>Current Status:</strong>

  Line  90: 'Save' → 'Hifadhi'
           console.log('Save button found:', !!saveBtn);

  Line 103: 'Save' → 'Hifadhi'
           console.log('Save button clicked!');

📄 templates\start_signup.html
----------------------------------------
  Line 117: 'Name' → 'Jina'
           <label for="mobile_money_sender_name" class="form-label">Mobile Money Registered...

  Line 123: 'Payment' → 'Malipo'
           <label for="mobile_operator" class="form-label">Payment Method *</label>

  Line 133: 'Payment' → 'Malipo'
           <!-- Dynamic Payment Method Details -->

  Line 147: 'Payment' → 'Malipo'
           <button type="submit" class="btn btn-success w-100">Submit Payment Confirmation<...

  Line 147: 'Submit' → 'Wasilisha'
           <button type="submit" class="btn btn-success w-100">Submit Payment Confirmation<...

  Line 151: 'Back' → 'Rudi'
           <a href="{{ url_for('pricing') }}" class="btn btn-link">&larr; Back to Packages<...

  Line 158: 'Payment' → 'Malipo'
           // Payment method details data

  Line 163: 'Number' → 'Nambari'
           detailLabel: "Till Number",

  Line 165: 'Payment' → 'Malipo'
           business: "Exlipa Payment Solutions Ltd",

  Line 173: 'Payment' → 'Malipo'
           business: "Exlipa Payment Solutions Ltd",

  Line 181: 'Payment' → 'Malipo'
           business: "Exlipa Payment Solutions Ltd",

  Line 189: 'Payment' → 'Malipo'
           business: "Exlipa Payment Solutions Ltd",

  Line 194: 'Update' → 'Sasisha'
           // Update payment details on selection

  Line 206: 'Name' → 'Jina'
           <div><strong>Business Name:</strong> ${d.business}</div>

📄 templates\team_management.html
----------------------------------------
  Line   7: 'Team' → 'Timu'
           Team Management - Exlipa

  Line  17: 'Team' → 'Timu'
           <h3 class="mb-0">Team Management</h3>

  Line  20: 'Team' → 'Timu'
           <h5 class="mb-3">Your Team</h5>

  Line  29: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line  40: 'Active' → 'Hai'
           {{ 'Active' if member.is_active else 'Disabled' }}

  Line  60: 'Team' → 'Timu'
           <h5>Invite New Team Member</h5>

  Line  63: 'Email' → 'Barua pepe'
           <input type="email" class="form-control" name="email" placeholder="Email address...

📄 templates\tier_based_landing_builder.html
----------------------------------------
  Line 411: 'Status' → 'Hali'
           <!-- Tier Status -->

  Line 414: 'Features' → 'Vipengele'
           <h6 class="mb-2">🚀 Unlock More Features!</h6>

  Line 414: 'More' → 'Zaidi'
           <h6 class="mb-2">🚀 Unlock More Features!</h6>

  Line 416: 'View' → 'Ona'
           <a href="{{ url_for('pricing') }}" class="btn btn-light btn-sm">View Pricing</a>

  Line 434: 'Actions' → 'Vitendo'
           <!-- Quick Actions -->

  Line 543: 'Features' → 'Vipengele'
           <!-- Advanced Features -->

  Line 546: 'Add' → 'Ongeza'
           <p class="text-muted small">Add your own custom styles</p>

  Line 553: 'Features' → 'Vipengele'
           <h6>Your Current Features</h6>

  Line 582: 'Features' → 'Vipengele'
           Upgrade for More Features

  Line 582: 'More' → 'Zaidi'
           Upgrade for More Features

  Line 592: 'Save' → 'Hifadhi'
           <i class="fas fa-save me-2"></i>Save Landing Page

  Line 700: 'Add' → 'Ongeza'
           // Add new template class

  Line 703: 'Update' → 'Sasisha'
           // Update CTA button style based on template

  Line 754: 'Save' → 'Hifadhi'
           // Save functionality

  Line 757: 'Add' → 'Ongeza'
           // Add save logic here

  Line 792: 'Payment' → 'Malipo'
           const subject = `Payment Page - ${companyName}`;

  Line 834: 'Create' → 'Unda'
           // Create toast notification

  Line 861: 'Add' → 'Ongeza'
           // Add CSS for toast animation

📄 templates\user_admin_dashboard.html
----------------------------------------
  Line  75: 'View' → 'Ona'
           <i class="fas fa-chart-line me-1"></i>View Analytics

  Line  75: 'Analytics' → 'Uchambuzi'
           <i class="fas fa-chart-line me-1"></i>View Analytics

  Line  81: 'View' → 'Ona'
           <i class="fas fa-chart-line me-1"></i>View Analytics

  Line  81: 'Analytics' → 'Uchambuzi'
           <i class="fas fa-chart-line me-1"></i>View Analytics

  Line 156: 'Actions' → 'Vitendo'
           <h5 class="fw-bold mb-3"><i class="fas fa-bullhorn me-2 text-primary"></i>Quick ...

  Line 158: 'Features' → 'Vipengele'
           <!-- Core Features - Available to all tiers -->

  Line 171: 'Features' → 'Vipengele'
           <!-- Business+ Features -->

  Line 173: 'Manage' → 'Simamia'
           <a href="{{ url_for('team_management') }}" class="btn btn-outline-info flex-fill...

  Line 173: 'Team' → 'Timu'
           <a href="{{ url_for('team_management') }}" class="btn btn-outline-info flex-fill...

  Line 176: 'Team' → 'Timu'
           <button class="btn btn-outline-info flex-fill" disabled title="Available in Busi...

  Line 180: 'Features' → 'Vipengele'
           <!-- Enterprise Features -->

  Line 210: 'Company' → 'Kampuni'
           <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2 text-info"></i>Compan...

  Line 210: 'Info' → 'Taarifa'
           <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2 text-info"></i>Compan...

  Line 212: 'Company' → 'Kampuni'
           <div class="mb-1"><strong>Company:</strong> {{ user_company.company_name }}</div...

  Line 213: 'Email' → 'Barua pepe'
           <div class="mb-1"><strong>Email:</strong> {{ user_company.company_email }}</div>

  Line 214: 'Phone' → 'Simu'
           <div class="mb-1"><strong>Phone:</strong> {{ user_company.company_phone or 'N/A'...

  Line 217: 'Username' → 'Jina la Mtumiaji'
           <div class="mb-1"><strong>Username:</strong> {{ current_user.username }}</div>

  Line 219: 'User' → 'Mtumiaji'
           <div class="mb-1"><strong>Access Level:</strong> User Administrative</div>

  Line 220: 'Company' → 'Kampuni'
           <div class="mb-1"><strong>Company:</strong> Not Associated</div>

  Line 230: 'Billing' → 'Malipo'
           <li class="list-inline-item"><span class="badge bg-primary"><i class="fas fa-fil...

  Line 231: 'Payment' → 'Malipo'
           <li class="list-inline-item"><span class="badge bg-success"><i class="fas fa-cre...

  Line 232: 'Team' → 'Timu'
           <li class="list-inline-item"><span class="badge bg-info"><i class="fas fa-users ...

  Line 233: 'Active' → 'Hai'
           <li class="list-inline-item"><span class="badge bg-warning text-dark"><i class="...

  Line 233: 'Pending' → 'Inasubiri'
           <li class="list-inline-item"><span class="badge bg-warning text-dark"><i class="...

  Line 233: 'Locked' → 'Imefungwa'
           <li class="list-inline-item"><span class="badge bg-warning text-dark"><i class="...

📄 templates\user_admin_login.html
----------------------------------------
  Line   7: 'User' → 'Mtumiaji'
           User Admin Login - EXLIPA

  Line   7: 'Admin' → 'Msimamizi'
           User Admin Login - EXLIPA

  Line   7: 'Login' → 'Ingia'
           User Admin Login - EXLIPA

  Line  19: 'User' → 'Mtumiaji'
           User Admin Login

  Line  19: 'Admin' → 'Msimamizi'
           User Admin Login

  Line  19: 'Login' → 'Ingia'
           User Admin Login

  Line  39: 'Email' → 'Barua pepe'
           Email or Username

  Line  39: 'Username' → 'Jina la Mtumiaji'
           Email or Username

  Line  55: 'User' → 'Mtumiaji'
           User Admin Login

  Line  55: 'Admin' → 'Msimamizi'
           User Admin Login

  Line  55: 'Login' → 'Ingia'
           User Admin Login

  Line  66: 'User' → 'Mtumiaji'
           User Admin access only

  Line  66: 'Admin' → 'Msimamizi'
           User Admin access only

📄 templates\user_invoices.html
----------------------------------------
  Line   7: 'Invoices' → 'Ankara'
           My Invoices - EXLIPA

  Line  15: 'Invoices' → 'Ankara'
           <i class="fas fa-file-invoice me-2"></i>My Invoices

  Line  19: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  19: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  22: 'Invoice' → 'Ankara'
           <i class="fas fa-plus me-1"></i>Create Invoice

  Line  22: 'Create' → 'Unda'
           <i class="fas fa-plus me-1"></i>Create Invoice

  Line  46: 'Invoice' → 'Ankara'
           <i class="fas fa-list me-2"></i>Invoice History

  Line  55: 'Invoice' → 'Ankara'
           <th>Invoice #</th>

  Line  56: 'Customer' → 'Mteja'
           <th>Customer</th>

  Line  59: 'Date' → 'Tarehe'
           <th>Due Date</th>

  Line  61: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line  77: 'Pending' → 'Inasubiri'
           {% elif invoice.status == 'Pending' %}

  Line  96: 'View' → 'Ona'
           class="btn btn-outline-primary" title="View Details">

  Line 106: 'Download' → 'Pakua'
           class="btn btn-outline-success" title="Download PDF">

  Line 119: 'Invoices' → 'Ankara'
           <h5 class="text-muted">No Invoices Yet</h5>

  Line 122: 'Invoice' → 'Ankara'
           <i class="fas fa-plus me-1"></i>Create Your First Invoice

  Line 122: 'Create' → 'Unda'
           <i class="fas fa-plus me-1"></i>Create Your First Invoice

  Line 133: 'Invoices' → 'Ankara'
           <h6 class="text-muted mb-1">Total Invoices</h6>

  Line 133: 'Total' → 'Jumla'
           <h6 class="text-muted mb-1">Total Invoices</h6>

  Line 139: 'Invoices' → 'Ankara'
           <h6 class="text-muted mb-1">Paid Invoices</h6>

  Line 145: 'Invoices' → 'Ankara'
           <h6 class="text-muted mb-1">Pending Invoices</h6>

  Line 145: 'Pending' → 'Inasubiri'
           <h6 class="text-muted mb-1">Pending Invoices</h6>

  Line 146: 'Pending' → 'Inasubiri'
           <h4 class="mb-0 text-warning">{{ invoices|selectattr('status', 'equalto', 'Pendi...

📄 templates\user_payments.html
----------------------------------------
  Line   7: 'Payments' → 'Malipo'
           My Payments - EXLIPA

  Line  15: 'Payments' → 'Malipo'
           <i class="fas fa-credit-card me-2"></i>My Payments

  Line  19: 'Dashboard' → 'Dashibodi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  19: 'Back' → 'Rudi'
           <i class="fas fa-arrow-left me-1"></i>Back to Dashboard

  Line  22: 'Payment' → 'Malipo'
           <i class="fas fa-search me-1"></i>Check Payment Status

  Line  22: 'Status' → 'Hali'
           <i class="fas fa-search me-1"></i>Check Payment Status

  Line  46: 'Payment' → 'Malipo'
           <i class="fas fa-list me-2"></i>Payment History

  Line  55: 'Payment' → 'Malipo'
           <th>Payment ID</th>

  Line  56: 'Customer' → 'Mteja'
           <th>Customer</th>

  Line  61: 'Actions' → 'Vitendo'
           <th>Actions</th>

  Line  82: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line  84: 'Pending' → 'Inasubiri'
           {% elif payment.status == 'Pending' %}

  Line  97: 'View' → 'Ona'
           title="View Details">

  Line 100: 'Confirmed' → 'Imethibitishwa'
           {% if payment.status == 'Confirmed' %}

  Line 103: 'Download' → 'Pakua'
           title="Download Receipt">

  Line 117: 'Payments' → 'Malipo'
           <h5 class="text-muted">No Payments Yet</h5>

  Line 121: 'Invoice' → 'Ankara'
           <i class="fas fa-file-invoice me-1"></i>Create Invoice

  Line 121: 'Create' → 'Unda'
           <i class="fas fa-file-invoice me-1"></i>Create Invoice

  Line 125: 'Payment' → 'Malipo'
           <i class="fas fa-globe me-1"></i>Share Payment Link

  Line 138: 'Payments' → 'Malipo'
           <h6 class="text-muted mb-1">Total Payments</h6>

  Line 138: 'Total' → 'Jumla'
           <h6 class="text-muted mb-1">Total Payments</h6>

  Line 145: 'Confirmed' → 'Imethibitishwa'
           <h4 class="mb-0 text-success">{{ payments|selectattr('status', 'equalto', 'Confi...

  Line 151: 'Pending' → 'Inasubiri'
           <h4 class="mb-0 text-warning">{{ payments|selectattr('status', 'equalto', 'Pendi...

  Line 156: 'Amount' → 'Kiasi'
           <h6 class="text-muted mb-1">Total Amount</h6>

  Line 156: 'Total' → 'Jumla'
           <h6 class="text-muted mb-1">Total Amount</h6>

  Line 158: 'Confirmed' → 'Imethibitishwa'
           TZS {{ "{:,.2f}".format(payments|selectattr('status', 'equalto', 'Confirmed')|ma...

  Line 166: 'Payment' → 'Malipo'
           <!-- Payment Details Modal -->

  Line 171: 'Payment' → 'Malipo'
           <h5 class="modal-title">Payment Details</h5>

  Line 175: 'Payment' → 'Malipo'
           <!-- Payment details will be loaded here -->

  Line 189: 'Payment' → 'Malipo'
           <div class="col-6"><strong>Payment ID:</strong></div>

  Line 191: 'Amount' → 'Kiasi'
           <div class="col-6"><strong>Amount:</strong></div>

  Line 193: 'Status' → 'Hali'
           <div class="col-6"><strong>Status:</strong></div>

  Line 194: 'Confirmed' → 'Imethibitishwa'
           <div class="col-6"><span class="badge bg-${data.status === 'Confirmed' ? 'succes...

  Line 197: 'Date' → 'Tarehe'
           <div class="col-6"><strong>Date:</strong></div>

📄 templates\view_invoice.html
----------------------------------------
  Line   3: 'Invoice' → 'Ankara'
           {% block title %}Invoice {{ invoice.invoice_number }} - Payment System{% endbloc...

  Line   3: 'Payment' → 'Malipo'
           {% block title %}Invoice {{ invoice.invoice_number }} - Payment System{% endbloc...

  Line  12: 'Invoice' → 'Ankara'
           <i class="fas fa-file-invoice me-2"></i>Invoice {{ invoice.invoice_number }}

  Line  38: 'Invoice' → 'Ankara'
           <strong>Invoice Date:</strong> {{ invoice.created_at.strftime('%d/%m/%Y') }}<br>

  Line  38: 'Date' → 'Tarehe'
           <strong>Invoice Date:</strong> {{ invoice.created_at.strftime('%d/%m/%Y') }}<br>

  Line  39: 'Date' → 'Tarehe'
           <strong>Due Date:</strong>

  Line  43: 'Status' → 'Hali'
           <strong>Status:</strong>

  Line 116: 'Amount' → 'Kiasi'
           <th {% if invoice.cart_items %}colspan="2"{% endif %}>Total Amount Due:</th>

  Line 116: 'Total' → 'Jumla'
           <th {% if invoice.cart_items %}colspan="2"{% endif %}>Total Amount Due:</th>

  Line 143: 'Payment' → 'Malipo'
           <h6>View Payment Instructions</h6>

  Line 143: 'View' → 'Ona'
           <h6>View Payment Instructions</h6>

  Line 147: 'Payment' → 'Malipo'
           <i class="fas fa-arrow-right me-1"></i>Payment Guide

  Line 157: 'Confirm' → 'Thibitisha'
           <p class="small">Confirm your payment using your transaction ID</p>

  Line 160: 'Payment' → 'Malipo'
           <i class="fas fa-check me-1"></i>Confirm Payment

  Line 160: 'Confirm' → 'Thibitisha'
           <i class="fas fa-check me-1"></i>Confirm Payment

  Line 178: 'Payment' → 'Malipo'
           <h4 class="text-success">Payment Received!</h4>

  Line 186: 'Download' → 'Pakua'
           <i class="fas fa-download me-1"></i>Download Receipt

  Line 199: 'Help' → 'Msaada'
           <i class="fas fa-info-circle me-2"></i>Need Help?

  Line 203: 'Email' → 'Barua pepe'
           <i class="fas fa-envelope me-1"></i> Email: <EMAIL><br>

📄 templates\webhook_management.html
----------------------------------------
  Line  25: 'Manage' → 'Simamia'
           Manage webhooks for {{ company.company_name }}

  Line 136: 'Create' → 'Unda'
           <!-- Create Webhook -->

  Line 310: 'Documentation' → 'Nyaraka'
           <!-- Webhook Documentation -->

  Line 332: 'Confirmed' → 'Imethibitishwa'
           "status": "Confirmed"

  Line 375: 'Create' → 'Unda'
           // Create webhook

  Line 417: 'Delete' → 'Futa'
           // Delete webhook


🎯 RECOMMENDED ACTIONS:
1. Replace hardcoded English text with conditional translations
2. Use: {% if session.language == 'sw' %}Swahili{% else %}English{% endif %}
3. Add missing translations to app.py translation dictionary
4. Test language switching functionality
