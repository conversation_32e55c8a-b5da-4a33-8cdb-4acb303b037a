{% extends "base.html" %}

{% block title %}{{ t('Master {%- if session.language == "sw" -%}<PERSON><PERSON><PERSON><PERSON>{%- else -%}Admin{%- endif -%} Dashboard') }} - EXLIPA{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt me-2"></i>{{ t('Master {%- if session.language == "sw" -%}Ms<PERSON><PERSON><PERSON>{%- else -%}Admin{%- endif -%} Dashboard') }}</h1>
    <div>
        <span class="text-muted">{{ t('{%- if session.language == "sw" -%}Ka<PERSON>bu{%- else -%}Welcome{%- endif -%}') }}, {{ current_user.username }}</span>
    </div>
</div>

<!-- {%- if session.language == "sw" -%}Takwimu{%- else -%}Statistics{%- endif -%} Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-file-invoice fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ total_invoices }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Total Invoices') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-hourglass-half fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ unpaid_invoices }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Unpaid Invoices') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ pending_count }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Pending Payments') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ confirmed_count }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('{%- if session.language == "sw" -%}Imethibitishwa{%- else -%}Confirmed{%- endif -%}') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ today_confirmed }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('{%- if session.language == "sw" -%}Leo{%- else -%}Today{%- endif -%}') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h3 style="color: white; font-weight: 700;">{{ active_companies }}</h3>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('{%- if session.language == "sw" -%}Makampuni{%- else -%}Companies{%- endif -%}') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick {%- if session.language == "sw" -%}Vitendo{%- else -%}Actions{%- endif -%} -->
<div class="row g-4 mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick {%- if session.language == "sw" -%}Msimamizi{%- else -%}Admin{%- endif -%} Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_invoices') }}" class="btn btn-outline-info">
                        <i class="fas fa-file-invoice me-2"></i>Manage Invoices
                    </a>
                    <a href="{{ url_for('admin_payments', status='{%- if session.language == "sw" -%}Inasubiri{%- else -%}Pending{%- endif -%}') }}" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>Pending Payments
                    </a>
                    <a href="{{ url_for('admin_payments') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>All Payments
                    </a>
                    <a href="{{ url_for('admin_pos_requests') }}" class="btn btn-outline-warning">
                        <i class="fas fa-cash-register me-2"></i>POS Unlock Requests
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Billing & {%- if session.language == "sw" -%}Kampuni{%- else -%}Company{%- endif -%} Management</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_companies') }}" class="btn btn-success">
                        <i class="fas fa-building me-2"></i>Manage Companies
                    </a>
                    <a href="{{ url_for('admin_pricing_tiers') }}" class="btn btn-outline-success">
                        <i class="fas fa-tags me-2"></i>Pricing Tiers
                    </a>
                    <a href="{{ url_for('admin_billing') }}" class="btn btn-warning">
                        <i class="fas fa-file-invoice-dollar me-2"></i>View Bills
                    </a>
                    <a href="{{ url_for('generate_monthly_bills') }}" class="btn btn-outline-warning">
                        <i class="fas fa-calculator me-2"></i>Generate Bills
                    </a>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        {%- if session.language == "sw" -%}Simamia{%- else -%}Manage{%- endif -%} all client companies, pricing tiers, and billing from one place.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Mobile Money Operators Supported:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>TigoPesa</li>
                    <li><i class="fas fa-check text-success me-2"></i>{{ t('Airtel Money') }}</li>
                    <li><i class="fas fa-check text-success me-2"></i>{{ t('M-Pesa') }}</li>
                    <li><i class="fas fa-check text-success me-2"></i>CRDB</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Platform Features:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>{{ t('Payment Confirmation') }}</li>
                    <li><i class="fas fa-check text-success me-2"></i>Receipt Generation</li>
                    <li><i class="fas fa-check text-success me-2"></i>Admin Verification</li>
                    <li><i class="fas fa-check text-success me-2"></i>Status Tracking</li>
                    <li><i class="fas fa-cash-register text-primary me-2"></i>Dynamic POS Management</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
