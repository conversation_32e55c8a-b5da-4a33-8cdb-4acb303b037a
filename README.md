# Mobile Money Payment Confirmation System

A complete payment confirmation system for Tanzanian businesses using mobile money operators like M-Pesa, Tigo Pesa, Airtel Money, and Halo Pesa.

## Features

### Customer Features
- **Payment Instructions**: Clear step-by-step guides for all major mobile money operators
- **Payment Confirmation**: Simple form to submit payment details with transaction ID
- **Status Tracking**: Real-time feedback on payment confirmation status
- **Mobile Responsive**: Works perfectly on mobile devices

### Admin Features
- **Dashboard**: Overview of pending, confirmed, and processed payments
- **Payment Management**: View, filter, and search all payment confirmations
- **Verification Actions**: Confirm, reject, or mark payments as processed
- **Receipt Generation**: Professional PDF receipts for confirmed payments
- **Internal Notes**: Add private notes to payment records
- **User Management**: Simple admin user management

## Technology Stack

- **Backend**: Python 3.8+ with Flask
- **Database**: SQLite (easily upgradeable to PostgreSQL/MySQL)
- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **PDF Generation**: ReportLab
- **Authentication**: Flask-Login

## Installation

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or Download** this project to your local machine

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   ```bash
   # Copy the example environment file
   copy .env.example .env  # Windows
   cp .env.example .env    # macOS/Linux
   
   # Edit .env file with your company details
   ```

5. **Run the application**:
   ```bash
   python app.py
   ```

6. **Access the application**:
   - Customer interface: http://localhost:5000
   - Admin login: http://localhost:5000/login
   - Default admin credentials: admin / admin123

## Configuration

### Company Information
Edit the `.env` file to customize your company details:

```
COMPANY_NAME=Your Company Name Ltd
COMPANY_ADDRESS=P.O. Box 12345, Dar es Salaam, Tanzania
COMPANY_PHONE=+255 XXX XXX XXX
COMPANY_EMAIL=<EMAIL>
```

### Mobile Money Setup
Update your till/paybill numbers in:
1. `.env` file (for configuration)
2. `templates/payment_instructions.html` (for customer display)

### Security
- Change the `SECRET_KEY` in `.env` for production
- Update default admin credentials
- Use HTTPS in production

## Usage

### For Customers
1. Visit the payment instructions page
2. Make payment using mobile money to your company's till/paybill
3. Note the transaction ID from SMS confirmation
4. Submit payment confirmation with transaction details
5. Receive confirmation and reference number

### For Administrators
1. Login to admin dashboard
2. View pending payment confirmations
3. Verify payments against mobile money records
4. Confirm or reject payments with reasons
5. Generate PDF receipts for confirmed payments
6. Mark payments as processed when service is delivered

## File Structure

```
payment-system/
├── app.py                      # Main Flask application
├── receipt_generator.py        # PDF receipt generation
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── README.md                 # This file
├── templates/                # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── payment_instructions.html
│   ├── confirm_payment.html
│   ├── confirmation_success.html
│   ├── login.html
│   ├── admin_dashboard.html
│   ├── admin_payments.html
│   └── payment_detail.html
└── static/                   # CSS, JS, images
    └── css/
        └── style.css
```

## Database Schema

The system uses three main database tables:

### Users
- Admin user accounts for system access

### PaymentConfirmation
- Customer payment submission records
- Status tracking (Pending, Confirmed, Rejected, Processed)
- Internal notes and processing history

## Customization

### Adding New Mobile Money Operators
1. Update payment instructions template
2. Add operator to dropdown in confirmation form
3. Update CSS styles if needed

### Customizing Receipts
Edit `receipt_generator.py` to modify:
- Receipt layout and styling
- Company branding
- Additional fields

### Extending Functionality
The system is built with extensibility in mind:
- Add new payment statuses
- Integrate with external APIs
- Add email notifications
- Implement bulk operations

## Production Deployment

### Security Checklist
- [ ] Change SECRET_KEY
- [ ] Update default admin credentials  
- [ ] Enable HTTPS
- [ ] Set up proper database backups
- [ ] Configure logging
- [ ] Set up monitoring

### Recommended Hosting
- **Small Scale**: Shared hosting with Python support
- **Medium Scale**: VPS with Ubuntu/CentOS
- **Large Scale**: Cloud platforms (AWS, Google Cloud, Azure)

### Database Upgrade
For production, consider upgrading from SQLite to PostgreSQL:
```python
# In app.py, change:
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:pass@localhost/dbname'
```

## Support and Maintenance

### Regular Tasks
- Monitor pending confirmations
- Backup database regularly
- Update mobile money operator details
- Review and clean old records

### Troubleshooting
- Check logs for errors
- Verify mobile money operator details
- Ensure database connectivity
- Test receipt generation

## Legal and Compliance

### Tanzania Specific
- Ensure compliance with Bank of Tanzania regulations
- Maintain proper transaction records
- Consider KYC requirements for high-value transactions

### Data Protection
- Implement data retention policies
- Secure customer information
- Regular security audits

## License

This software is provided as-is for business use. Customize according to your needs and local regulations.

## Support

For technical support or customization requests, please contact your development team or system administrator.
