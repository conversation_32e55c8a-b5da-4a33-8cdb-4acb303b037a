#!/usr/bin/env python3
"""
Integration tests for authentication system
"""

import pytest
from flask import url_for
from tests.conftest import login_user, logout_user

class TestAuthenticationIntegration:
    """Integration tests for authentication flows"""
    
    def test_master_admin_login_flow(self, client, super_admin_user):
        """Test complete master admin login flow"""
        # Test login page access
        response = client.get('/master-admin-login')
        assert response.status_code == 200
        assert b'Master Admin Login' in response.data
        
        # Test successful login
        response = client.post('/master-admin-login', data={
            'username': super_admin_user.username,
            'password': 'SuperAdmin123!'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        # Should redirect to master admin dashboard
        assert b'Master Admin Dashboard' in response.data or b'Dashboard' in response.data
        
        # Test logout
        response = client.get('/logout', follow_redirects=True)
        assert response.status_code == 200
    
    def test_user_admin_login_flow(self, client, user_admin_user):
        """Test complete user admin login flow"""
        # Test login page access
        response = client.get('/user-admin-login')
        assert response.status_code == 200
        
        # Test successful login
        response = client.post('/user-admin-login', data={
            'username': user_admin_user.username,
            'password': 'UserAdmin123!'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        # Should redirect to user admin dashboard
        
        # Test logout
        response = client.get('/logout', follow_redirects=True)
        assert response.status_code == 200
    
    def test_company_user_login_flow(self, client, company_user):
        """Test complete company user login flow"""
        # Test login page access
        response = client.get('/company-login')
        assert response.status_code == 200
        
        # Test successful login
        response = client.post('/company-login', data={
            'username': company_user.username,
            'password': 'CompanyUser123!'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Test logout
        response = client.get('/logout', follow_redirects=True)
        assert response.status_code == 200
    
    def test_invalid_login_attempts(self, client, user_admin_user):
        """Test invalid login attempts and account locking"""
        # Test invalid password
        response = client.post('/user-admin-login', data={
            'username': user_admin_user.username,
            'password': 'wrongpassword'
        })
        
        assert response.status_code == 200
        assert b'Invalid username or password' in response.data
        
        # Test multiple failed attempts (should lock account)
        for _ in range(5):
            client.post('/user-admin-login', data={
                'username': user_admin_user.username,
                'password': 'wrongpassword'
            })
        
        # Next attempt should show account locked message
        response = client.post('/user-admin-login', data={
            'username': user_admin_user.username,
            'password': 'wrongpassword'
        })
        
        assert b'Account is temporarily locked' in response.data
    
    def test_role_based_access_control(self, client, super_admin_user, user_admin_user, company_user):
        """Test role-based access control"""
        # Test super admin access to master admin routes
        login_user(client, super_admin_user.username, 'SuperAdmin123!')
        
        response = client.get('/master-admin')
        assert response.status_code == 200
        
        logout_user(client)
        
        # Test user admin cannot access master admin routes
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        response = client.get('/master-admin')
        assert response.status_code == 302  # Should redirect
        
        logout_user(client)
        
        # Test company user cannot access admin routes
        login_user(client, company_user.username, 'CompanyUser123!')
        
        response = client.get('/user-admin-dashboard')
        assert response.status_code == 302  # Should redirect
    
    def test_session_management(self, client, user_admin_user):
        """Test session management and security"""
        # Login user
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Check that session is active
        response = client.get('/user-admin-dashboard')
        assert response.status_code == 200
        
        # Logout
        logout_user(client)
        
        # Check that session is cleared
        response = client.get('/user-admin-dashboard')
        assert response.status_code == 302  # Should redirect to login
    
    def test_unauthorized_access_redirects(self, client):
        """Test that unauthorized access redirects to appropriate login"""
        # Test master admin route redirect
        response = client.get('/master-admin')
        assert response.status_code == 302
        assert '/master-admin-login' in response.location
        
        # Test user admin route redirect
        response = client.get('/user-admin-dashboard')
        assert response.status_code == 302
        assert '/user-admin-login' in response.location
        
        # Test company route redirect
        response = client.get('/company-dashboard')
        assert response.status_code == 302
        assert '/company-login' in response.location

class TestPasswordSecurity:
    """Test password security features"""
    
    def test_password_validation_on_creation(self, app):
        """Test password validation during user creation"""
        from models.user import User
        from utils.validators import ValidationError
        
        with app.app_context():
            user = User(
                username='testuser',
                email='<EMAIL>',
                full_name='Test User',
                role='user_admin'
            )
            
            # Test weak password rejection
            with pytest.raises(ValidationError):
                user.set_password('weak')
            
            # Test strong password acceptance
            user.set_password('StrongPassword123!')
            assert user.password_hash is not None
            assert user.check_password('StrongPassword123!')
    
    def test_password_change_security(self, client, user_admin_user):
        """Test password change security"""
        # Login user
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Test password change with wrong current password
        response = client.post('/change-password', data={
            'current_password': 'wrongpassword',
            'new_password': 'NewPassword123!',
            'confirm_password': 'NewPassword123!'
        })
        
        assert b'Current password is incorrect' in response.data
        
        # Test password change with mismatched new passwords
        response = client.post('/change-password', data={
            'current_password': 'UserAdmin123!',
            'new_password': 'NewPassword123!',
            'confirm_password': 'DifferentPassword123!'
        })
        
        assert b'New passwords do not match' in response.data
        
        # Test successful password change
        response = client.post('/change-password', data={
            'current_password': 'UserAdmin123!',
            'new_password': 'NewPassword123!',
            'confirm_password': 'NewPassword123!'
        })
        
        assert b'Password changed successfully' in response.data

class TestUserProfileManagement:
    """Test user profile management"""
    
    def test_profile_update(self, client, user_admin_user):
        """Test user profile update"""
        # Login user
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Test profile page access
        response = client.get('/profile')
        assert response.status_code == 200
        
        # Test profile update
        response = client.post('/profile', data={
            'full_name': 'Updated Full Name',
            'email': '<EMAIL>',
            'preferred_language': 'sw'
        })
        
        assert b'Profile updated successfully' in response.data
        
        # Verify changes in database
        from models.user import User
        updated_user = User.query.get(user_admin_user.id)
        assert updated_user.full_name == 'Updated Full Name'
        assert updated_user.email == '<EMAIL>'
        assert updated_user.preferred_language == 'sw'
    
    def test_profile_update_validation(self, client, user_admin_user):
        """Test profile update validation"""
        # Login user
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Test invalid email
        response = client.post('/profile', data={
            'full_name': 'Test User',
            'email': 'invalid-email',
            'preferred_language': 'en'
        })
        
        assert b'Invalid email format' in response.data
        
        # Test empty required fields
        response = client.post('/profile', data={
            'full_name': '',
            'email': '',
            'preferred_language': 'en'
        })
        
        assert b'Full name and email are required' in response.data

class TestMultipleSessionHandling:
    """Test handling of multiple sessions"""
    
    def test_concurrent_role_sessions(self, client, super_admin_user, user_admin_user):
        """Test that logging in as different roles properly manages sessions"""
        # Login as super admin
        login_user(client, super_admin_user.username, 'SuperAdmin123!')
        
        # Verify super admin access
        response = client.get('/master-admin')
        assert response.status_code == 200
        
        # Login as user admin (should override super admin session)
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Verify user admin access
        response = client.get('/user-admin-dashboard')
        assert response.status_code == 200
        
        # Verify super admin access is now denied
        response = client.get('/master-admin')
        assert response.status_code == 302  # Should redirect
    
    def test_session_timeout_behavior(self, client, user_admin_user):
        """Test session timeout behavior"""
        # This would require mocking time or using a test configuration
        # with very short session timeout
        pass  # Placeholder for session timeout tests

class TestSecurityHeaders:
    """Test security headers and CSRF protection"""
    
    def test_csrf_protection(self, client, user_admin_user):
        """Test CSRF protection on forms"""
        # Login user
        login_user(client, user_admin_user.username, 'UserAdmin123!')
        
        # Test form submission without CSRF token (if enabled)
        # This test would depend on CSRF being enabled in test config
        pass  # Placeholder for CSRF tests
    
    def test_security_headers(self, client):
        """Test that appropriate security headers are set"""
        response = client.get('/')
        
        # Check for security headers
        # These would be set by Flask-Talisman or similar
        # assert 'X-Content-Type-Options' in response.headers
        # assert 'X-Frame-Options' in response.headers
        pass  # Placeholder for security header tests
