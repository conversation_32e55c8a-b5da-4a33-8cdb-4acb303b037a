{% extends "base.html" %}
{%- block title -%}
{%- if session.language == 'sw' -%}
    My Ankara - Exlipa
{%- else -%}
    My Invoices - Exlipa
{%- endif -%}
{%- endblock -%}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-file-invoice me-2 text-primary"></i>My Invoices</h2>
    {% if invoices %}
    <div class="table-responsive">
        <table class="table table-bordered align-middle">
            <thead class="table-light">
                <tr>
                    <th>Invoice #</th>
                    <th>{{ t('Date') }}</th>
                    <th>{{ t('Amount') }}</th>
                    <th>{{ t('Status') }}</th>
                    <th>{{ t('Description') }}</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in invoices %}
                <tr>
                    <td>{{ invoice.invoice_number }}</td>
                    <td>{{ invoice.created_at.strftime('%Y-%m-%d') if invoice.created_at else 'N/A' }}</td>
                    <td>TZS {{ "{:,}".format(invoice.amount|int) }}</td>
                    <td>
                        <span class="badge {% if invoice.status == 'Paid' %}bg-success{% elif invoice.status == 'Unpaid' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">{{ invoice.status }}</span>
                    </td>
                    <td>{{ invoice.service_description or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">No invoices found.</div>
    {% endif %}
    <div class="mt-3">
        <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to Dashboard</a>
    </div>
</div>
{% endblock %}
