#!/usr/bin/env python3
"""
Automated Translation Fixer for EXLIPA
Systematically fixes ALL hardcoded English text with proper Swahili translations
"""

import os
import re
import glob
from typing import Dict, List, Tuple

class AutomatedTranslationFixer:
    def __init__(self):
        # Comprehensive translation mapping
        self.translations = {
            # Core UI Elements
            'Welcome': '<PERSON><PERSON><PERSON>',
            'Login': 'Ingia',
            'Logout': 'Toka',
            'Dashboard': 'Dashibodi',
            'Company': '<PERSON><PERSON><PERSON>',
            'Admin': '<PERSON><PERSON><PERSON><PERSON>',
            'Master': '<PERSON>uu',
            'User': 'Mtumiaji',
            
            # Payment & Business
            'Payment': '<PERSON>po',
            'Payments': 'Malipo',
            'Invoice': 'Ankara',
            'Invoices': 'Ankara',
            'Amount': 'Kiasi',
            'Customer': 'Mteja',
            'Email': '<PERSON>ua pepe',
            'Phone': 'Simu',
            'Billing': '<PERSON><PERSON>',
            
            # Actions
            'Submit': '<PERSON><PERSON><PERSON>',
            'Confirm': 'T<PERSON><PERSON><PERSON>',
            'Cancel': '<PERSON><PERSON><PERSON>',
            'Edit': '<PERSON><PERSON>',
            'Delete': '<PERSON><PERSON>',
            'View': 'Ona',
            'Manage': '<PERSON><PERSON><PERSON>',
            'Create': 'Unda',
            'Add': 'Ongeza',
            'Update': 'Sasisha',
            'Save': 'Hifadhi',
            'Back': 'Rudi',
            'Next': 'Ifuatayo',
            'Previous': 'Iliyotangulia',
            'Continue': 'Endelea',
            'Finish': 'Maliza',
            'Complete': 'Kamili',
            
            # Status
            'Active': 'Hai',
            'Inactive': 'Haifanyi Kazi',
            'Pending': 'Inasubiri',
            'Confirmed': 'Imethibitishwa',
            'Rejected': 'Imekataliwa',
            'Approved': 'Imeidhinishwa',
            'Locked': 'Imefungwa',
            'Success': 'Mafanikio',
            'Error': 'Hitilafu',
            'Warning': 'Onyo',
            'Info': 'Taarifa',
            
            # Common Fields
            'Name': 'Jina',
            'Description': 'Maelezo',
            'Status': 'Hali',
            'Features': 'Vipengele',
            'Actions': 'Vitendo',
            'Settings': 'Mipangilio',
            'Profile': 'Wasifu',
            'Password': 'Nywila',
            'Username': 'Jina la Mtumiaji',
            
            # Operations
            'Search': 'Tafuta',
            'Filter': 'Chuja',
            'Sort': 'Panga',
            'Export': 'Hamisha',
            'Import': 'Leta',
            'Download': 'Pakua',
            'Upload': 'Pakia',
            
            # Organization
            'Team': 'Timu',
            'Members': 'Wanachama',
            'Users': 'Watumiaji',
            'Companies': 'Makampuni',
            'Organizations': 'Mashirika',
            
            # Analytics
            'Analytics': 'Uchambuzi',
            'Reports': 'Ripoti',
            'Statistics': 'Takwimu',
            'Metrics': 'Vipimo',
            'Performance': 'Utendaji',
            
            # Support
            'Help': 'Msaada',
            'Support': 'Msaada',
            'Contact': 'Wasiliana',
            'About': 'Kuhusu',
            'FAQ': 'Maswali Yanayoulizwa Mara kwa Mara',
            'Documentation': 'Nyaraka',
            
            # Authentication
            'Remember': 'Kumbuka',
            'Forgot': 'Umesahau',
            'Reset': 'Weka upya',
            'Change': 'Badilisha',
            
            # Messages
            'Message': 'Ujumbe',
            'Notification': 'Arifa',
            
            # Navigation
            'Home': 'Nyumbani',
            
            # Numbers & Quantities
            'Total': 'Jumla',
            'Count': 'Idadi',
            'Number': 'Nambari',
            'Quantity': 'Wingi',
            'Price': 'Bei',
            'Cost': 'Gharama',
            'Fee': 'Ada',
            'Charge': 'Malipo',
            
            # Time
            'Date': 'Tarehe',
            'Time': 'Muda',
            'Today': 'Leo',
            'Yesterday': 'Jana',
            'Tomorrow': 'Kesho',
            'Week': 'Wiki',
            'Month': 'Mwezi',
            'Year': 'Mwaka',
            
            # Quantifiers
            'All': 'Yote',
            'None': 'Hakuna',
            'Any': 'Yoyote',
            'Some': 'Baadhi',
            'Every': 'Kila',
            'Each': 'Kila',
            'Other': 'Nyingine',
            'More': 'Zaidi',
            'Less': 'Kidogo',
        }
    
    def fix_template_file(self, filepath: str) -> bool:
        """Fix a single template file with comprehensive pattern matching"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content
            changes_made = 0

            # Fix title blocks first
            content = self._fix_title_blocks(content)

            # Fix hardcoded text in various contexts
            for english, swahili in self.translations.items():
                # Skip if this word already has conditional translation
                if f"session.language == 'sw'" in content and english in content:
                    continue

                # More comprehensive pattern matching
                patterns = [
                    # HTML content patterns
                    (f'>{english}<', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}<'),
                    (f'>{english}</button>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</button>'),
                    (f'>{english}</a>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</a>'),
                    (f'>{english}</h1>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h1>'),
                    (f'>{english}</h2>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h2>'),
                    (f'>{english}</h3>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h3>'),
                    (f'>{english}</h4>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h4>'),
                    (f'>{english}</h5>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h5>'),
                    (f'>{english}</h6>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h6>'),
                    (f'>{english}</span>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</span>'),
                    (f'>{english}</div>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</div>'),
                    (f'>{english}</p>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</p>'),
                    (f'>{english}</label>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</label>'),
                    (f'>{english}</th>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</th>'),
                    (f'>{english}</td>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</td>'),
                    (f'>{english}</li>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</li>'),
                    (f'>{english}</option>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</option>'),

                    # Attribute patterns
                    (f'title="{english}"', f'title="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
                    (f"title='{english}'", f"title='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
                    (f'placeholder="{english}"', f'placeholder="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
                    (f"placeholder='{english}'", f"placeholder='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
                    (f'alt="{english}"', f'alt="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
                    (f"alt='{english}'", f"alt='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),

                    # Comment patterns
                    (f'<!-- {english}', f'<!-- {{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'),

                    # JavaScript string patterns
                    (f'"{english}"', f'"{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
                    (f"'{english}'", f"'{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
                ]

                for old_pattern, new_pattern in patterns:
                    if old_pattern in content:
                        # Only replace if it's not already inside a conditional block
                        if not self._is_inside_conditional(content, old_pattern):
                            content = content.replace(old_pattern, new_pattern)
                            changes_made += 1

            # Only write if changes were made
            if content != original_content:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Fixed {changes_made} translations in {filepath}")
                return True
            else:
                print(f"⚪ No changes needed in {filepath}")
                return False

        except Exception as e:
            print(f"❌ Error fixing {filepath}: {e}")
            return False

    def _is_inside_conditional(self, content: str, pattern: str) -> bool:
        """Check if pattern is already inside a conditional translation block"""
        pattern_index = content.find(pattern)
        if pattern_index == -1:
            return False

        # Look backwards for conditional blocks
        before_pattern = content[:pattern_index]
        conditional_start = before_pattern.rfind('{% if session.language')
        conditional_end = before_pattern.rfind('{% endif %}')

        # If we found a conditional start after the last conditional end, we're inside one
        return conditional_start > conditional_end
    
    def _fix_title_blocks(self, content: str) -> str:
        """Fix title blocks with conditional translation"""
        # Pattern to match title blocks
        title_pattern = r'{% block title %}([^{%]+){% endblock %}'
        
        def replace_title(match):
            title_text = match.group(1).strip()
            
            # Skip if already has translation logic
            if 'session.language' in title_text:
                return match.group(0)
            
            # Create conditional translation
            return f"""{{%- block title -%}}
{{%- if session.language == 'sw' -%}}
    {self._translate_title_text(title_text)}
{{%- else -%}}
    {title_text}
{{%- endif -%}}
{{%- endblock -%}}"""
        
        return re.sub(title_pattern, replace_title, content)
    
    def _translate_title_text(self, title: str) -> str:
        """Translate title text"""
        # Simple word-by-word translation for titles
        words = title.split()
        translated_words = []
        
        for word in words:
            # Remove punctuation for translation lookup
            clean_word = re.sub(r'[^\w]', '', word)
            if clean_word in self.translations:
                # Preserve original punctuation
                translated = word.replace(clean_word, self.translations[clean_word])
                translated_words.append(translated)
            else:
                translated_words.append(word)
        
        return ' '.join(translated_words)
    
    def fix_all_templates(self) -> Dict[str, int]:
        """Fix all template files"""
        results = {'fixed': 0, 'skipped': 0, 'errors': 0}
        
        template_files = glob.glob('templates/*.html')
        
        print(f"🔧 Starting automated translation fix for {len(template_files)} templates...")
        print("=" * 70)
        
        for template_file in template_files:
            try:
                if self.fix_template_file(template_file):
                    results['fixed'] += 1
                else:
                    results['skipped'] += 1
            except Exception as e:
                print(f"❌ Error processing {template_file}: {e}")
                results['errors'] += 1
        
        return results
    
    def update_app_translations(self) -> bool:
        """Update app.py with missing translations"""
        try:
            # Read current app.py
            with open('app.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find the translation dictionary section
            pattern = r"'sw': \{([^}]+)\}"
            match = re.search(pattern, content, re.DOTALL)
            
            if not match:
                print("❌ Could not find Swahili translation dictionary in app.py")
                return False
            
            # Extract existing translations
            existing_dict = match.group(1)
            
            # Add missing translations
            new_translations = []
            for english, swahili in self.translations.items():
                if f"'{english}'" not in existing_dict:
                    new_translations.append(f"            '{english}': '{swahili}',")
            
            if new_translations:
                # Insert new translations before the closing brace
                insertion_point = match.end(1)
                new_content = (content[:insertion_point] + 
                             '\n' + '\n'.join(new_translations) + 
                             content[insertion_point:])
                
                with open('app.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ Added {len(new_translations)} new translations to app.py")
                return True
            else:
                print("⚪ All translations already exist in app.py")
                return False
                
        except Exception as e:
            print(f"❌ Error updating app.py: {e}")
            return False

def main():
    """Main function to run the automated translation fixer"""
    print("🌍 EXLIPA Automated Translation Fixer")
    print("=" * 50)
    
    fixer = AutomatedTranslationFixer()
    
    # Update app.py translations first
    print("\n📝 Updating translation dictionary in app.py...")
    fixer.update_app_translations()
    
    # Fix all template files
    print("\n🔧 Fixing template files...")
    results = fixer.fix_all_templates()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎉 AUTOMATED TRANSLATION FIX COMPLETE!")
    print(f"✅ Fixed: {results['fixed']} files")
    print(f"⚪ Skipped: {results['skipped']} files")
    print(f"❌ Errors: {results['errors']} files")
    print("\n🌍 EXLIPA now has comprehensive Swahili translation support!")

if __name__ == '__main__':
    main()
