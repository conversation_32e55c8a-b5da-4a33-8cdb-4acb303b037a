{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}Usimamizi wa Webhooks{% else %}Webhook Management{% endif %} - EXLIPA
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">🔗 
                        {% if session.language == 'sw' %}
                            Usimamizi wa Webhooks
                        {% else %}
                            Webhook Management
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if session.language == 'sw' %}
                            Simamia webhooks kwa {{ company.company_name }}
                        {% else %}
                            Manage webhooks for {{ company.company_name }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('user_admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {% if session.language == 'sw' %}Rudi{% else %}Back{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if not has_webhook_access %}
    <!-- No Webhook Access -->
    <div class="row">
        <div class="col-12">
            <div class="glass-card p-5 text-center">
                <div class="mb-4">
                    <i class="fas fa-lock fa-3x text-muted"></i>
                </div>
                <h4 class="fw-bold mb-3">
                    {% if session.language == 'sw' %}Webhooks Hazipatikani{% else %}Webhooks Not Available{% endif %}
                </h4>
                <p class="text-muted mb-4">
                    {% if session.language == 'sw' %}
                        Webhooks zinapatikana kwa vipimo vya Biashara na Makampuni tu. 
                        Boresha mpango wako ili kupata ufikiaji wa webhooks.
                    {% else %}
                        Webhooks are available for Business and Enterprise tiers only. 
                        Upgrade your plan to access webhook functionality.
                    {% endif %}
                </p>
                
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="alert alert-info">
                            <h6 class="fw-bold">
                                {% if session.language == 'sw' %}Webhooks ni nini?{% else %}What are Webhooks?{% endif %}
                            </h6>
                            <p class="mb-0">
                                {% if session.language == 'sw' %}
                                    Webhooks ni njia ya kiotomatiki ya kupokea taarifa za matukio ya malipo katika mfumo wako. 
                                    Zinawezesha mfumo wako kupokea taarifa za haraka wakati malipo yanapohakikiwa.
                                {% else %}
                                    Webhooks are automated notifications that your system receives when payment events occur. 
                                    They enable real-time integration between EXLIPA and your applications.
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{{ url_for('pricing') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-up me-1"></i>
                        {% if session.language == 'sw' %}Boresha Mpango{% else %}Upgrade Plan{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Webhook Access Available -->
    <div class="row g-3">
        <!-- Webhook Stats -->
        <div class="col-md-4">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    {% if session.language == 'sw' %}Takwimu{% else %}Statistics{% endif %}
                </h5>
                
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-primary mb-1">{{ webhook_stats.total or 0 }}</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Jumla{% else %}Total{% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success mb-1">{{ webhook_stats.active or 0 }}</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Inatumika{% else %}Active{% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning mb-1">{{ webhook_stats.failed or 0 }}</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Imeshindwa{% else %}Failed{% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info mb-1">24/7</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Inatumika{% else %}Monitoring{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Webhook -->
        <div class="col-md-8">
            <div class="glass-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0">
                        <i class="fas fa-plus me-2"></i>
                        {% if session.language == 'sw' %}Unda Webhook{% else %}Create Webhook{% endif %}
                    </h5>
                </div>
                
                <form id="webhookForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}URL ya Webhook{% else %}Webhook URL{% endif %} *
                            </label>
                            <input type="url" class="form-control" id="webhookUrl" required
                                   placeholder="https://your-app.com/webhooks/exlipa">
                            <div class="form-text">
                                {% if session.language == 'sw' %}
                                    URL ambayo EXLIPA itatuma taarifa za matukio
                                {% else %}
                                    URL where EXLIPA will send event notifications
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Matukio{% else %}Events{% endif %} *
                            </label>
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="payment.created" id="event1">
                                    <label class="form-check-label" for="event1">
                                        {% if session.language == 'sw' %}Malipo Yameundwa{% else %}Payment Created{% endif %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="payment.confirmed" id="event2">
                                    <label class="form-check-label" for="event2">
                                        {% if session.language == 'sw' %}Malipo Yamehakikiwa{% else %}Payment Confirmed{% endif %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="payment.failed" id="event3">
                                    <label class="form-check-label" for="event3">
                                        {% if session.language == 'sw' %}Malipo Yameshindwa{% else %}Payment Failed{% endif %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="invoice.created" id="event4">
                                    <label class="form-check-label" for="event4">
                                        {% if session.language == 'sw' %}Ankara Imeundwa{% else %}Invoice Created{% endif %}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="invoice.paid" id="event5">
                                    <label class="form-check-label" for="event5">
                                        {% if session.language == 'sw' %}Ankara Imelipwa{% else %}Invoice Paid{% endif %}
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Siri ya Webhook{% else %}Webhook Secret{% endif %}
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="webhookSecret" 
                                       placeholder="{% if session.language == 'sw' %}Siri ya kuhakiki{% else %}Verification secret{% endif %}">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateSecret()">
                                    <i class="fas fa-random"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                {% if session.language == 'sw' %}
                                    Siri ya kuhakiki uthabiti wa webhook (inashauriwa)
                                {% else %}
                                    Secret for webhook verification (recommended)
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% if session.language == 'sw' %}Unda Webhook{% else %}Create Webhook{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Existing Webhooks -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-list me-2"></i>
                    {% if session.language == 'sw' %}Webhooks Zilizopo{% else %}Existing Webhooks{% endif %}
                </h5>
                
                <div id="webhooksList">
                    {% if webhook_stats.webhooks %}
                        {% for webhook in webhook_stats.webhooks %}
                        <div class="border rounded p-3 mb-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="fw-bold">{{ webhook.url }}</div>
                                    <small class="text-muted">
                                        {% if session.language == 'sw' %}Imeundwa{% else %}Created{% endif %}: 
                                        {{ webhook.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                <div class="col-md-3">
                                    {% if webhook.active %}
                                        <span class="badge bg-success">
                                            {% if session.language == 'sw' %}Inatumika{% else %}Active{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            {% if session.language == 'sw' %}Haifanyi kazi{% else %}Inactive{% endif %}
                                        </span>
                                    {% endif %}
                                    
                                    {% if webhook.failure_count > 0 %}
                                        <span class="badge bg-warning ms-1">
                                            {{ webhook.failure_count }} 
                                            {% if session.language == 'sw' %}makosa{% else %}failures{% endif %}
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="col-md-3 text-end">
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="testWebhook('{{ webhook.url }}')">
                                        <i class="fas fa-play"></i>
                                        {% if session.language == 'sw' %}Jaribu{% else %}Test{% endif %}
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteWebhook('{{ webhook.url }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <small class="text-muted">
                                    {% if session.language == 'sw' %}Matukio{% else %}Events{% endif %}: 
                                    {% for event in webhook.events %}
                                        <span class="badge bg-light text-dark me-1">{{ event.value }}</span>
                                    {% endfor %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-webhook fa-2x text-muted mb-3"></i>
                            <p class="text-muted">
                                {% if session.language == 'sw' %}
                                    Hakuna webhooks zilizotengenezwa bado
                                {% else %}
                                    No webhooks created yet
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Webhook Documentation -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-book me-2"></i>
                    {% if session.language == 'sw' %}Mafunzo ya Webhook{% else %}Webhook Documentation{% endif %}
                </h5>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold">
                            {% if session.language == 'sw' %}Muundo wa Ujumbe{% else %}Message Format{% endif %}
                        </h6>
                        <pre class="bg-light p-3 rounded small"><code>{
  "event": "payment.confirmed",
  "timestamp": "2024-01-15T10:30:00Z",
  "company_id": 123,
  "data": {
    "payment_id": 456,
    "amount": 50000,
    "customer_name": "John Doe",
    "status": "Confirmed"
  }
}</code></pre>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">
                            {% if session.language == 'sw' %}Uhakiki wa Uthabiti{% else %}Signature Verification{% endif %}
                        </h6>
                        <p class="small text-muted mb-2">
                            {% if session.language == 'sw' %}
                                Tumia header ya X-EXLIPA-Signature kuhakiki uthabiti wa ujumbe
                            {% else %}
                                Use X-EXLIPA-Signature header to verify message authenticity
                            {% endif %}
                        </p>
                        <pre class="bg-light p-3 rounded small"><code>const crypto = require('crypto');
const signature = req.headers['x-exlipa-signature'];
const payload = JSON.stringify(req.body);
const expected = crypto
  .createHmac('sha256', secret)
  .update(payload)
  .digest('hex');
const isValid = signature === `sha256=${expected}`;</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Generate random secret
function generateSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let secret = '';
    for (let i = 0; i < 32; i++) {
        secret += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('webhookSecret').value = secret;
}

// Create webhook
document.getElementById('webhookForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const url = document.getElementById('webhookUrl').value;
    const secret = document.getElementById('webhookSecret').value;
    const events = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                       .map(cb => cb.value);
    
    if (events.length === 0) {
        alert('{% if session.language == "sw" %}Tafadhali chagua angalau tukio moja{% else %}Please select at least one event{% endif %}');
        return;
    }
    
    try {
        const response = await fetch('/webhooks/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url, events, secret })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('{% if session.language == "sw" %}Webhook imeundwa kwa mafanikio!{% else %}Webhook created successfully!{% endif %}');
            location.reload();
        } else {
            alert('{% if session.language == "sw" %}Hitilafu{% else %}Error{% endif %}: ' + result.error);
        }
    } catch (error) {
        alert('{% if session.language == "sw" %}Hitilafu ya mtandao{% else %}Network error{% endif %}: ' + error.message);
    }
});

// Test webhook
function testWebhook(url) {
    alert('{% if session.language == "sw" %}Jaribio la webhook limeanza...{% else %}Webhook test started...{% endif %}');
    // Implement webhook testing
}

// Delete webhook
function deleteWebhook(url) {
    if (confirm('{% if session.language == "sw" %}Una uhakika unataka kufuta webhook hii?{% else %}Are you sure you want to delete this webhook?{% endif %}')) {
        // Implement webhook deletion
        alert('{% if session.language == "sw" %}Webhook imefutwa{% else %}Webhook deleted{% endif %}');
    }
}
</script>
{% endblock %}
