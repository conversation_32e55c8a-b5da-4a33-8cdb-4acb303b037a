{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}Shughuli za Wingi{% else %}Bulk Operations{% endif %} - EXLIPA
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">📦 
                        {% if session.language == 'sw' %}
                            Shughuli za Wingi
                        {% else %}
                            Bulk Operations
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if session.language == 'sw' %}
                            Fanya shughuli nyingi kwa wakati mmoja kwa {{ company.company_name }}
                        {% else %}
                            Perform multiple operations at once for {{ company.company_name }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('user_admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {% if session.language == 'sw' %}Rudi{% else %}Back{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Operation Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-4">
            <div class="glass-card p-4 h-100 hover-lift">
                <div class="text-center">
                    <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                    <h5 class="fw-bold">
                        {% if session.language == 'sw' %}Malipo ya Wingi{% else %}Bulk Payments{% endif %}
                    </h5>
                    <p class="text-muted small">
                        {% if session.language == 'sw' %}
                            Sasisha hali ya malipo mengi kwa wakati mmoja
                        {% else %}
                            Update status of multiple payments at once
                        {% endif %}
                    </p>
                    <button class="btn btn-primary" onclick="showBulkPayments()">
                        {% if session.language == 'sw' %}Anza{% else %}Start{% endif %}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="glass-card p-4 h-100 hover-lift">
                <div class="text-center">
                    <i class="fas fa-download fa-3x text-success mb-3"></i>
                    <h5 class="fw-bold">
                        {% if session.language == 'sw' %}Hamisha Data{% else %}Export Data{% endif %}
                    </h5>
                    <p class="text-muted small">
                        {% if session.language == 'sw' %}
                            Hamisha data ya malipo au ankara kwa mfumo mwingine
                        {% else %}
                            Export payment or invoice data to external systems
                        {% endif %}
                    </p>
                    <button class="btn btn-success" onclick="showDataExport()">
                        {% if session.language == 'sw' %}Hamisha{% else %}Export{% endif %}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="glass-card p-4 h-100 hover-lift">
                <div class="text-center">
                    <i class="fas fa-upload fa-3x text-info mb-3"></i>
                    <h5 class="fw-bold">
                        {% if session.language == 'sw' %}Leta Data{% else %}Import Data{% endif %}
                    </h5>
                    <p class="text-muted small">
                        {% if session.language == 'sw' %}
                            Leta data kutoka mifumo mingine kwa EXLIPA
                        {% else %}
                            Import data from external systems to EXLIPA
                        {% endif %}
                    </p>
                    <button class="btn btn-info" onclick="showDataImport()">
                        {% if session.language == 'sw' %}Leta{% else %}Import{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Operation Panels -->
    
    <!-- Bulk Payments Panel -->
    <div class="row" id="bulkPaymentsPanel" style="display: none;">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    {% if session.language == 'sw' %}Sasisha Malipo ya Wingi{% else %}Bulk Update Payments{% endif %}
                </h5>
                
                <form id="bulkPaymentForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Chagua Malipo{% else %}Select Payments{% endif %}
                            </label>
                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="selectAllPayments">
                                    <label class="form-check-label fw-bold" for="selectAllPayments">
                                        {% if session.language == 'sw' %}Chagua Yote{% else %}Select All{% endif %}
                                    </label>
                                </div>
                                <hr>
                                <div id="paymentsList">
                                    <!-- Payment list will be loaded here -->
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm" role="status"></div>
                                        <span class="ms-2">
                                            {% if session.language == 'sw' %}Inapakia...{% else %}Loading...{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Vitendo{% else %}Actions{% endif %}
                            </label>
                            
                            <div class="mb-3">
                                <label class="form-label">
                                    {% if session.language == 'sw' %}Badilisha Hali{% else %}Change Status{% endif %}
                                </label>
                                <select class="form-control" id="newStatus">
                                    <option value="">
                                        {% if session.language == 'sw' %}Chagua hali mpya{% else %}Select new status{% endif %}
                                    </option>
                                    <option value="Confirmed">
                                        {% if session.language == 'sw' %}Imehakikiwa{% else %}Confirmed{% endif %}
                                    </option>
                                    <option value="Pending">
                                        {% if session.language == 'sw' %}Inasubiri{% else %}Pending{% endif %}
                                    </option>
                                    <option value="Rejected">
                                        {% if session.language == 'sw' %}Imekataliwa{% else %}Rejected{% endif %}
                                    </option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">
                                    {% if session.language == 'sw' %}Maelezo{% else %}Notes{% endif %}
                                </label>
                                <textarea class="form-control" id="bulkNotes" rows="3" 
                                         placeholder="{% if session.language == 'sw' %}Maelezo ya ziada (si lazima){% else %}Additional notes (optional){% endif %}"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if session.language == 'sw' %}Sasisha{% else %}Update{% endif %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="hideBulkPayments()">
                                {% if session.language == 'sw' %}Ghairi{% else %}Cancel{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Data Export Panel -->
    <div class="row" id="dataExportPanel" style="display: none;">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-download me-2"></i>
                    {% if session.language == 'sw' %}Hamisha Data{% else %}Export Data{% endif %}
                </h5>
                
                <form id="exportForm">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Aina ya Data{% else %}Data Type{% endif %}
                            </label>
                            <select class="form-control" id="exportDataType" required>
                                <option value="">
                                    {% if session.language == 'sw' %}Chagua aina{% else %}Select type{% endif %}
                                </option>
                                <option value="payments">
                                    {% if session.language == 'sw' %}Malipo{% else %}Payments{% endif %}
                                </option>
                                <option value="invoices">
                                    {% if session.language == 'sw' %}Ankara{% else %}Invoices{% endif %}
                                </option>
                                <option value="customers">
                                    {% if session.language == 'sw' %}Wateja{% else %}Customers{% endif %}
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Tarehe ya Kuanza{% else %}From Date{% endif %}
                            </label>
                            <input type="date" class="form-control" id="exportFromDate" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Tarehe ya Mwisho{% else %}To Date{% endif %}
                            </label>
                            <input type="date" class="form-control" id="exportToDate" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Muundo wa Faili{% else %}File Format{% endif %}
                            </label>
                            <select class="form-control" id="exportFormat">
                                <option value="csv">CSV</option>
                                <option value="excel">Excel (.xlsx)</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Vipimo{% else %}Filters{% endif %}
                            </label>
                            <select class="form-control" id="exportFilters">
                                <option value="all">
                                    {% if session.language == 'sw' %}Yote{% else %}All Records{% endif %}
                                </option>
                                <option value="confirmed">
                                    {% if session.language == 'sw' %}Yamehakikiwa tu{% else %}Confirmed Only{% endif %}
                                </option>
                                <option value="pending">
                                    {% if session.language == 'sw' %}Yanasubiri tu{% else %}Pending Only{% endif %}
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-download me-1"></i>
                                {% if session.language == 'sw' %}Hamisha{% else %}Export{% endif %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="hideDataExport()">
                                {% if session.language == 'sw' %}Ghairi{% else %}Cancel{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Data Import Panel -->
    <div class="row" id="dataImportPanel" style="display: none;">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-upload me-2"></i>
                    {% if session.language == 'sw' %}Leta Data{% else %}Import Data{% endif %}
                </h5>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if session.language == 'sw' %}
                        Pakua template ya CSV ili kuona muundo sahihi wa data
                    {% else %}
                        Download CSV template to see the correct data format
                    {% endif %}
                </div>
                
                <form id="importForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Aina ya Data{% else %}Data Type{% endif %}
                            </label>
                            <select class="form-control" id="importDataType" required>
                                <option value="">
                                    {% if session.language == 'sw' %}Chagua aina{% else %}Select type{% endif %}
                                </option>
                                <option value="payments">
                                    {% if session.language == 'sw' %}Malipo{% else %}Payments{% endif %}
                                </option>
                                <option value="customers">
                                    {% if session.language == 'sw' %}Wateja{% else %}Customers{% endif %}
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label fw-bold">
                                {% if session.language == 'sw' %}Chagua Faili{% else %}Select File{% endif %}
                            </label>
                            <input type="file" class="form-control" id="importFile" accept=".csv,.xlsx" required>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="validateOnly">
                                <label class="form-check-label" for="validateOnly">
                                    {% if session.language == 'sw' %}
                                        Hakiki tu (usihifadhi data)
                                    {% else %}
                                        Validate only (don't save data)
                                    {% endif %}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-upload me-1"></i>
                                {% if session.language == 'sw' %}Leta{% else %}Import{% endif %}
                            </button>
                            <button type="button" class="btn btn-outline-primary ms-2" onclick="downloadTemplate()">
                                <i class="fas fa-download me-1"></i>
                                {% if session.language == 'sw' %}Pakua Template{% else %}Download Template{% endif %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="hideDataImport()">
                                {% if session.language == 'sw' %}Ghairi{% else %}Cancel{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Progress Modal -->
    <div class="modal fade" id="progressModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        {% if session.language == 'sw' %}Inaendelea...{% else %}Processing...{% endif %}
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <p id="progressText">
                        {% if session.language == 'sw' %}Tafadhali subiri...{% else %}Please wait...{% endif %}
                    </p>
                    <div class="progress">
                        <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide panels
function showBulkPayments() {
    hideAllPanels();
    document.getElementById('bulkPaymentsPanel').style.display = 'block';
    loadPaymentsList();
}

function showDataExport() {
    hideAllPanels();
    document.getElementById('dataExportPanel').style.display = 'block';
}

function showDataImport() {
    hideAllPanels();
    document.getElementById('dataImportPanel').style.display = 'block';
}

function hideAllPanels() {
    document.getElementById('bulkPaymentsPanel').style.display = 'none';
    document.getElementById('dataExportPanel').style.display = 'none';
    document.getElementById('dataImportPanel').style.display = 'none';
}

function hideBulkPayments() { hideAllPanels(); }
function hideDataExport() { hideAllPanels(); }
function hideDataImport() { hideAllPanels(); }

// Load payments list
function loadPaymentsList() {
    // Simulate loading payments
    setTimeout(() => {
        const paymentsList = document.getElementById('paymentsList');
        paymentsList.innerHTML = `
            <div class="form-check mb-2">
                <input class="form-check-input payment-checkbox" type="checkbox" value="1" id="payment1">
                <label class="form-check-label" for="payment1">
                    Payment #001 - John Doe - TSh 25,000
                </label>
            </div>
            <div class="form-check mb-2">
                <input class="form-check-input payment-checkbox" type="checkbox" value="2" id="payment2">
                <label class="form-check-label" for="payment2">
                    Payment #002 - Jane Smith - TSh 50,000
                </label>
            </div>
            <div class="form-check mb-2">
                <input class="form-check-input payment-checkbox" type="checkbox" value="3" id="payment3">
                <label class="form-check-label" for="payment3">
                    Payment #003 - Bob Johnson - TSh 15,000
                </label>
            </div>
        `;
    }, 1000);
}

// Select all payments
document.getElementById('selectAllPayments').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.payment-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// Form submissions
document.getElementById('bulkPaymentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const selectedPayments = Array.from(document.querySelectorAll('.payment-checkbox:checked'))
                                  .map(cb => cb.value);
    const newStatus = document.getElementById('newStatus').value;
    
    if (selectedPayments.length === 0) {
        alert('{% if session.language == "sw" %}Tafadhali chagua angalau malipo moja{% else %}Please select at least one payment{% endif %}');
        return;
    }
    
    if (!newStatus) {
        alert('{% if session.language == "sw" %}Tafadhali chagua hali mpya{% else %}Please select a new status{% endif %}');
        return;
    }
    
    showProgress('{% if session.language == "sw" %}Inasasisha malipo...{% else %}Updating payments...{% endif %}');
    
    // Simulate bulk update
    setTimeout(() => {
        hideProgress();
        alert(`{% if session.language == "sw" %}Malipo ${selectedPayments.length} yamesasishwa kwa mafanikio!{% else %}${selectedPayments.length} payments updated successfully!{% endif %}`);
        hideAllPanels();
    }, 3000);
});

document.getElementById('exportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    showProgress('{% if session.language == "sw" %}Inahamisha data...{% else %}Exporting data...{% endif %}');
    
    // Simulate export
    setTimeout(() => {
        hideProgress();
        alert('{% if session.language == "sw" %}Data imehamishwa kwa mafanikio!{% else %}Data exported successfully!{% endif %}');
        hideAllPanels();
    }, 2000);
});

document.getElementById('importForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const file = document.getElementById('importFile').files[0];
    if (!file) {
        alert('{% if session.language == "sw" %}Tafadhali chagua faili{% else %}Please select a file{% endif %}');
        return;
    }
    
    showProgress('{% if session.language == "sw" %}Inaleta data...{% else %}Importing data...{% endif %}');
    
    // Simulate import
    setTimeout(() => {
        hideProgress();
        alert('{% if session.language == "sw" %}Data imeingizwa kwa mafanikio!{% else %}Data imported successfully!{% endif %}');
        hideAllPanels();
    }, 4000);
});

// Progress modal
function showProgress(text) {
    document.getElementById('progressText').textContent = text;
    document.getElementById('progressBar').style.width = '0%';
    new bootstrap.Modal(document.getElementById('progressModal')).show();
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;
        document.getElementById('progressBar').style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

function hideProgress() {
    bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
}

function downloadTemplate() {
    const dataType = document.getElementById('importDataType').value;
    if (!dataType) {
        alert('{% if session.language == "sw" %}Tafadhali chagua aina ya data kwanza{% else %}Please select data type first{% endif %}');
        return;
    }
    
    // Create and download template
    const csvContent = dataType === 'payments' ? 
        'customer_name,amount,mobile_operator,transaction_id,notes\nJohn Doe,25000,M-Pesa,ABC123,Sample payment' :
        'name,email,phone,address\nJohn Doe,<EMAIL>,+255123456789,Dar es Salaam';
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${dataType}_template.csv`;
    a.click();
    URL.revokeObjectURL(url);
}
</script>
{% endblock %}
