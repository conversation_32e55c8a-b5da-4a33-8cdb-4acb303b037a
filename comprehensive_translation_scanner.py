#!/usr/bin/env python3
"""
Comprehensive Translation Scanner for EXLIPA
Finds ALL hardcoded English text that needs Swahili translation
"""

import os
import re
import glob
from typing import List, Dict, Tuple

class TranslationScanner:
    def __init__(self):
        self.hardcoded_patterns = [
            # Common English words that should be translated
            r'\b(Welcome|Login|Logout|Dashboard|Company|Admin|Master|User)\b',
            r'\b(Payment|Payments|Invoice|Invoices|Amount|Customer|Email|Phone)\b',
            r'\b(Submit|Confirm|Cancel|Edit|Delete|View|Manage|Create|Add|Update)\b',
            r'\b(Active|Inactive|Pending|Confirmed|Rejected|Approved|Locked)\b',
            r'\b(Name|Description|Status|Features|Actions|Settings|Profile)\b',
            r'\b(Search|Filter|Sort|Export|Import|Download|Upload|Save)\b',
            r'\b(Team|Members|Users|Companies|Organizations|Billing)\b',
            r'\b(Analytics|Reports|Statistics|Metrics|Performance)\b',
            r'\b(Help|Support|Contact|About|FAQ|Documentation)\b',
            r'\b(Password|Username|Remember|Forgot|Reset|Change)\b',
            r'\b(Success|Error|Warning|Info|Message|Notification)\b',
            r'\b(Home|Back|Next|Previous|Continue|Finish|Complete)\b',
            r'\b(Total|Count|Number|Quantity|Price|Cost|Fee|Charge)\b',
            r'\b(Date|Time|Today|Yesterday|Tomorrow|Week|Month|Year)\b',
            r'\b(All|None|Any|Some|Every|Each|Other|More|Less)\b',
        ]
        
        self.translation_map = {
            # Core UI Elements
            'Welcome': 'Karibu',
            'Login': 'Ingia',
            'Logout': 'Toka',
            'Dashboard': 'Dashibodi',
            'Company': 'Kampuni',
            'Admin': 'Msimamizi',
            'Master': 'Mkuu',
            'User': 'Mtumiaji',
            
            # Payment & Business
            'Payment': 'Malipo',
            'Payments': 'Malipo',
            'Invoice': 'Ankara',
            'Invoices': 'Ankara',
            'Amount': 'Kiasi',
            'Customer': 'Mteja',
            'Email': 'Barua pepe',
            'Phone': 'Simu',
            
            # Actions
            'Submit': 'Wasilisha',
            'Confirm': 'Thibitisha',
            'Cancel': 'Ghairi',
            'Edit': 'Hariri',
            'Delete': 'Futa',
            'View': 'Ona',
            'Manage': 'Simamia',
            'Create': 'Unda',
            'Add': 'Ongeza',
            'Update': 'Sasisha',
            
            # Status
            'Active': 'Hai',
            'Inactive': 'Haifanyi Kazi',
            'Pending': 'Inasubiri',
            'Confirmed': 'Imethibitishwa',
            'Rejected': 'Imekataliwa',
            'Approved': 'Imeidhinishwa',
            'Locked': 'Imefungwa',
            
            # Common Fields
            'Name': 'Jina',
            'Description': 'Maelezo',
            'Status': 'Hali',
            'Features': 'Vipengele',
            'Actions': 'Vitendo',
            'Settings': 'Mipangilio',
            'Profile': 'Wasifu',
            
            # Operations
            'Search': 'Tafuta',
            'Filter': 'Chuja',
            'Sort': 'Panga',
            'Export': 'Hamisha',
            'Import': 'Leta',
            'Download': 'Pakua',
            'Upload': 'Pakia',
            'Save': 'Hifadhi',
            
            # Organization
            'Team': 'Timu',
            'Members': 'Wanachama',
            'Users': 'Watumiaji',
            'Companies': 'Makampuni',
            'Organizations': 'Mashirika',
            'Billing': 'Malipo',
            
            # Analytics
            'Analytics': 'Uchambuzi',
            'Reports': 'Ripoti',
            'Statistics': 'Takwimu',
            'Metrics': 'Vipimo',
            'Performance': 'Utendaji',
            
            # Support
            'Help': 'Msaada',
            'Support': 'Msaada',
            'Contact': 'Wasiliana',
            'About': 'Kuhusu',
            'FAQ': 'Maswali Yanayoulizwa Mara kwa Mara',
            'Documentation': 'Nyaraka',
            
            # Authentication
            'Password': 'Nywila',
            'Username': 'Jina la Mtumiaji',
            'Remember': 'Kumbuka',
            'Forgot': 'Umesahau',
            'Reset': 'Weka upya',
            'Change': 'Badilisha',
            
            # Messages
            'Success': 'Mafanikio',
            'Error': 'Hitilafu',
            'Warning': 'Onyo',
            'Info': 'Taarifa',
            'Message': 'Ujumbe',
            'Notification': 'Arifa',
            
            # Navigation
            'Home': 'Nyumbani',
            'Back': 'Rudi',
            'Next': 'Ifuatayo',
            'Previous': 'Iliyotangulia',
            'Continue': 'Endelea',
            'Finish': 'Maliza',
            'Complete': 'Kamili',
            
            # Numbers & Quantities
            'Total': 'Jumla',
            'Count': 'Idadi',
            'Number': 'Nambari',
            'Quantity': 'Wingi',
            'Price': 'Bei',
            'Cost': 'Gharama',
            'Fee': 'Ada',
            'Charge': 'Malipo',
            
            # Time
            'Date': 'Tarehe',
            'Time': 'Muda',
            'Today': 'Leo',
            'Yesterday': 'Jana',
            'Tomorrow': 'Kesho',
            'Week': 'Wiki',
            'Month': 'Mwezi',
            'Year': 'Mwaka',
            
            # Quantifiers
            'All': 'Yote',
            'None': 'Hakuna',
            'Any': 'Yoyote',
            'Some': 'Baadhi',
            'Every': 'Kila',
            'Each': 'Kila',
            'Other': 'Nyingine',
            'More': 'Zaidi',
            'Less': 'Kidogo',
        }
    
    def scan_template(self, filepath: str) -> List[Tuple[int, str, str]]:
        """Scan a template file for hardcoded English text"""
        issues = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # Skip lines that already have translation functions
                if "t('" in line or 't("' in line or "session.language" in line:
                    continue
                
                # Check for hardcoded patterns
                for pattern in self.hardcoded_patterns:
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    for match in matches:
                        word = match.group()
                        if word in self.translation_map:
                            issues.append((line_num, word, line.strip()))
        
        except Exception as e:
            print(f"Error scanning {filepath}: {e}")
        
        return issues
    
    def scan_all_templates(self) -> Dict[str, List[Tuple[int, str, str]]]:
        """Scan all template files"""
        results = {}
        template_files = glob.glob('templates/*.html')
        
        for template_file in template_files:
            issues = self.scan_template(template_file)
            if issues:
                results[template_file] = issues
        
        return results
    
    def generate_report(self) -> str:
        """Generate a comprehensive report of translation issues"""
        results = self.scan_all_templates()
        
        if not results:
            return "✅ No hardcoded English text found! All templates are properly translated."
        
        report = "🔍 COMPREHENSIVE TRANSLATION SCAN REPORT\n"
        report += "=" * 60 + "\n\n"
        
        total_issues = sum(len(issues) for issues in results.values())
        report += f"📊 SUMMARY: Found {total_issues} hardcoded English texts in {len(results)} files\n\n"
        
        for filepath, issues in results.items():
            report += f"📄 {filepath}\n"
            report += "-" * 40 + "\n"
            
            for line_num, word, line_content in issues:
                report += f"  Line {line_num:3d}: '{word}' → '{self.translation_map.get(word, '???')}'\n"
                report += f"           {line_content[:80]}{'...' if len(line_content) > 80 else ''}\n\n"
        
        report += "\n🎯 RECOMMENDED ACTIONS:\n"
        report += "1. Replace hardcoded English text with conditional translations\n"
        report += "2. Use: {% if session.language == 'sw' %}Swahili{% else %}English{% endif %}\n"
        report += "3. Add missing translations to app.py translation dictionary\n"
        report += "4. Test language switching functionality\n"
        
        return report

def main():
    """Main function to run the comprehensive scan"""
    print("🌍 EXLIPA Comprehensive Translation Scanner")
    print("=" * 50)
    
    scanner = TranslationScanner()
    report = scanner.generate_report()
    
    # Save report to file
    with open('translation_scan_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(report)
    print(f"\n📝 Full report saved to: translation_scan_report.txt")

if __name__ == '__main__':
    main()
