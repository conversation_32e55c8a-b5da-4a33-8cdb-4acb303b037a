#!/usr/bin/env python3
"""
Quick fix for database to add missing columns
"""

import sqlite3
import os

def fix_database():
    """Add missing columns to client_company table"""
    db_path = 'payment_system.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if client_company table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_company'")
        if not cursor.fetchone():
            print("❌ client_company table not found!")
            return False
            
        print("✅ client_company table found")
        
        # Get existing columns
        cursor.execute("PRAGMA table_info(client_company)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"📋 Existing columns: {', '.join(columns)}")
        
        # Add missing columns one by one
        try:
            if "pricing_tier" not in columns:
                cursor.execute("ALTER TABLE client_company ADD COLUMN pricing_tier VARCHAR(20) DEFAULT 'Starter'")
                print("✅ Added pricing_tier")
            else:
                print("⏭️ pricing_tier already exists")
                
            if "primary_color" not in columns:
                cursor.execute("ALTER TABLE client_company ADD COLUMN primary_color VARCHAR(7) DEFAULT '#3b82f6'")
                print("✅ Added primary_color")
            else:
                print("⏭️ primary_color already exists")
                
            if "secondary_color" not in columns:
                cursor.execute("ALTER TABLE client_company ADD COLUMN secondary_color VARCHAR(7) DEFAULT '#1d4ed8'")
                print("✅ Added secondary_color")
            else:
                print("⏭️ secondary_color already exists")
                
            if "font_family" not in columns:
                cursor.execute("ALTER TABLE client_company ADD COLUMN font_family VARCHAR(50) DEFAULT 'Inter'")
                print("✅ Added font_family")
            else:
                print("⏭️ font_family already exists")
                
            if "template_style" not in columns:
                cursor.execute("ALTER TABLE client_company ADD COLUMN template_style VARCHAR(50) DEFAULT 'basic'")
                print("✅ Added template_style")
            else:
                print("⏭️ template_style already exists")
                
            conn.commit()
            print("✅ Migration completed!")
            
            # Verify
            cursor.execute("PRAGMA table_info(client_company)")
            final_columns = [row[1] for row in cursor.fetchall()]
            print(f"📊 Final columns: {', '.join(final_columns)}")
            
            return True
            
        except sqlite3.Error as e:
            print(f"❌ SQLite error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 EXLIPA Database Fix")
    print("=" * 40)
    
    if fix_database():
        print("\n🎉 Database fix completed successfully!")
        print("You can now restart the server and use the tier-based landing page builder.")
    else:
        print("\n❌ Database fix failed!")
