#!/usr/bin/env python3
"""
Payment models for EXLIPA
"""

from datetime import datetime
from .base import db, BaseModel, ValidationMixin
from utils.validators import InputValidator, ValidationError

class Invoice(BaseModel, ValidationMixin):
    """Invoice model"""
    
    __tablename__ = 'invoice'
    
    # Invoice details
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    
    # Financial details
    amount = db.Column(db.Float, nullable=False)
    service_description = db.Column(db.Text, nullable=False)
    
    # Status and dates
    status = db.Column(db.String(20), default='Unpaid')  # Unpaid, Paid, Overdue, Cancelled
    due_date = db.Column(db.DateTime)
    
    # Audit
    created_by = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('user.id'))
    company_id = db.Column(db.Integer, db.<PERSON>Key('client_company.id'))  # Added for proper association
    
    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by])
    company = db.relationship('ClientCompany', foreign_keys=[company_id])
    
    # Table constraints
    __table_args__ = (
        db.CheckConstraint('amount > 0', name='check_positive_amount'),
    )
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'
    
    def validate(self):
        """Validate invoice data"""
        # Validate invoice number
        if not self.invoice_number:
            raise ValidationError("Invoice number is required")
        
        self.invoice_number = InputValidator.sanitize_string(self.invoice_number, 50)
        
        # Validate customer name
        if not self.customer_name:
            raise ValidationError("Customer name is required")
        
        self.customer_name = InputValidator.sanitize_string(self.customer_name, 100)
        
        # Validate email if provided
        if self.customer_email:
            self.customer_email = InputValidator.validate_email(self.customer_email)
        
        # Validate phone if provided
        if self.customer_phone:
            self.customer_phone = InputValidator.validate_phone(self.customer_phone)
        
        # Validate amount
        if not self.amount:
            raise ValidationError("Amount is required")
        
        self.amount = InputValidator.validate_amount(self.amount)
        
        # Validate service description
        if not self.service_description:
            raise ValidationError("Service description is required")
        
        self.service_description = InputValidator.sanitize_string(self.service_description, 1000)
    
    def is_overdue(self):
        """Check if invoice is overdue"""
        if self.due_date and self.status == 'Unpaid':
            return datetime.utcnow() > self.due_date
        return False
    
    def mark_as_paid(self):
        """Mark invoice as paid"""
        self.status = 'Paid'
        return self.save()
    
    def mark_as_overdue(self):
        """Mark invoice as overdue"""
        self.status = 'Overdue'
        return self.save()
    
    @classmethod
    def generate_invoice_number(cls):
        """Generate unique invoice number"""
        import random
        import string
        
        # Get current count for sequence
        count = cls.query.count() + 1
        
        # Generate format: INV-YYYY-NNNN
        year = datetime.now().year
        sequence = f"{count:04d}"
        
        return f"INV-{year}-{sequence}"
    
    @classmethod
    def get_unpaid_invoices(cls, company_id=None):
        """Get unpaid invoices"""
        query = cls.query.filter_by(status='Unpaid')
        if company_id:
            query = query.filter_by(company_id=company_id)
        return query.all()
    
    @classmethod
    def get_overdue_invoices(cls, company_id=None):
        """Get overdue invoices"""
        query = cls.query.filter_by(status='Overdue')
        if company_id:
            query = query.filter_by(company_id=company_id)
        return query.all()

class PaymentConfirmation(BaseModel, ValidationMixin):
    """Payment confirmation model with enhanced validation"""
    
    __tablename__ = 'payment_confirmation'
    
    # Associations - at least one must be present
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=True)
    client_company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=True)
    pricing_tier_id = db.Column(db.Integer, db.ForeignKey('pricing_tier.id'), nullable=True)
    
    # Customer details
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(120), nullable=True)
    mobile_money_sender_name = db.Column(db.String(100), nullable=False)
    
    # Financial details
    amount = db.Column(db.Float, nullable=False)
    transaction_fee = db.Column(db.Float, default=0.0)
    net_amount = db.Column(db.Float, nullable=False)
    
    # Mobile money details
    mobile_operator = db.Column(db.String(50), nullable=False)
    transaction_id = db.Column(db.String(100), nullable=False, unique=True)
    service_description = db.Column(db.Text)
    
    # Status and processing
    status = db.Column(db.String(20), default='Pending')  # Pending, Confirmed, Rejected, Processed
    rejection_reason = db.Column(db.Text)
    internal_notes = db.Column(db.Text)
    
    # SMS verification fields
    sms_sender = db.Column(db.String(20))
    sms_amount = db.Column(db.Float)
    sms_sender_name = db.Column(db.String(100))
    sms_transaction_ref = db.Column(db.String(100))
    admin_verification_notes = db.Column(db.Text)
    
    # Processing details
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    processed_at = db.Column(db.DateTime)
    processed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    receipt_generated = db.Column(db.Boolean, default=False)
    fee_calculated = db.Column(db.Boolean, default=False)
    
    # Relationships will be defined after importing other models
    # invoice = db.relationship('Invoice', backref='payment_confirmations')
    # client_company = db.relationship('ClientCompany', backref='payment_confirmations')
    # pricing_tier = db.relationship('PricingTier', backref='payment_confirmations')
    # processor = db.relationship('User', foreign_keys=[processed_by])
    
    # Table constraints
    __table_args__ = (
        db.CheckConstraint('amount >= 1000', name='check_minimum_amount'),
        db.CheckConstraint('amount <= 10000000', name='check_maximum_amount'),
        db.CheckConstraint(
            'invoice_id IS NOT NULL OR client_company_id IS NOT NULL',
            name='check_payment_association'
        ),
    )
    
    def __repr__(self):
        return f'<PaymentConfirmation {self.transaction_id}>'
    
    def validate(self):
        """Validate payment confirmation data"""
        # Validate customer name
        if not self.customer_name:
            raise ValidationError("Customer name is required")
        
        self.customer_name = InputValidator.sanitize_string(self.customer_name, 100)
        
        # Validate mobile money sender name
        if not self.mobile_money_sender_name:
            raise ValidationError("Mobile money sender name is required")
        
        self.mobile_money_sender_name = InputValidator.sanitize_string(self.mobile_money_sender_name, 100)
        
        # Validate email if provided
        if self.customer_email:
            self.customer_email = InputValidator.validate_email(self.customer_email)
        
        # Validate amount
        if not self.amount:
            raise ValidationError("Amount is required")
        
        self.amount = InputValidator.validate_amount(self.amount)
        
        # Validate mobile operator
        if not self.mobile_operator:
            raise ValidationError("Mobile operator is required")
        
        self.mobile_operator = InputValidator.validate_mobile_operator(self.mobile_operator)
        
        # Validate transaction ID
        if not self.transaction_id:
            raise ValidationError("Transaction ID is required")
        
        self.transaction_id = InputValidator.validate_transaction_id(self.transaction_id, self.mobile_operator)
        
        # Validate status
        if self.status:
            self.status = InputValidator.validate_payment_status(self.status)
        
        # Validate association
        if not self.invoice_id and not self.client_company_id:
            raise ValidationError("Payment must be associated with either an invoice or company")
    
    def validate_amount(self):
        """Validate payment amount"""
        if self.amount < 1000:
            raise ValidationError("Minimum payment amount is TZS 1,000")
        if self.amount > 10000000:
            raise ValidationError("Maximum payment amount is TZS 10,000,000")
    
    def validate_transaction_id_format(self):
        """Validate transaction ID format"""
        from services.payment_verifier import MobileMoneyVerifier
        verifier = MobileMoneyVerifier()
        
        if not verifier.validate_transaction_id_format(self.transaction_id, self.mobile_operator):
            raise ValidationError(f"Invalid transaction ID format for {self.mobile_operator}")
    
    def validate_mobile_money_name(self):
        """Validate mobile money sender name"""
        if not self.mobile_money_sender_name or len(self.mobile_money_sender_name.strip()) < 2:
            raise ValidationError("Mobile money sender name must be at least 2 characters")
    
    def confirm_payment(self, processed_by_id, notes=None):
        """Confirm the payment"""
        self.status = 'Confirmed'
        self.processed_at = datetime.utcnow()
        self.processed_by = processed_by_id
        
        if notes:
            self.internal_notes = notes
        
        # Mark associated invoice as paid
        if self.invoice:
            self.invoice.mark_as_paid()
        
        return self.save()
    
    def reject_payment(self, processed_by_id, reason):
        """Reject the payment"""
        self.status = 'Rejected'
        self.processed_at = datetime.utcnow()
        self.processed_by = processed_by_id
        self.rejection_reason = reason
        
        return self.save()
    
    def get_reference_number(self):
        """Get formatted reference number"""
        return f"REF{self.id:06d}"
    
    @classmethod
    def get_pending_payments(cls):
        """Get pending payment confirmations"""
        return cls.query.filter_by(status='Pending').order_by(cls.submitted_at.desc()).all()
    
    @classmethod
    def get_by_transaction_id(cls, transaction_id):
        """Get payment by transaction ID"""
        return cls.query.filter_by(transaction_id=transaction_id).first()
    
    @classmethod
    def check_duplicate_transaction(cls, transaction_id, mobile_operator):
        """Check if transaction ID already exists"""
        return cls.query.filter_by(
            transaction_id=transaction_id,
            mobile_operator=mobile_operator
        ).first() is not None
