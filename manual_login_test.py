#!/usr/bin/env python3
"""
Manual login test to debug authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, simple_rate_limit, sanitize_input
from werkzeug.security import check_password_hash

def manual_login_test():
    """Manually test the login process"""
    with app.app_context():
        print("=== MANUAL LOGIN TEST ===")
        
        # Test credentials
        username = 'admin'
        password = 'admin123'
        
        print(f"Testing login for: {username} / {password}")
        
        # Step 1: Rate limiting check
        client_ip = '127.0.0.1'
        rate_limit_key = f"login_master_{client_ip}"
        rate_limit_ok = simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15)
        print(f"1. Rate limit check: {rate_limit_ok}")
        
        # Step 2: Sanitize input
        clean_username = sanitize_input(username, 80)
        print(f"2. Sanitized username: '{clean_username}'")
        
        # Step 3: Check if username and password provided
        has_credentials = bool(clean_username and password)
        print(f"3. Has credentials: {has_credentials}")
        
        # Step 4: Find user with role check
        user = User.query.filter_by(username=clean_username, is_active=True, role='master_admin').first()
        print(f"4. User found: {user is not None}")
        if user:
            print(f"   User ID: {user.id}")
            print(f"   Username: {user.username}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
        
        # Step 5: Check if account is locked
        if user:
            is_locked = user.is_account_locked()
            print(f"5. Account locked: {is_locked}")
            print(f"   Failed attempts: {user.failed_login_attempts}")
            print(f"   Locked until: {user.account_locked_until}")
        
        # Step 6: Check password
        if user:
            password_valid = check_password_hash(user.password_hash, password)
            print(f"6. Password valid: {password_valid}")
        
        # Step 7: Overall result
        if user and not user.is_account_locked() and check_password_hash(user.password_hash, password):
            print("✅ LOGIN SHOULD SUCCEED")
        else:
            print("❌ LOGIN SHOULD FAIL")
            if not user:
                print("   Reason: User not found")
            elif user.is_account_locked():
                print("   Reason: Account locked")
            elif not check_password_hash(user.password_hash, password):
                print("   Reason: Invalid password")

if __name__ == '__main__':
    manual_login_test()
