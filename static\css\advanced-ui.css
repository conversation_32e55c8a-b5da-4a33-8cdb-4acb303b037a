/* EXLIPA Advanced UI/UX Styles - Improved Accessibility */

:root {
    /* High Contrast Color Scheme */
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --secondary-gradient: linear-gradient(135deg, #64748b 0%, #475569 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #047857 100%);
    --warning-gradient: linear-gradient(135deg, #d97706 0%, #c2410c 100%);
    --danger-gradient: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);

    /* Solid Backgrounds for Better Readability */
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-bg-dark: rgba(30, 41, 59, 0.95);
    --glass-border: rgba(226, 232, 240, 0.8);
    --glass-border-dark: rgba(148, 163, 184, 0.3);

    /* Enhanced Shadows */
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-heavy: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;

    /* Transitions */
    --transition: all 0.2s ease;
    --transition-fast: all 0.15s ease-out;
    --transition-slow: all 0.3s ease-in-out;
}

/* Enhanced Typography - Better Contrast */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    min-height: 100vh;
    line-height: 1.6;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Improved Glass Cards - High Contrast */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    color: #1e293b;
}

/* Ensure text is always visible */
.glass-card h1,
.glass-card h2,
.glass-card h3,
.glass-card h4,
.glass-card h5,
.glass-card h6 {
    color: #1e293b !important;
    font-weight: 600;
}

.glass-card p,
.glass-card span,
.glass-card div {
    color: #475569 !important;
}

.glass-card .text-white {
    color: #ffffff !important;
}

.glass-card .text-muted {
    color: #64748b !important;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.3), transparent);
    opacity: 0;
    transition: var(--transition);
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: rgba(37, 99, 235, 0.2);
    background: rgba(255, 255, 255, 0.98);
}

.glass-card:hover::before {
    opacity: 1;
}

/* Dark Glass Cards for Special Sections */
.glass-card-dark {
    background: var(--glass-bg-dark);
    border: 1px solid var(--glass-border-dark);
    color: #ffffff;
}

.glass-card-dark h1,
.glass-card-dark h2,
.glass-card-dark h3,
.glass-card-dark h4,
.glass-card-dark h5,
.glass-card-dark h6 {
    color: #ffffff !important;
}

.glass-card-dark p,
.glass-card-dark span,
.glass-card-dark div {
    color: #e2e8f0 !important;
}

/* Dashboard & Navigation Improvements */
.navbar {
    background-color: #ffffff !important;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    color: #2563eb !important;
    font-weight: 700;
}

.navbar-nav .nav-link {
    color: #475569 !important;
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    color: #2563eb !important;
    background-color: #f1f5f9;
    border-radius: 6px;
}

.dropdown-menu {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    color: #475569 !important;
}

.dropdown-item:hover {
    background-color: #f1f5f9;
    color: #2563eb !important;
}

/* Dashboard Cards */
.dashboard-card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.dashboard-card h1,
.dashboard-card h2,
.dashboard-card h3,
.dashboard-card h4,
.dashboard-card h5,
.dashboard-card h6 {
    color: #1e293b !important;
}

.dashboard-card p,
.dashboard-card span {
    color: #64748b !important;
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.3s;
}

.btn:hover::before {
    left: 100%;
}

/* Text Visibility Utilities */
.text-high-contrast {
    color: #1e293b !important;
    font-weight: 500;
}

.text-white-contrast {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.bg-white-solid {
    background-color: #ffffff !important;
    color: #1e293b !important;
}

.bg-dark-solid {
    background-color: #1e293b !important;
    color: #ffffff !important;
}

/* Gradient Text - High Contrast */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.elegant-gradient-text {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Hero Section Improvements */
.hero-section {
    background: var(--primary-gradient);
    color: #ffffff;
    padding: 4rem 0;
    position: relative;
}

.hero-section h1,
.hero-section h2,
.hero-section h3 {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section p {
    color: #e2e8f0 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Button Improvements */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    color: #ffffff !important;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
}

.btn-secondary {
    background: var(--secondary-gradient);
    border: none;
    color: #ffffff !important;
    font-weight: 500;
}

.btn-success {
    background: var(--success-gradient);
    border: none;
    color: #ffffff !important;
    font-weight: 500;
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: #ffffff !important;
    font-weight: 500;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
    color: #ffffff !important;
    font-weight: 500;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(250, 112, 154, 0.6);
    color: white;
}

/* Enhanced Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 20px;
    margin: 0 0.25rem;
    padding: 0.5rem 1rem !important;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link:hover::after {
    width: 80%;
}

/* Enhanced Dropdowns */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: 8px;
    margin: 0.125rem 0;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(4px);
}

/* Enhanced Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-weight: 400;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(45deg, #007bff, #6f42c1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.gradient-text-success {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.gradient-text-warning {
    background: var(--warning-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Tables */
.table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.table td {
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Enhanced Badges */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px);
    border-left: 4px solid;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: #28a745;
    color: #155724;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
    color: #856404;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .glass-card {
        margin: 0.5rem;
        border-radius: var(--border-radius);
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .container-fluid {
        padding: 0.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .glass-card {
        background: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .form-control {
        background: rgba(0, 0, 0, 0.2);
        color: white;
    }
    
    .table {
        background: rgba(0, 0, 0, 0.1);
    }
}

/* Accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.blur-bg {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.glass-border {
    border: 1px solid var(--glass-border);
}

.rounded-xl {
    border-radius: var(--border-radius-xl) !important;
}

/* HIGH CONTRAST OVERRIDES - IMPROVED VISIBILITY */
/* These styles override the glass morphism for better readability */

/* Force high contrast for all text elements */
.glass-card,
.glass-card *,
.card,
.card * {
    color: #1e293b !important;
}

.glass-card h1,
.glass-card h2,
.glass-card h3,
.glass-card h4,
.glass-card h5,
.glass-card h6,
.card h1,
.card h2,
.card h3,
.card h4,
.card h5,
.card h6 {
    color: #1e293b !important;
    font-weight: 600 !important;
}

/* Override glass backgrounds for solid, readable backgrounds */
.glass-card {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(5px) !important;
    border: 1px solid #e5e7eb !important;
}

.card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

/* Force readable form elements */
.form-control,
.form-select {
    background: #ffffff !important;
    color: #1e293b !important;
    border: 1px solid #d1d5db !important;
}

.form-control:focus,
.form-select:focus {
    background: #ffffff !important;
    color: #1e293b !important;
    border-color: #2563eb !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* Force readable table elements */
.table {
    background: #ffffff !important;
    color: #1e293b !important;
}

.table th {
    background: #f8fafc !important;
    color: #374151 !important;
    border-bottom: 2px solid #e5e7eb !important;
}

.table td {
    color: #1e293b !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.table-hover tbody tr:hover {
    background: #f8fafc !important;
    color: #1e293b !important;
}

/* Force readable navigation */
.navbar {
    background: #ffffff !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.navbar-brand {
    color: #2563eb !important;
}

.nav-link {
    color: #475569 !important;
}

.nav-link:hover {
    color: #2563eb !important;
    background: #f1f5f9 !important;
}

/* Force readable dropdowns */
.dropdown-menu {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

.dropdown-item {
    color: #475569 !important;
}

.dropdown-item:hover {
    background: #f1f5f9 !important;
    color: #2563eb !important;
}

/* Ensure white text stays white on colored backgrounds */
.text-white,
.text-white *,
.btn .text-white,
.badge .text-white {
    color: #ffffff !important;
}

/* Ensure colored backgrounds have proper text contrast */
.bg-primary,
.bg-primary *,
.btn-primary,
.btn-primary * {
    color: #ffffff !important;
}

.bg-success,
.bg-success *,
.btn-success,
.btn-success * {
    color: #ffffff !important;
}

.bg-warning,
.bg-warning *,
.btn-warning,
.btn-warning * {
    color: #ffffff !important;
}

.bg-danger,
.bg-danger *,
.btn-danger,
.btn-danger * {
    color: #ffffff !important;
}

/* Hero sections with gradients */
.hero-section,
.hero-section * {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Sidebar sections */
.sidebar-section {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    color: #1e293b !important;
}

.sidebar-section h1,
.sidebar-section h2,
.sidebar-section h3,
.sidebar-section h4,
.sidebar-section h5,
.sidebar-section h6 {
    color: #1e293b !important;
}

/* Footer */
footer {
    background: #ffffff !important;
    color: #64748b !important;
    border-top: 1px solid #e5e7eb !important;
}
