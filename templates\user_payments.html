{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    My Malipo - EXLIPA
{%- else -%}
    My Payments - EXLIPA
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold gradient-text">
            <i class="fas fa-credit-card me-2"></i>My Payments
        </h2>
        <div>
            <a href="/user-admin" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
            <a href="{{ url_for('check_payment_status') }}" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>Check Payment Status
            </a>
        </div>
    </div>

    {% if company %}
    <div class="glass-card p-3 mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1">{{ company.company_name }}</h5>
                <p class="text-muted mb-0">{{ company.company_email }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-success">
                    {{ company.pricing_tier.name if company.pricing_tier else 'Free' }} Tier
                </span>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="glass-card">
        <div class="card-header bg-transparent">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Payment History
            </h5>
        </div>
        <div class="card-body">
            {% if payments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Payment ID</th>
                            <th>Customer</th>
                            <th>{{ t('Amount') }}</th>
                            <th>{{ t('Payment Method') }}</th>
                            <th>{{ t('Status') }}</th>
                            <th>{{ t('Date') }}</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>
                                <strong>{{ payment.id }}</strong>
                            </td>
                            <td>{{ payment.customer_name or 'N/A' }}</td>
                            <td>
                                <strong>TZS {{ "{:,.2f}".format(payment.amount) }}</strong>
                            </td>
                            <td>
                                {% if payment.payment_method %}
                                    <span class="badge bg-info">{{ payment.payment_method }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if payment.status == 'Confirmed' %}
                                    <span class="badge bg-success">{{ t('Confirmed') }}</span>
                                {% elif payment.status == 'Pending' %}
                                    <span class="badge bg-warning">{{ t('Pending') }}</span>
                                {% elif payment.status == 'Failed' %}
                                    <span class="badge bg-danger">Failed</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ payment.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ payment.submitted_at.strftime('%Y-%m-%d %H:%M') if payment.submitted_at else 'N/A' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" 
                                            onclick="showPaymentDetails({{ payment.id }})" 
                                            title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if payment.status == 'Confirmed' %}
                                    <button class="btn btn-outline-success" 
                                            onclick="downloadReceipt({{ payment.id }})" 
                                            title="Download Receipt">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Payments Yet</h5>
                <p class="text-muted">You haven't received any payments yet.</p>
                <div class="d-flex gap-2 justify-content-center">
                    <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-file-invoice me-1"></i>Create Invoice
                    </a>
                    {% if company %}
                    <a href="{{ url_for('advanced_landing', company_id=company.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-globe me-1"></i>Share Payment Link
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    {% if payments %}
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">Total Payments</h6>
                <h4 class="mb-0">{{ payments|length }}</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">{{ t('Confirmed') }}</h6>
                <h4 class="mb-0 text-success">{{ payments|selectattr('status', 'equalto', 'Confirmed')|list|length }}</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">{{ t('Pending') }}</h6>
                <h4 class="mb-0 text-warning">{{ payments|selectattr('status', 'equalto', 'Pending')|list|length }}</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">Total Amount</h6>
                <h4 class="mb-0 text-primary">
                    TZS {{ "{:,.2f}".format(payments|selectattr('status', 'equalto', 'Confirmed')|map(attribute='amount')|sum) }}
                </h4>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- Payment details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function showPaymentDetails(paymentId) {
    // Show payment details in modal
    fetch(`/api/payment-details/${paymentId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('paymentDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>Payment ID:</strong></div>
                    <div class="col-6">${data.id}</div>
                    <div class="col-6"><strong>Amount:</strong></div>
                    <div class="col-6">TZS ${data.amount}</div>
                    <div class="col-6"><strong>Status:</strong></div>
                    <div class="col-6"><span class="badge bg-${data.status === 'Confirmed' ? 'success' : 'warning'}">${data.status}</span></div>
                    <div class="col-6"><strong>Method:</strong></div>
                    <div class="col-6">${data.payment_method || 'N/A'}</div>
                    <div class="col-6"><strong>Date:</strong></div>
                    <div class="col-6">${data.submitted_at}</div>
                </div>
            `;
            new bootstrap.Modal(document.getElementById('paymentDetailsModal')).show();
        })
        .catch(error => {
            alert('Error loading payment details');
        });
}

function downloadReceipt(paymentId) {
    window.open(`/download-receipt/${paymentId}`, '_blank');
}
</script>
{% endblock %}
