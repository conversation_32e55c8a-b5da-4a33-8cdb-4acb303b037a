{% extends "base.html" %}

{% block title %}Invoice {{ invoice.invoice_number }} - {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <!-- {%- if session.language == "sw" -%}Ankara{%- else -%}Invoice{%- endif -%} Header -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>Invoice {{ invoice.invoice_number }}
                    <span class="badge 
                        {% if invoice.status == 'Unpaid' %}bg-warning text-dark
                        {% elif invoice.status == 'Paid' %}bg-success
                        {% elif invoice.status == 'Overdue' %}bg-danger
                        {% else %}bg-secondary{% endif %} ms-2">
                        {{ invoice.status }}
                    </span>
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Bill To:</h5>
                        <address>
                            <strong>{{ invoice.customer_name }}</strong><br>
                            {% if invoice.customer_email %}
                                <i class="fas fa-envelope me-1"></i>{{ invoice.customer_email }}<br>
                            {% endif %}
                            {% if invoice.customer_phone %}
                                <i class="fas fa-phone me-1"></i>{{ invoice.customer_phone }}<br>
                            {% endif %}
                        </address>
                    </div>
                    <div class="col-md-6 text-end">
                        <p>
                            <strong>Invoice {%- if session.language == "sw" -%}Tarehe{%- else -%}Date{%- endif -%}:</strong> {{ invoice.created_at.strftime('%d/%m/%Y') }}<br>
                            <strong>Due {%- if session.language == "sw" -%}Tarehe{%- else -%}Date{%- endif -%}:</strong> 
                            <span class="{% if invoice.due_date < now and invoice.status == 'Unpaid' %}text-danger{% endif %}">
                                {{ invoice.due_date.strftime('%d/%m/%Y') }}
                            </span><br>
                            <strong>{%- if session.language == "sw" -%}Hali{%- else -%}Status{%- endif -%}:</strong> 
                            <span class="badge 
                                {% if invoice.status == 'Unpaid' %}bg-warning text-dark
                                {% elif invoice.status == 'Paid' %}bg-success
                                {% elif invoice.status == 'Overdue' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {{ invoice.status }}
                            </span>
                        </p>
                    </div>
                </div>

                <hr>

                <!-- {%- if session.language == "sw" -%}Ankara{%- else -%}Invoice{%- endif -%} Details -->
                <div class="table-responsive">
                    <table class="table">
                        <thead class="table-light">
                            <tr>
                                <th>{{ t('{%- if session.language == "sw" -%}Maelezo{%- else -%}Description{%- endif -%}') }}</th>
                                {% if invoice.cart_items %}
                                <th class="text-center">Qty</th>
                                {% endif %}
                                <th class="text-end">{{ t('{%- if session.language == "sw" -%}Kiasi{%- else -%}Amount{%- endif -%}') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if invoice.cart_items %}
                                {% set cart_data = invoice.cart_items|fromjson %}
                                {% for item in cart_data %}
                                <tr>
                                    <td>{{ item.name }}</td>
                                    <td class="text-center">{{ item.quantity }}</td>
                                    <td class="text-end">TZS {{ "{:,.0f}"|format(item.price * item.quantity) }}</td>
                                </tr>
                                {% endfor %}
                                
                                {% if invoice.subtotal %}
                                <tr>
                                    <td colspan="2" class="text-end"><strong>Subtotal:</strong></td>
                                    <td class="text-end">TZS {{ "{:,.0f}"|format(invoice.subtotal) }}</td>
                                </tr>
                                {% endif %}
                                
                                {% if invoice.discount_amount and invoice.discount_amount > 0 %}
                                <tr>
                                    <td colspan="2" class="text-end text-success"><strong>Discount:</strong></td>
                                    <td class="text-end text-success">-TZS {{ "{:,.0f}"|format(invoice.discount_amount) }}</td>
                                </tr>
                                {% endif %}
                                
                                {% if invoice.tax_amount and invoice.tax_amount > 0 %}
                                <tr>
                                    <td colspan="2" class="text-end"><strong>Tax:</strong></td>
                                    <td class="text-end">TZS {{ "{:,.0f}"|format(invoice.tax_amount) }}</td>
                                </tr>
                                {% endif %}
                                
                                {% if invoice.shipping_cost and invoice.shipping_cost > 0 %}
                                <tr>
                                    <td colspan="2" class="text-end"><strong>Shipping:</strong></td>
                                    <td class="text-end">TZS {{ "{:,.0f}"|format(invoice.shipping_cost) }}</td>
                                </tr>
                                {% endif %}
                            {% else %}
                                <tr>
                                    <td>{{ invoice.service_description }}</td>
                                    <td class="text-end"><strong>TZS {{ "{:,.0f}"|format(invoice.amount) }}</strong></td>
                                </tr>
                            {% endif %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th {% if invoice.cart_items %}colspan="2"{% endif %}>Total {%- if session.language == "sw" -%}Kiasi{%- else -%}Amount{%- endif -%} Due:</th>
                                <th class="text-end">
                                    <h4 class="text-primary">TZS {{ "{:,.0f}"|format(invoice.amount) }}</h4>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} {%- if session.language == "sw" -%}Vitendo{%- else -%}Actions{%- endif -%} -->
        {% if invoice.status == 'Unpaid' or invoice.status == 'Overdue' %}
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>Ready to Pay?
                </h5>
            </div>
            <div class="card-body text-center">
                <p class="lead">Choose how you'd like to proceed with payment:</p>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <i class="fas fa-mobile-alt fa-2x text-primary mb-3"></i>
                                <h6>View {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Instructions</h6>
                                <p class="small">See step-by-step instructions for mobile money payment</p>
                                <a href="{{ url_for('payment_instructions_with_invoice', invoice_number=invoice.invoice_number) }}" 
                                   class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-1"></i>Payment Guide
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                                <h6>Already Paid?</h6>
                                <p class="small">Confirm your payment using your transaction ID</p>
                                <a href="{{ url_for('confirm_payment_with_invoice', invoice_number=invoice.invoice_number) }}" 
                                   class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>Confirm Payment
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Important:</strong> Please use invoice number <code>{{ invoice.invoice_number }}</code> 
                    as reference when making your mobile money payment.
                </div>
            </div>
        </div>
        {% elif invoice.status == 'Paid' %}
        <div class="card mt-4 border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h4 class="text-success">Payment Received!</h4>
                <p>Thank you for your payment. This invoice has been fully settled.</p>
                
                {% if invoice.payment_confirmations %}
                    {% for payment in invoice.payment_confirmations %}
                        {% if payment.receipt_generated %}
                            <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" 
                               class="btn btn-outline-success">
                                <i class="fas fa-download me-1"></i>Download Receipt
                            </a>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- {%- if session.language == "sw" -%}Ankara{%- else -%}Invoice{%- endif -%} Details Footer -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="text-muted">
                    <i class="fas fa-info-circle me-2"></i>Need Help?
                </h6>
                <p class="mb-0">
                    <i class="fas fa-phone me-1"></i> Call: +255 123 456 789 |
                    <i class="fas fa-envelope me-1"></i> {%- if session.language == "sw" -%}Barua pepe{%- else -%}Email{%- endif -%}: <EMAIL><br>
                    <small class="text-muted">Please reference invoice number: {{ invoice.invoice_number }}</small>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
