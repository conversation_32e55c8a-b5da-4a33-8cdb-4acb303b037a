#!/usr/bin/env python3
"""
Unit tests for fee calculator service
"""

import pytest
from unittest.mock import Mock
from services.fee_calculator import FeeCalculator

class TestFeeCalculator:
    """Test FeeCalculator class"""
    
    def test_calculate_transaction_fee_basic(self):
        """Test basic transaction fee calculation"""
        result = FeeCalculator.calculate_transaction_fee(5000.0)
        
        assert result['gross_amount'] == 5000.0
        assert result['base_fee'] == 125.0  # 2.5% of 5000
        assert result['operator_fee'] == 0.0
        assert result['total_fee'] == 125.0
        assert result['net_amount'] == 4875.0
        assert result['fee_percentage'] == 2.5
    
    def test_calculate_transaction_fee_with_operator(self):
        """Test transaction fee calculation with mobile operator"""
        # Test M-Pesa (no additional fees)
        result = FeeCalculator.calculate_transaction_fee(5000.0, mobile_operator='M-Pesa')
        
        assert result['gross_amount'] == 5000.0
        assert result['base_fee'] == 125.0
        assert result['operator_fee'] == 0.0
        assert result['total_fee'] == 125.0
        assert result['mobile_operator'] == 'M-Pesa'
        
        # Test CRDB Lipa (additional fees)
        result = FeeCalculator.calculate_transaction_fee(5000.0, mobile_operator='CRDB Lipa')
        
        assert result['gross_amount'] == 5000.0
        assert result['base_fee'] == 125.0
        assert result['operator_fee'] == 75.0  # 0.5% of 5000 + 50 fixed
        assert result['total_fee'] == 200.0
        assert result['mobile_operator'] == 'CRDB Lipa'
    
    def test_calculate_transaction_fee_minimum_fee(self):
        """Test minimum fee application"""
        # Small amount should use minimum fee
        result = FeeCalculator.calculate_transaction_fee(1000.0)
        
        assert result['gross_amount'] == 1000.0
        assert result['base_fee'] == 100.0  # Minimum fee applied
        assert result['total_fee'] == 100.0
        assert result['net_amount'] == 900.0
    
    def test_calculate_transaction_fee_maximum_fee(self):
        """Test maximum fee application"""
        # Large amount should use maximum fee
        result = FeeCalculator.calculate_transaction_fee(1000000.0)
        
        assert result['gross_amount'] == 1000000.0
        assert result['base_fee'] == 5000.0  # Maximum fee applied
        assert result['total_fee'] == 5000.0
        assert result['net_amount'] == 995000.0
    
    def test_calculate_transaction_fee_with_company(self):
        """Test transaction fee calculation with company-specific rates"""
        # Mock company with subscription
        mock_company = Mock()
        mock_subscription = Mock()
        mock_tier = Mock()
        
        mock_tier.transaction_fee_percentage = 1.5  # Lower rate for this company
        mock_subscription.pricing_tier = mock_tier
        mock_company.get_active_subscription.return_value = mock_subscription
        
        result = FeeCalculator.calculate_transaction_fee(5000.0, company=mock_company)
        
        assert result['gross_amount'] == 5000.0
        assert result['base_fee'] == 100.0  # Minimum fee (1.5% of 5000 = 75, but min is 100)
        assert result['fee_percentage'] == 1.5
    
    def test_calculate_transaction_fee_error_handling(self):
        """Test error handling in fee calculation"""
        # Invalid amount should return error result
        result = FeeCalculator.calculate_transaction_fee("invalid")
        
        assert 'error' in result
        assert result['gross_amount'] == "invalid"
        assert result['total_fee'] == 0.0
        assert result['net_amount'] == "invalid"
    
    def test_get_company_fee_percentage(self):
        """Test getting company-specific fee percentage"""
        # No company should return default
        fee_percentage = FeeCalculator._get_company_fee_percentage(None)
        assert fee_percentage == 2.5
        
        # Company without subscription should return default
        mock_company = Mock()
        mock_company.get_active_subscription.return_value = None
        
        fee_percentage = FeeCalculator._get_company_fee_percentage(mock_company)
        assert fee_percentage == 2.5
        
        # Company with subscription should return tier rate
        mock_subscription = Mock()
        mock_tier = Mock()
        mock_tier.transaction_fee_percentage = 1.8
        mock_subscription.pricing_tier = mock_tier
        mock_company.get_active_subscription.return_value = mock_subscription
        
        fee_percentage = FeeCalculator._get_company_fee_percentage(mock_company)
        assert fee_percentage == 1.8
    
    def test_calculate_operator_fee(self):
        """Test mobile operator fee calculation"""
        from decimal import Decimal
        
        # M-Pesa should have no additional fees
        fee = FeeCalculator._calculate_operator_fee(Decimal('5000'), 'M-Pesa')
        assert fee == Decimal('0.0')
        
        # CRDB Lipa should have percentage + fixed fee
        fee = FeeCalculator._calculate_operator_fee(Decimal('5000'), 'CRDB Lipa')
        expected = Decimal('5000') * Decimal('0.005') + Decimal('50')  # 0.5% + 50
        assert fee == expected
        
        # Halo Pesa should have percentage + fixed fee
        fee = FeeCalculator._calculate_operator_fee(Decimal('5000'), 'Halo Pesa')
        expected = Decimal('5000') * Decimal('0.003') + Decimal('30')  # 0.3% + 30
        assert fee == expected
        
        # Unknown operator should have no fees
        fee = FeeCalculator._calculate_operator_fee(Decimal('5000'), 'Unknown Operator')
        assert fee == Decimal('0.0')
    
    def test_calculate_subscription_fee(self):
        """Test subscription fee calculation"""
        # Mock pricing tier
        mock_tier = Mock()
        mock_tier.name = 'Business'
        mock_tier.price = 50000.0
        mock_tier.currency = 'TZS'
        
        # Test monthly subscription (no discount)
        result = FeeCalculator.calculate_subscription_fee(mock_tier, 1)
        
        assert result['tier_name'] == 'Business'
        assert result['base_price'] == 50000.0
        assert result['duration_months'] == 1
        assert result['subtotal'] == 50000.0
        assert result['discount_percentage'] == 0.0
        assert result['discount_amount'] == 0.0
        assert result['final_cost'] == 50000.0
        assert result['currency'] == 'TZS'
        
        # Test quarterly subscription (5% discount)
        result = FeeCalculator.calculate_subscription_fee(mock_tier, 3)
        
        assert result['duration_months'] == 3
        assert result['subtotal'] == 150000.0
        assert result['discount_percentage'] == 5.0
        assert result['discount_amount'] == 7500.0
        assert result['final_cost'] == 142500.0
        
        # Test annual subscription (15% discount)
        result = FeeCalculator.calculate_subscription_fee(mock_tier, 12)
        
        assert result['duration_months'] == 12
        assert result['subtotal'] == 600000.0
        assert result['discount_percentage'] == 15.0
        assert result['discount_amount'] == 90000.0
        assert result['final_cost'] == 510000.0
    
    def test_get_subscription_discount(self):
        """Test subscription discount calculation"""
        assert FeeCalculator._get_subscription_discount(1) == 0.0   # Monthly
        assert FeeCalculator._get_subscription_discount(3) == 5.0   # Quarterly
        assert FeeCalculator._get_subscription_discount(6) == 10.0  # Semi-annual
        assert FeeCalculator._get_subscription_discount(12) == 15.0 # Annual
        assert FeeCalculator._get_subscription_discount(24) == 15.0 # Max discount
    
    def test_calculate_pos_transaction_fee_cash(self):
        """Test POS transaction fee for cash payments"""
        result = FeeCalculator.calculate_pos_transaction_fee(5000.0, 'Cash')
        
        assert result['gross_amount'] == 5000.0
        assert result['total_fee'] == 0.0
        assert result['net_amount'] == 5000.0
        assert result['payment_method'] == 'Cash'
    
    def test_calculate_pos_transaction_fee_mobile_money(self):
        """Test POS transaction fee for mobile money payments"""
        result = FeeCalculator.calculate_pos_transaction_fee(5000.0, 'Mobile Money')
        
        assert result['gross_amount'] == 5000.0
        assert result['total_fee'] == 125.0  # Standard mobile money fee
        assert result['net_amount'] == 4875.0
        assert result['payment_method'] == 'Mobile Money'
    
    def test_calculate_pos_transaction_fee_card(self):
        """Test POS transaction fee for card payments"""
        result = FeeCalculator.calculate_pos_transaction_fee(5000.0, 'Card')
        
        assert result['gross_amount'] == 5000.0
        assert result['total_fee'] == 200.0  # Minimum card fee (3.5% of 5000 = 175, but min is 200)
        assert result['net_amount'] == 4800.0
        assert result['fee_percentage'] == 3.5
        assert result['payment_method'] == 'Card'
    
    def test_calculate_pos_transaction_fee_unknown(self):
        """Test POS transaction fee for unknown payment methods"""
        result = FeeCalculator.calculate_pos_transaction_fee(5000.0, 'Unknown Method')
        
        assert result['gross_amount'] == 5000.0
        assert result['total_fee'] == 0.0
        assert result['net_amount'] == 5000.0
        assert result['payment_method'] == 'Unknown Method'
    
    def test_get_fee_summary_for_company(self):
        """Test getting fee summary for a company"""
        # Mock company with subscription
        mock_company = Mock()
        mock_company.company_name = 'Test Company'
        
        mock_subscription = Mock()
        mock_subscription.is_active = True
        mock_subscription.end_date = None
        
        mock_tier = Mock()
        mock_tier.name = 'Business'
        mock_tier.transaction_fee_percentage = 2.0
        
        mock_subscription.pricing_tier = mock_tier
        mock_company.get_active_subscription.return_value = mock_subscription
        
        result = FeeCalculator.get_fee_summary_for_company(mock_company)
        
        assert result['company_name'] == 'Test Company'
        assert result['current_tier'] == 'Business'
        assert result['transaction_fee_percentage'] == 2.0
        assert result['minimum_fee'] == 100.0
        assert result['maximum_fee'] == 5000.0
        assert result['subscription_active'] == True
        assert 'mobile_money_fees' in result
    
    def test_get_fee_summary_no_company(self):
        """Test getting fee summary with no company"""
        result = FeeCalculator.get_fee_summary_for_company(None)
        
        assert result['company_name'] == 'Unknown'
        assert result['current_tier'] == 'No Subscription'
        assert result['transaction_fee_percentage'] == 2.5  # Default
        assert result['subscription_active'] == False
    
    def test_get_fee_summary_no_subscription(self):
        """Test getting fee summary for company without subscription"""
        mock_company = Mock()
        mock_company.company_name = 'Test Company'
        mock_company.get_active_subscription.return_value = None
        
        result = FeeCalculator.get_fee_summary_for_company(mock_company)
        
        assert result['company_name'] == 'Test Company'
        assert result['current_tier'] == 'No Subscription'
        assert result['transaction_fee_percentage'] == 2.5  # Default
        assert result['subscription_active'] == False

class TestFeeCalculatorEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_zero_amount(self):
        """Test fee calculation with zero amount"""
        result = FeeCalculator.calculate_transaction_fee(0.0)
        
        # Should apply minimum fee
        assert result['gross_amount'] == 0.0
        assert result['base_fee'] == 100.0  # Minimum fee
        assert result['total_fee'] == 100.0
        assert result['net_amount'] == -100.0  # Negative net amount
    
    def test_negative_amount(self):
        """Test fee calculation with negative amount"""
        result = FeeCalculator.calculate_transaction_fee(-1000.0)
        
        # Should handle gracefully
        assert result['gross_amount'] == -1000.0
        assert result['base_fee'] == 100.0  # Minimum fee still applies
    
    def test_very_large_amount(self):
        """Test fee calculation with very large amount"""
        result = FeeCalculator.calculate_transaction_fee(100000000.0)
        
        # Should cap at maximum fee
        assert result['gross_amount'] == 100000000.0
        assert result['base_fee'] == 5000.0  # Maximum fee
        assert result['total_fee'] == 5000.0
    
    def test_string_amount(self):
        """Test fee calculation with string amount"""
        result = FeeCalculator.calculate_transaction_fee("5000.50")
        
        # Should convert and calculate correctly
        assert result['gross_amount'] == 5000.5
        assert result['total_fee'] == 125.01  # 2.5% of 5000.5, rounded
    
    def test_precision_rounding(self):
        """Test fee calculation precision and rounding"""
        result = FeeCalculator.calculate_transaction_fee(3333.33)
        
        # Should round to 2 decimal places
        assert result['gross_amount'] == 3333.33
        assert isinstance(result['base_fee'], float)
        assert isinstance(result['total_fee'], float)
        assert isinstance(result['net_amount'], float)
        
        # Check that values are properly rounded
        assert round(result['total_fee'], 2) == result['total_fee']
