{% extends "base.html" %}

{% block title %}Landing Page Builder - {{ tier_name or 'Starter' }} Tier - EXLIPA{% endblock %}

{% block content %}
<style>
/* Tier-based styling */
.tier-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tier-free { background: #dcfce7; color: #166534; }
.tier-business { background: #dbeafe; color: #1d4ed8; }
.tier-enterprise { background: #fef3c7; color: #d97706; }

.feature-locked {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.feature-locked::after {
    content: '🔒 Upgrade Required';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
}

.upgrade-prompt {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
    text-align: center;
}

.builder-container {
    display: flex;
    height: 100vh;
    background: #f8fafc;
    position: relative;
}

.builder-sidebar {
    width: 350px;
    background: white;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.builder-canvas {
    flex: 1;
    background: #f1f5f9;
    overflow-y: auto;
    position: relative;
}

.sidebar-section {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
}

.sidebar-section h5 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.template-card {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 0.85rem;
}

.template-card:hover:not(.locked) {
    border-color: #3b82f6;
    transform: translateY(-2px);
}

.template-card.active {
    border-color: #1d4ed8;
    background: #eff6ff;
}

.template-card.locked {
    opacity: 0.5;
    cursor: not-allowed;
    position: relative;
}

.color-picker-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-option:hover:not(.locked) {
    transform: scale(1.1);
    border-color: #3b82f6;
}

.color-option.active {
    border-color: #1d4ed8;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.preview-frame {
    max-width: 1200px;
    margin: 20px auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tier-comparison {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.tier-comparison h6 {
    color: #1e293b;
    margin-bottom: 10px;
}

.tier-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    font-size: 0.85rem;
}

.tier-feature.available {
    color: #059669;
}

.tier-feature.unavailable {
    color: #dc2626;
}

/* Template Styles */
.template-basic .hero-section {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    padding: 60px 20px;
    text-align: center;
    color: white;
}

.template-basic .hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    font-family: 'Inter', sans-serif;
}

.template-basic .payment-section {
    padding: 60px 20px;
    background: #f8fafc;
}

.template-basic .payment-card {
    background: white;
    padding: 30px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.template-basic .payment-card:hover {
    transform: translateY(-5px);
}

.template-modern .hero-section {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%) !important;
    padding: 80px 20px;
    text-align: left;
    color: white;
    position: relative;
    overflow: hidden;
}

.template-modern .hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.template-modern .hero-section h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 25px;
    font-family: 'Poppins', sans-serif;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.template-modern .payment-section {
    padding: 80px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.template-modern .payment-card {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.template-modern .payment-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.template-minimal .hero-section {
    background: #ffffff !important;
    padding: 100px 20px;
    text-align: center;
    color: #1a202c;
    border-bottom: 1px solid #e2e8f0;
}

.template-minimal .hero-section h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 30px;
    font-family: 'Inter', sans-serif;
    letter-spacing: -0.02em;
    color: #2d3748;
}

.template-minimal .hero-section p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto 40px;
    line-height: 1.6;
}

.template-minimal .cta-button {
    background: #2d3748 !important;
    color: white !important;
    padding: 12px 32px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: inline-block !important;
    transition: all 0.3s ease !important;
    border: 2px solid #2d3748 !important;
}

.template-minimal .cta-button:hover {
    background: transparent !important;
    color: #2d3748 !important;
}

.template-minimal .payment-section {
    padding: 80px 20px;
    background: #fafafa;
}

.template-minimal .payment-card {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    transition: box-shadow 0.3s ease;
}

.template-minimal .payment-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .builder-container {
        flex-direction: column;
        height: auto;
    }

    .builder-sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
        order: 2;
    }

    .builder-canvas {
        order: 1;
        min-height: 60vh;
    }

    .preview-frame {
        margin: 10px;
        border-radius: 8px;
    }

    .sidebar-section {
        padding: 15px;
    }

    .form-control, .btn {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .landing-preview {
        padding: 15px;
    }

    .hero-section h1 {
        font-size: 1.8rem !important;
    }

    .hero-section p {
        font-size: 1rem !important;
    }

    .payment-methods {
        grid-template-columns: 1fr !important;
        gap: 10px !important;
    }

    .method-card {
        padding: 15px !important;
    }
}

@media (max-width: 480px) {
    .tier-badge {
        position: static;
        display: inline-block;
        margin-bottom: 10px;
    }

    .hero-section h1 {
        font-size: 1.5rem !important;
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<div class="builder-container">
    <!-- Tier Badge -->
    <div class="tier-badge tier-{{ (tier_name or 'starter')|lower }}">
        {{ tier_name or 'Starter' }} Tier
    </div>

    <!-- Sidebar Controls -->
    <div class="builder-sidebar">
        <!-- Tier Status -->
        {% if (tier_name or 'Starter') != 'Enterprise' %}
        <div class="upgrade-prompt">
            <h6 class="mb-2">🚀 Unlock More Features!</h6>
            <p class="mb-2 small">Upgrade to get advanced customization tools</p>
            <a href="{{ url_for('pricing') }}" class="btn btn-light btn-sm">View Pricing</a>
        </div>
        {% endif %}

        <!-- Share Section -->
        <div class="sidebar-section">
            <h5><i class="fas fa-share-alt"></i> Share</h5>

            <!-- URL with Copy -->
            <div class="input-group mb-2">
                <input type="text" class="form-control form-control-sm" id="landingPageUrl"
                       value="{{ url_for('public_company_landing', company_id=company.id, _external=True) }}"
                       readonly style="font-size: 0.75rem;">
                <button class="btn btn-outline-primary btn-sm" type="button" onclick="copyLandingUrl()" title="{{ t('Copy') }}">
                    <i class="fas fa-copy"></i>
                </button>
            </div>

            <!-- Quick Actions -->
            <div class="d-flex gap-1">
                <button class="btn btn-outline-success btn-sm" onclick="shareWhatsApp()" title="WhatsApp">
                    <i class="fab fa-whatsapp"></i>
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="shareEmail()" title="{{ t('{%- if session.language == "sw" -%}Barua pepe{%- else -%}Email{%- endif -%}') }}">
                    <i class="fas fa-envelope"></i>
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="previewLandingPage()" title="{{ t('Preview') }}">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="toggleQR()" title="QR Code">
                    <i class="fas fa-qrcode"></i>
                </button>
            </div>

            <!-- Collapsible QR Code -->
            <div id="qrSection" style="display: none;" class="mt-2 text-center">
                <div id="qrcode" style="width: 80px; height: 80px; margin: 0 auto;"></div>
            </div>
        </div>

        <!-- Template Selection -->
        <div class="sidebar-section">
            <h5><i class="fas fa-palette"></i> Templates</h5>
            <div class="template-grid">
                {% for template in available_templates %}
                <div class="template-card {% if loop.first %}active{% endif %}" data-template="{{ template.id }}">
                    <i class="fas fa-{% if template.id == 'basic' %}file-alt{% elif template.id == 'modern' %}laptop{% elif template.id == 'minimal' %}leaf{% elif template.id == 'professional' %}briefcase{% elif template.id == 'gradient' %}paint-brush{% elif template.id == 'corporate' %}building{% elif template.id == 'ecommerce' %}shopping-cart{% elif template.id == 'service' %}tools{% elif template.id == 'luxury' %}crown{% elif template.id == 'tech' %}microchip{% elif template.id == 'creative' %}magic{% else %}file-alt{% endif %} mb-1"></i>
                    <div class="fw-bold">{{ template.name }}</div>
                    <div class="small text-muted">{{ template.description }}</div>
                </div>
                {% endfor %}

                <!-- {%- if session.language == "sw" -%}Imefungwa{%- else -%}Locked{%- endif -%} Templates for Lower Tiers -->
                {% if tier_name == 'Free' %}
                <div class="template-card feature-locked" data-template="professional">
                    <i class="fas fa-briefcase mb-1"></i>
                    <div class="fw-bold">{{ t('Professional') }}</div>
                    <div class="small text-muted">Business+ only</div>
                </div>
                <div class="template-card feature-locked" data-template="luxury">
                    <i class="fas fa-crown mb-1"></i>
                    <div class="fw-bold">{{ t('Luxury') }}</div>
                    <div class="small text-muted">Enterprise only</div>
                </div>
                {% elif tier_name == 'Business' %}
                <div class="template-card locked" title="Upgrade to Business tier">
                    <i class="fas fa-laptop mb-1"></i>
                    <div class="fw-bold">{{ t('Modern') }}</div>
                    <small class="text-muted">🔒 Business+</small>
                </div>
                <div class="template-card locked" title="Upgrade to Business tier">
                    <i class="fas fa-utensils mb-1"></i>
                    <div class="fw-bold">Restaurant</div>
                    <small class="text-muted">🔒 Business+</small>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Color Customization -->
        <div class="sidebar-section {% if not available_features.custom_colors %}feature-locked{% endif %}">
            <h5><i class="fas fa-paint-brush"></i> Colors</h5>
            
            {% if available_features.colors == 'unlimited' or available_features.custom_colors %}
            <label>Primary Color</label>
            <div class="color-picker-grid">
                <div class="color-option active" style="background: #3b82f6" data-color="#3b82f6"></div>
                <div class="color-option" style="background: #ef4444" data-color="#ef4444"></div>
                <div class="color-option" style="background: #10b981" data-color="#10b981"></div>
                <div class="color-option" style="background: #f59e0b" data-color="#f59e0b"></div>
                <div class="color-option" style="background: #8b5cf6" data-color="#8b5cf6"></div>
                <div class="color-option" style="background: #06b6d4" data-color="#06b6d4"></div>
                <div class="color-option" style="background: #84cc16" data-color="#84cc16"></div>
                <div class="color-option" style="background: #f97316" data-color="#f97316"></div>
            </div>
            <input type="color" class="form-control" id="customColor" value="#3b82f6">
            {% else %}
            <p class="text-muted small">Limited to preset colors in Starter tier</p>
            <div class="color-picker-grid">
                {% for color in available_features.colors %}
                <div class="color-option" style="background: {{ color }}" data-color="{{ color }}"></div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <!-- Typography -->
        <div class="sidebar-section {% if not available_features.layout_controls %}feature-locked{% endif %}">
            <h5><i class="fas fa-font"></i> Typography</h5>
            <div class="mb-3">
                <label class="form-label">Font Family</label>
                <select class="form-select" id="fontFamily">
                    {% if available_features.fonts == 'unlimited' %}
                        <option value="Inter">Inter (Modern)</option>
                        <option value="Poppins">Poppins (Friendly)</option>
                        <option value="Roboto">Roboto (Clean)</option>
                        <option value="Playfair Display">Playfair (Elegant)</option>
                        <option value="Montserrat">Montserrat (Bold)</option>
                    {% else %}
                        {% for font in available_features.fonts %}
                        <option value="{{ font }}">{{ font }}</option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
        </div>

        <!-- Advanced Features -->
        <div class="sidebar-section {% if not available_features.get('custom_css', False) %}feature-locked{% endif %}">
            <h5><i class="fas fa-code"></i> Custom CSS</h5>
            <p class="text-muted small">Add your own custom styles</p>
            <textarea class="form-control" rows="4" placeholder="/* Your custom CSS here */"></textarea>
        </div>

        <!-- Tier Comparison -->
        <div class="sidebar-section">
            <div class="tier-comparison">
                <h6>Your Current Features</h6>
                <div class="tier-feature available">
                    <i class="fas fa-check"></i> Basic templates
                </div>
                <div class="tier-feature available">
                    <i class="fas fa-check"></i> Mobile responsive
                </div>
                {% if available_features.custom_colors %}
                <div class="tier-feature available">
                    <i class="fas fa-check"></i> Custom colors
                </div>
                {% else %}
                <div class="tier-feature unavailable">
                    <i class="fas fa-times"></i> Custom colors
                </div>
                {% endif %}
                {% if available_features.get('custom_css', False) %}
                <div class="tier-feature available">
                    <i class="fas fa-check"></i> Custom CSS
                </div>
                {% else %}
                <div class="tier-feature unavailable">
                    <i class="fas fa-times"></i> Custom CSS
                </div>
                {% endif %}
                
                {% if (tier_name or 'Starter') != 'Enterprise' %}
                <div class="mt-3">
                    <a href="{{ url_for('pricing') }}" class="btn btn-primary btn-sm w-100">
                        Upgrade for More Features
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- {%- if session.language == "sw" -%}Hifadhi{%- else -%}Save{%- endif -%} Button -->
        <div class="sidebar-section">
            <button class="btn btn-success w-100 btn-lg" id="saveButton">
                <i class="fas fa-save me-2"></i>Save Landing Page
            </button>
        </div>
    </div>

    <!-- Preview Canvas -->
    <div class="builder-canvas">
        <div class="preview-frame" id="previewFrame">
            <!-- Live Preview Content -->
            <div class="landing-preview template-basic" id="landingPreview">
                <!-- Hero Section -->
                <div class="hero-section">
                    <div class="container">
                        <h1 id="previewTitle">{{ company.company_name }}</h1>
                        <p id="previewDescription">{{ company.landing_page_description or 'Professional payment solutions for your business' }}</p>
                        <a href="#payment" class="cta-button">{{ t('Make Payment') }}</a>
                    </div>
                </div>

                <!-- {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Methods Section -->
                <div class="payment-section">
                    <div class="container text-center">
                        <h2>{{ t('Payment Methods') }}</h2>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; max-width: 800px; margin: 30px auto 0;" id="paymentGrid">
                            {% if company.mpesa_till %}
                            <div class="payment-card">
                                <i class="fas fa-mobile-alt fa-3x text-success mb-3"></i>
                                <h5>{{ t('M-Pesa') }}</h5>
                                <p class="fw-bold">{{ company.mpesa_till }}</p>
                            </div>
                            {% endif %}
                            {% if company.tigo_paybill %}
                            <div class="payment-card">
                                <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                                <h5>{{ t('Tigo Pesa') }}</h5>
                                <p class="fw-bold">{{ company.tigo_paybill }}</p>
                            </div>
                            {% endif %}
                            {% if company.airtel_merchant %}
                            <div class="payment-card">
                                <i class="fas fa-phone fa-3x text-danger mb-3"></i>
                                <h5>{{ t('Airtel Money') }}</h5>
                                <p class="fw-bold">{{ company.airtel_merchant }}</p>
                            </div>
                            {% endif %}
                            {% if company.crdb_merchant %}
                            <div class="payment-card">
                                <i class="fas fa-university fa-3x text-info mb-3"></i>
                                <h5>CRDB Bank</h5>
                                <p class="fw-bold">{{ company.crdb_merchant }}</p>
                            </div>
                            {% endif %}
                            {% if not (company.mpesa_till or company.tigo_paybill or company.airtel_merchant or company.crdb_merchant) %}
                            <div class="payment-card">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h5>{{ t('Payment Methods') }}</h5>
                                <p class="text-muted">Configure payment methods in your profile</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Tier-based landing page builder loaded');
    console.log('Available features:', {{ available_features|tojson }});
    
    // Only enable features available in current tier
    const availableFeatures = {{ available_features|tojson }};
    
    // Template selection (only for available templates)
    const templateCards = document.querySelectorAll('.template-card:not(.locked)');
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            templateCards.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            // Switch template style
            const templateId = this.dataset.template;
            switchTemplate(templateId);
        });
    });
    
    // Color picker (only if custom colors are available)
    if (availableFeatures.custom_colors) {
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                colorOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
                
                const color = this.dataset.color;
                updatePrimaryColor(color);
            });
        });
    }
    
    function switchTemplate(templateId) {
        const preview = document.getElementById('landingPreview');

        // Remove all template classes
        preview.classList.remove('template-basic', 'template-modern', 'template-minimal');

        // Add new template class
        preview.classList.add(`template-${templateId}`);

        // Update CTA button style based on template
        const ctaButton = preview.querySelector('.cta-button');
        if (templateId === 'minimal') {
            ctaButton.style.background = '#2d3748';
            ctaButton.style.color = 'white';
            ctaButton.style.borderRadius = '6px';
            ctaButton.style.padding = '12px 32px';
        } else if (templateId === 'modern') {
            ctaButton.style.background = 'rgba(255,255,255,0.2)';
            ctaButton.style.color = 'white';
            ctaButton.style.borderRadius = '25px';
            ctaButton.style.padding = '15px 35px';
            ctaButton.style.border = '2px solid rgba(255,255,255,0.3)';
            ctaButton.style.backdropFilter = 'blur(10px)';
        } else {
            // Basic template
            ctaButton.style.background = 'white';
            ctaButton.style.color = '#3b82f6';
            ctaButton.style.borderRadius = '50px';
            ctaButton.style.padding = '15px 30px';
            ctaButton.style.border = 'none';
        }

        console.log(`Switched to ${templateId} template`);
    }

    function updatePrimaryColor(color) {
        const heroSection = document.querySelector('.hero-section');
        const preview = document.getElementById('landingPreview');

        if (heroSection && preview.classList.contains('template-basic')) {
            heroSection.style.background = `linear-gradient(135deg, ${color}, ${adjustBrightness(color, -20)})`;
        } else if (heroSection && preview.classList.contains('template-modern')) {
            heroSection.style.background = `linear-gradient(45deg, ${color}, ${adjustBrightness(color, -30)})`;
        }
        // Minimal template keeps white background
    }
    
    function adjustBrightness(color, amount) {
        const usePound = color[0] === '#';
        const col = usePound ? color.slice(1) : color;
        const num = parseInt(col, 16);
        let r = (num >> 16) + amount;
        let g = (num >> 8 & 0x00FF) + amount;
        let b = (num & 0x0000FF) + amount;
        r = r > 255 ? 255 : r < 0 ? 0 : r;
        g = g > 255 ? 255 : g < 0 ? 0 : g;
        b = b > 255 ? 255 : b < 0 ? 0 : b;
        return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }
    
    // Save functionality
    document.getElementById('saveButton').addEventListener('click', function() {
        console.log('Saving tier-based landing page...');
        // Add save logic here
    });

});

// Sharing Functions
function copyLandingUrl() {
    const urlInput = document.getElementById('landingPageUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showToast('✅ URL copied to clipboard!', 'success');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(urlInput.value).then(() => {
            showToast('✅ URL copied to clipboard!', 'success');
        }).catch(() => {
            showToast('❌ Failed to copy URL', 'error');
        });
    }
}

function shareWhatsApp() {
    const url = document.getElementById('landingPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const message = `Make payments to ${companyName} securely: ${url}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareEmail() {
    const url = document.getElementById('landingPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const subject = `Payment Page - ${companyName}`;
    const body = `Hi,\n\nYou can make payments to ${companyName} using this secure payment page:\n\n${url}\n\nThank you!`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = emailUrl;
}

function previewLandingPage() {
    const url = document.getElementById('landingPageUrl').value;
    window.open(url, '_blank');
}

function toggleQR() {
    const qrSection = document.getElementById('qrSection');
    if (qrSection.style.display === 'none') {
        qrSection.style.display = 'block';
        generateQRCode();
    } else {
        qrSection.style.display = 'none';
    }
}

function generateQRCode() {
    const url = document.getElementById('landingPageUrl').value;
    const qrContainer = document.getElementById('qrcode');

    // Clear existing QR code
    qrContainer.innerHTML = '';

    // Generate QR code using a simple method
    const qrSize = 80;
    const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}x${qrSize}&data=${encodeURIComponent(url)}`;

    const qrImg = document.createElement('img');
    qrImg.src = qrUrl;
    qrImg.style.width = '100%';
    qrImg.style.height = '100%';
    qrImg.alt = 'QR Code';

    qrContainer.appendChild(qrImg);
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="me-2">${message}</span>
            <button type="button" class="btn-close btn-close-white ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// Add CSS for toast animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
