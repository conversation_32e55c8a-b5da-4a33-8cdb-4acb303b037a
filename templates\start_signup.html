{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}<PERSON><PERSON><PERSON><PERSON> kwa {{ tier.name }} - EXLIPA{% else %}Sign Up for {{ tier.name }} - EXLIPA{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Progress Indicator -->
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8">
            <div class="glass-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0 gradient-text">
                        {% if session.language == 'sw' %}Mchakat<PERSON> wa <PERSON>li{% else %}Registration Process{% endif %}
                    </h6>
                    <span class="badge bg-primary">1/3</span>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-gradient" style="width: 33%"></div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <small class="text-primary fw-bold">
                        {% if session.language == 'sw' %}Taarifa{% else %}Details{% endif %}
                    </small>
                    <small class="text-muted">
                        {% if session.language == 'sw' %}Malipo{% else %}Payment{% endif %}
                    </small>
                    <small class="text-muted">
                        {% if session.language == 'sw' %}Uhakiki{% else %}Verification{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Plan Summary -->
            <div class="glass-card p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="fw-bold gradient-text mb-2">
                            {% if session.language == 'sw' %}
                                {% if tier.name == 'Free' %}Mpango wa Bure{% elif tier.name == 'Business' %}Mpango wa Biashara{% else %}Mpango wa Makampuni{% endif %}
                            {% else %}
                                {{ tier.name }} Plan
                            {% endif %}
                        </h4>
                        <p class="text-muted mb-0">{{ tier.description }}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        {% if tier.name == 'Free' %}
                        <div class="h3 text-success mb-0">
                            {% if session.language == 'sw' %}BURE{% else %}FREE{% endif %}
                        </div>
                        <small class="text-muted">
                            {% if session.language == 'sw' %}Hakuna malipo{% else %}No payment required{% endif %}
                        </small>
                        {% else %}
                        <div class="h3 text-primary mb-0">TSh {{ "{:,}".format(tier.monthly_fee) }}</div>
                        <small class="text-muted">
                            {% if session.language == 'sw' %}kwa mwezi{% else %}per month{% endif %}
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Signup Form -->
            <div class="glass-card p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-2x text-primary mb-3"></i>
                    <h3 class="fw-bold gradient-text">
                        {% if session.language == 'sw' %}Taarifa Zako{% else %}Your Information{% endif %}
                    </h3>
                    <p class="text-muted">
                        {% if session.language == 'sw' %}
                            Jaza taarifa zako ili tuanze mchakato wa kujisajili
                        {% else %}
                            Fill in your details to start the registration process
                        {% endif %}
                    </p>
                </div>
                <form method="POST" class="needs-validation" novalidate id="signupForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="customer_name" class="form-label fw-bold">
                                <i class="fas fa-user me-2 text-primary"></i>
                                {% if session.language == 'sw' %}Jina Lako Kamili{% else %}Your Full Name{% endif %} *
                            </label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" required
                                   placeholder="{% if session.language == 'sw' %}Jina la kwanza na la mwisho{% else %}First and last name{% endif %}">
                            <div class="invalid-feedback">
                                {% if session.language == 'sw' %}Tafadhali ingiza jina lako{% else %}Please enter your name{% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customer_email" class="form-label fw-bold">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                {% if session.language == 'sw' %}Barua Pepe{% else %}Email Address{% endif %} *
                            </label>
                            <input type="email" class="form-control" id="customer_email" name="customer_email" required
                                   placeholder="{% if session.language == 'sw' %}<EMAIL>{% else %}<EMAIL>{% endif %}">
                            <div class="form-text">
                                {% if session.language == 'sw' %}
                                    Tutakutumia kiungo cha kujisajili baada ya uhakiki wa malipo
                                {% else %}
                                    We'll send your registration link after payment verification
                                {% endif %}
                            </div>
                            <div class="invalid-feedback">
                                {% if session.language == 'sw' %}Tafadhali ingiza barua pepe sahihi{% else %}Please enter a valid email address{% endif %}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="mobile_money_sender_name" class="form-label">Mobile Money Registered Name *</label>
                            <input type="text" class="form-control" id="mobile_money_sender_name" name="mobile_money_sender_name" required>
                            <div class="form-text">Enter the exact name registered with your mobile money account.</div>
                            <div class="invalid-feedback">Please enter your mobile money registered name.</div>
                        </div>
                        <div class="mb-3">
                            <label for="mobile_operator" class="form-label">Payment Method *</label>
                            <select class="form-select" id="mobile_operator" name="mobile_operator" required>
                                <option value="">Select payment method...</option>
                                <option value="M-Pesa">M-Pesa (Vodacom)</option>
                                <option value="TigoPesa">{{ t('Tigo Pesa') }}</option>
                                <option value="Airtel Money">{{ t('Airtel Money') }}</option>
                                <option value="CRDB Lipa">{{ t('CRDB Lipa') }}</option>
                            </select>
                            <div class="invalid-feedback">Please select a payment method.</div>
                        </div>
                        <!-- Dynamic Payment Method Details -->
                        <div class="mb-3" id="payment-details" style="display:none;">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center" id="payment-details-content">
                                    <!-- Details will be injected here -->
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="transaction_id" class="form-label">Mobile Money Transaction ID *</label>
                            <input type="text" class="form-control" id="transaction_id" name="transaction_id" required>
                            <div class="form-text">Enter the transaction ID from your mobile money payment SMS.</div>
                            <div class="invalid-feedback">Please enter your transaction ID.</div>
                        </div>
                        <button type="submit" class="btn btn-success w-100">Submit Payment Confirmation</button>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ url_for('pricing') }}" class="btn btn-link">&larr; Back to Packages</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
// Payment method details data
const paymentDetails = {
    "M-Pesa": {
        icon: '<i class="fab fa-cc-mastercard fa-2x text-success mb-2"></i>',
        name: "Vodacom M-Pesa",
        detailLabel: "Till Number",
        detailValue: "123456",
        business: "Exlipa Payment Solutions Ltd",
        badgeClass: "bg-success"
    },
    "TigoPesa": {
        icon: '<i class="fab fa-cc-visa fa-2x text-primary mb-2"></i>',
        name: "Tigo Pesa",
        detailLabel: "Paybill",
        detailValue: "654321",
        business: "Exlipa Payment Solutions Ltd",
        badgeClass: "bg-primary"
    },
    "Airtel Money": {
        icon: '<i class="fas fa-mobile-alt fa-2x text-danger mb-2"></i>',
        name: "Airtel Money",
        detailLabel: "Merchant Code",
        detailValue: "789012",
        business: "Exlipa Payment Solutions Ltd",
        badgeClass: "bg-danger"
    },
    "CRDB Lipa": {
        icon: '<i class="fas fa-university fa-2x text-secondary mb-2"></i>',
        name: "CRDB Lipa",
        detailLabel: "Merchant Code",
        detailValue: "890123",
        business: "Exlipa Payment Solutions Ltd",
        badgeClass: "bg-secondary"
    }
};

// Update payment details on selection
const operatorSelect = document.getElementById('mobile_operator');
const detailsDiv = document.getElementById('payment-details');
const detailsContent = document.getElementById('payment-details-content');
operatorSelect.addEventListener('change', function() {
    const val = operatorSelect.value;
    if (paymentDetails[val]) {
        const d = paymentDetails[val];
        detailsContent.innerHTML = `
            ${d.icon}
            <h6 class="fw-bold mt-2">${d.name}</h6>
            <div><strong>${d.detailLabel}:</strong> <span class="badge ${d.badgeClass}">${d.detailValue}</span></div>
            <div><strong>Business Name:</strong> ${d.business}</div>
        `;
        detailsDiv.style.display = '';
    } else {
        detailsDiv.style.display = 'none';
        detailsContent.innerHTML = '';
    }
});
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
