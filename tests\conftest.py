#!/usr/bin/env python3
"""
Test configuration and fixtures for EXLIPA
"""

import pytest
import tempfile
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.base import db
from models.user import User
from models.company import ClientCompany, PricingTier, Subscription
from models.payment import PaymentConfirmation, Invoice
from werkzeug.security import generate_password_hash

@pytest.fixture
def app():
    """Create application for testing"""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()
    
    # Test configuration
    test_config = {
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key'
    }
    
    app = create_app(test_config)
    
    with app.app_context():
        db.create_all()
        yield app
        
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()

@pytest.fixture
def super_admin_user(app):
    """Create super admin user for testing"""
    with app.app_context():
        user = User(
            username='superadmin',
            email='<EMAIL>',
            full_name='Super Admin',
            role='super_admin'
        )
        user.set_password('SuperAdmin123!')
        user.save()
        return user

@pytest.fixture
def user_admin_user(app):
    """Create user admin for testing"""
    with app.app_context():
        user = User(
            username='useradmin',
            email='<EMAIL>',
            full_name='User Admin',
            role='user_admin'
        )
        user.set_password('UserAdmin123!')
        user.save()
        return user

@pytest.fixture
def company_user(app):
    """Create company user for testing"""
    with app.app_context():
        user = User(
            username='companyuser',
            email='<EMAIL>',
            full_name='Company User',
            role='company_user'
        )
        user.set_password('CompanyUser123!')
        user.save()
        return user

@pytest.fixture
def pricing_tier(app):
    """Create pricing tier for testing"""
    with app.app_context():
        tier = PricingTier(
            name='Business',
            description='Business tier for testing',
            price=50000.0,
            transaction_fee_percentage=2.5,
            has_api_access=True,
            has_analytics=True
        )
        tier.save()
        return tier

@pytest.fixture
def client_company(app, user_admin_user, pricing_tier):
    """Create client company for testing"""
    with app.app_context():
        company = ClientCompany(
            company_name='Test Company Ltd',
            company_email='<EMAIL>',
            company_phone='+255712345678',
            owner_user_id=user_admin_user.id
        )
        company.generate_api_key()
        company.save()
        
        # Create subscription
        subscription = Subscription.create_subscription(
            company_id=company.id,
            pricing_tier_id=pricing_tier.id,
            amount_paid=50000.0,
            payment_method='Mobile Money',
            payment_reference='TEST123'
        )
        
        return company

@pytest.fixture
def sample_invoice(app, client_company):
    """Create sample invoice for testing"""
    with app.app_context():
        invoice = Invoice(
            invoice_number=Invoice.generate_invoice_number(),
            customer_name='John Doe',
            customer_email='<EMAIL>',
            amount=25000.0,
            service_description='Test service',
            company_id=client_company.id
        )
        invoice.save()
        return invoice

@pytest.fixture
def sample_payment(app, client_company):
    """Create sample payment confirmation for testing"""
    with app.app_context():
        payment = PaymentConfirmation(
            customer_name='Jane Doe',
            mobile_money_sender_name='JANE DOE',
            amount=15000.0,
            mobile_operator='M-Pesa',
            transaction_id='AB12345678',
            client_company_id=client_company.id,
            net_amount=14625.0,  # After 2.5% fee
            transaction_fee=375.0
        )
        payment.save()
        return payment

@pytest.fixture
def authenticated_client(client, user_admin_user):
    """Create authenticated client session"""
    with client.session_transaction() as sess:
        sess['user_id'] = user_admin_user.id
        sess['user_role'] = user_admin_user.role
        sess['_user_id'] = str(user_admin_user.id)
    return client

@pytest.fixture
def api_headers(client_company):
    """Create API headers with authentication"""
    return {
        'Authorization': f'Bearer {client_company.api_key}',
        'Content-Type': 'application/json'
    }

# Test data fixtures
@pytest.fixture
def valid_payment_data():
    """Valid payment data for testing"""
    return {
        'customer_name': 'Test Customer',
        'mobile_money_sender_name': 'TEST CUSTOMER',
        'amount': '5000.00',
        'mobile_operator': 'M-Pesa',
        'transaction_id': 'TEST123456',
        'service_description': 'Test payment'
    }

@pytest.fixture
def invalid_payment_data():
    """Invalid payment data for testing"""
    return {
        'customer_name': '',  # Missing required field
        'amount': '500',      # Below minimum
        'mobile_operator': 'Invalid Operator',
        'transaction_id': '123'  # Too short
    }

@pytest.fixture
def valid_user_data():
    """Valid user data for testing"""
    return {
        'username': 'testuser',
        'email': '<EMAIL>',
        'full_name': 'Test User',
        'password': 'TestPassword123!',
        'role': 'user_admin'
    }

@pytest.fixture
def invalid_user_data():
    """Invalid user data for testing"""
    return {
        'username': 'tu',  # Too short
        'email': 'invalid-email',
        'full_name': '',
        'password': '123',  # Too weak
        'role': 'invalid_role'
    }

# Helper functions
def login_user(client, username, password):
    """Helper function to login user"""
    return client.post('/user-admin-login', data={
        'username': username,
        'password': password
    }, follow_redirects=True)

def logout_user(client):
    """Helper function to logout user"""
    return client.get('/logout', follow_redirects=True)

def create_test_user(username, email, role='user_admin', password='TestPass123!'):
    """Helper function to create test user"""
    user = User(
        username=username,
        email=email,
        full_name=f'Test {username.title()}',
        role=role
    )
    user.set_password(password)
    user.save()
    return user
