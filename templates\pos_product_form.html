{% extends "base.html" %}

{% block title %}{{ '{%- if session.language == "sw" -%}<PERSON><PERSON>{%- else -%}Edit{%- endif -%}' if product else '{%- if session.language == "sw" -%}Ongeza{%- else -%}Add{%- endif -%}' }} {%- if session.language == "sw" -%}Bidhaa{%- else -%}Product{%- endif -%} - POS{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-{% if product %}edit{% else %}plus{% endif %} me-2 text-primary"></i>
                        {{ '{%- if session.language == "sw" -%}<PERSON>ri{%- else -%}Edit{%- endif -%}' if product else 'Add New' }} Product
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Basic Information</h6>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Product {%- if session.language == "sw" -%}Jina{%- else -%}Name{%- endif -%} *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ product.name if product else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">{{ t('{%- if session.language == "sw" -%}Maelezo{%- else -%}Description{%- endif -%}') }}</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" 
                                              placeholder="Optional product description">{{ product.description if product else '' }}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">{%- if session.language == "sw" -%}Jamii{%- else -%}Category{%- endif -%}</label>
                                    <input type="text" class="form-control" id="category" name="category" 
                                           value="{{ product.category if product else '' }}" 
                                           placeholder="e.g., Electronics, Food, Clothing">
                                </div>

                                <div class="mb-3">
                                    <label for="sku" class="form-label">SKU (Stock Keeping Unit)</label>
                                    <input type="text" class="form-control" id="sku" name="sku" 
                                           value="{{ product.sku if product else '' }}" 
                                           placeholder="e.g., PROD-001">
                                </div>
                            </div>

                            <!-- Pricing & Inventory -->
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Pricing & Inventory</h6>
                                
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price (TZS) *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">TZS</span>
                                        <input type="number" class="form-control" id="price" name="price" 
                                               value="{{ product.price if product else '' }}" 
                                               min="0" step="0.01" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="track_inventory" 
                                               name="track_inventory" {% if not product or product.track_inventory %}checked{% endif %}>
                                        <label class="form-check-label" for="track_inventory">
                                            Track Inventory
                                        </label>
                                    </div>
                                    <small class="text-muted">Enable to track stock levels for this product</small>
                                </div>

                                <div id="inventory-fields">
                                    <div class="mb-3">
                                        <label for="stock_quantity" class="form-label">Current Stock</label>
                                        <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                               value="{{ product.stock_quantity if product else 0 }}" min="0">
                                    </div>

                                    <div class="mb-3">
                                        <label for="low_stock_alert" class="form-label">Low Stock Alert</label>
                                        <input type="number" class="form-control" id="low_stock_alert" name="low_stock_alert" 
                                               value="{{ product.low_stock_alert if product else 5 }}" min="0">
                                        <small class="text-muted">Get alerts when stock falls below this number</small>
                                    </div>
                                </div>

                                {% if product %}
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" 
                                               name="is_active" {% if product.is_active %}checked{% endif %}>
                                        <label class="form-check-label" for="is_active">
                                            {%- if session.language == "sw" -%}Bidhaa{%- else -%}Product{%- endif -%} is Active
                                        </label>
                                    </div>
                                    <small class="text-muted">Inactive products won't appear in the POS</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- {%- if session.language == "sw" -%}Fomu{%- else -%}Form{%- endif -%} {%- if session.language == "sw" -%}Vitendo{%- else -%}Actions{%- endif -%} -->
                        <div class="border-top pt-3 mt-4">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('pos_products') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>{{ '{%- if session.language == "sw" -%}Sasisha{%- else -%}Update{%- endif -%}' if product else '{%- if session.language == "sw" -%}Hifadhi{%- else -%}Save{%- endif -%}' }} Product
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {% if product %}
            <!-- {%- if session.language == "sw" -%}Bidhaa{%- else -%}Product{%- endif -%} Preview -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Product Preview</h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">{{ product.name }}</h5>
                            {% if product.description %}
                                <p class="text-muted mb-2">{{ product.description }}</p>
                            {% endif %}
                            <div class="d-flex gap-3 align-items-center">
                                <span class="badge bg-success fs-6">TZS {{ "{:,.0f}".format(product.price) }}</span>
                                {% if product.category %}
                                    <span class="badge bg-secondary">{{ product.category }}</span>
                                {% endif %}
                                {% if product.track_inventory %}
                                    <small class="text-muted">Stock: {{ product.stock_quantity }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-{% if product.is_active %}success{% else %}secondary{% endif %} fs-6">
                                {{ '{%- if session.language == "sw" -%}Hai{%- else -%}Active{%- endif -%}' if product.is_active else '{%- if session.language == "sw" -%}Haifanyi Kazi{%- else -%}Inactive{%- endif -%}' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Toggle inventory fields based on track_inventory checkbox
document.getElementById('track_inventory').addEventListener('change', function() {
    const inventoryFields = document.getElementById('inventory-fields');
    if (this.checked) {
        inventoryFields.style.display = 'block';
    } else {
        inventoryFields.style.display = 'none';
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const trackInventory = document.getElementById('track_inventory');
    const inventoryFields = document.getElementById('inventory-fields');
    
    if (!trackInventory.checked) {
        inventoryFields.style.display = 'none';
    }
});

// Real-time price formatting
document.getElementById('price').addEventListener('input', function() {
    const value = parseFloat(this.value);
    if (!isNaN(value)) {
        // {%- if session.language == "sw" -%}Sasisha{%- else -%}Update{%- endif -%} preview if it exists
        const preview = document.querySelector('.badge.bg-success.fs-6');
        if (preview) {
            preview.textContent = `TZS ${value.toLocaleString()}`;
        }
    }
});
</script>
{% endblock %}
