{% extends "base.html" %}

{% block title %}API Management - {{ company.company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">🔑
                        {% if session.language == 'sw' %}
                            Usimamizi wa API
                        {% else %}
                            API Management
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if session.language == 'sw' %}
                            Simamia ufikiaji wa API kwa {{ company.company_name }}
                        {% else %}
                            Manage API access for {{ company.company_name }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('user_admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {% if session.language == 'sw' %}<PERSON><PERSON>od<PERSON>{% else %}Back to Dashboard{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if not has_api_access %}
    <!-- No API Access -->
    <div class="row">
        <div class="col-12">
            <div class="glass-card p-5 text-center">
                <div class="mb-4">
                    <i class="fas fa-lock fa-3x text-muted"></i>
                </div>
                <h4 class="fw-bold mb-3">API Access Not Available</h4>
                <p class="text-muted mb-4">
                    API access is available for Business and Enterprise tiers only. 
                    Upgrade your plan to access our powerful REST API.
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="border rounded p-3">
                                    <h6 class="fw-bold text-primary">Business Tier</h6>
                                    <ul class="list-unstyled small mb-3">
                                        <li><i class="fas fa-check text-success me-1"></i>Full API Access</li>
                                        <li><i class="fas fa-check text-success me-1"></i>1,000 requests/hour</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Payment Processing</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Analytics API</li>
                                    </ul>
                                    <div class="fw-bold">TSh 50,000/month</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="border rounded p-3">
                                    <h6 class="fw-bold text-warning">Enterprise Tier</h6>
                                    <ul class="list-unstyled small mb-3">
                                        <li><i class="fas fa-check text-success me-1"></i>Full API Access</li>
                                        <li><i class="fas fa-check text-success me-1"></i>10,000 requests/hour</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Payment Processing</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Analytics API</li>
                                        <li><i class="fas fa-check text-success me-1"></i>Webhooks</li>
                                    </ul>
                                    <div class="fw-bold">TSh 150,000/month</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{{ url_for('pricing') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-up me-1"></i>Upgrade Now
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- API Access Available -->
    <div class="row g-3">
        <!-- API Status Card -->
        <div class="col-md-4">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-signal me-2"></i>API Status
                </h5>
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        {% if company.api_enabled %}
                        <span class="badge bg-success fs-6">{{ t('Active') }}</span>
                        {% else %}
                        <span class="badge bg-secondary fs-6">Disabled</span>
                        {% endif %}
                    </div>
                    <form method="POST" action="{{ url_for('toggle_api_access') }}" class="d-inline">
                        <button type="submit" class="btn btn-sm {% if company.api_enabled %}btn-outline-danger{% else %}btn-outline-success{% endif %}">
                            {% if company.api_enabled %}
                            <i class="fas fa-pause me-1"></i>Disable
                            {% else %}
                            <i class="fas fa-play me-1"></i>Enable
                            {% endif %}
                        </button>
                    </form>
                </div>
                
                {% if company.api_last_used %}
                <div class="small text-muted">
                    <i class="fas fa-clock me-1"></i>
                    Last used: {{ company.api_last_used.strftime('%Y-%m-%d %H:%M') }}
                </div>
                {% else %}
                <div class="small text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    API has not been used yet
                </div>
                {% endif %}
            </div>
        </div>

        <!-- API Credentials Card -->
        <div class="col-md-8">
            <div class="glass-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0">
                        <i class="fas fa-key me-2"></i>API Credentials
                    </h5>
                    <form method="POST" action="{{ url_for('generate_api_keys') }}" class="d-inline">
                        <button type="submit" class="btn btn-outline-primary btn-sm" 
                                onclick="return confirm('This will generate new credentials and invalidate the old ones. Continue?')">
                            <i class="fas fa-refresh me-1"></i>Regenerate
                        </button>
                    </form>
                </div>
                
                {% if company.api_key and company.api_secret %}
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label fw-bold">API Key</label>
                        <div class="input-group">
                            <input type="text" class="form-control font-monospace" 
                                   value="{{ company.api_key }}" readonly id="apiKey">
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="copyToClipboard('apiKey')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">API Secret</label>
                        <div class="input-group">
                            <input type="password" class="form-control font-monospace" 
                                   value="{{ company.api_secret }}" readonly id="apiSecret">
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('apiSecret')">
                                <i class="fas fa-eye" id="apiSecretIcon"></i>
                            </button>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="copyToClipboard('apiSecret')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Keep your credentials secure!</strong> Never share your API secret or commit it to version control.
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-key fa-2x text-muted mb-3"></i>
                    <p class="text-muted mb-3">No API credentials generated yet.</p>
                    <form method="POST" action="{{ url_for('generate_api_keys') }}" class="d-inline">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Generate API Credentials
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- API Documentation -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-book me-2"></i>API Documentation
                </h5>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Authentication</h6>
                        <p class="small text-muted">Include these headers in all API requests:</p>
                        <pre class="bg-light p-3 rounded small"><code>X-API-Key: your_api_key
X-API-Secret: your_api_secret</code></pre>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">Base URL</h6>
                        <p class="small text-muted">All API endpoints are relative to:</p>
                        <pre class="bg-light p-3 rounded small"><code>{{ request.url_root }}api/v1/</code></pre>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6 class="fw-bold">Available Endpoints</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Endpoint</th>
                                    <th>{{ t('Description') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-success">POST</span></td>
                                    <td><code>/payments</code></td>
                                    <td>Create a new payment confirmation</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">GET</span></td>
                                    <td><code>/payments/{id}</code></td>
                                    <td>Get payment status by ID</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">GET</span></td>
                                    <td><code>/analytics</code></td>
                                    <td>Get company analytics data</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>View Full Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-success"></i>';
    setTimeout(() => {
        button.innerHTML = originalIcon;
    }, 2000);
}

function togglePassword(elementId) {
    const element = document.getElementById(elementId);
    const icon = document.getElementById(elementId + 'Icon');
    
    if (element.type === 'password') {
        element.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        element.type = 'password';
        icon.className = 'fas fa-eye';
    }
}
</script>
{% endblock %}
