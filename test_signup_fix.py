#!/usr/bin/env python3
"""
Test the signup fix with special invoice
"""

from app import app, db, PaymentConfirmation, Invoice
from datetime import datetime

def test_signup_fix():
    """Test the signup fix"""
    with app.app_context():
        print("=== TESTING SIGNUP FIX ===")
        
        try:
            # Get or create a special "SIGNUP" invoice for all signup payments
            signup_invoice = Invoice.query.filter_by(invoice_number='SIGNUP-GENERAL').first()
            if not signup_invoice:
                print("Creating special signup invoice...")
                signup_invoice = Invoice(
                    invoice_number='SIGNUP-GENERAL',
                    customer_name='Signup Payments',
                    customer_email='<EMAIL>',
                    amount=0.0,
                    service_description='General signup invoice for new registrations',
                    status='Paid',
                    created_at=datetime.utcnow()
                )
                db.session.add(signup_invoice)
                db.session.flush()
                print(f"✅ Created signup invoice with ID: {signup_invoice.id}")
            else:
                print(f"✅ Found existing signup invoice with ID: {signup_invoice.id}")
            
            # Now test creating a payment confirmation
            confirmation = PaymentConfirmation(
                invoice_id=signup_invoice.id,
                customer_name='John Doe',
                customer_email='<EMAIL>',
                mobile_money_sender_name='John Doe',
                amount=700000.0,
                mobile_operator='M-Pesa',
                transaction_id='ND12345679',  # Different ID to avoid conflicts
                service_description='Signup for Business package'
            )
            
            print("Testing payment confirmation creation...")
            
            # Validate
            confirmation.validate_amount()
            confirmation.validate_transaction_id_format()
            confirmation.validate_mobile_money_name()
            print("✅ All validations passed")
            
            # Save to database
            db.session.add(confirmation)
            db.session.commit()
            print(f"✅ Payment confirmation created with ID: {confirmation.id}")
            
            # Clean up
            db.session.delete(confirmation)
            db.session.commit()
            print("✅ Cleanup successful")
            
            print("\n🎉 SIGNUP FIX WORKING!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    test_signup_fix()
