{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Ankara Management - Malipo System
{%- else -%}
    Invoice Management - Payment System
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-file-invoice me-2"></i>Invoice Management</h1>
    <div>
        <a href="{{ url_for('create_invoice') }}" class="btn btn-info me-2">
            <i class="fas fa-plus me-1"></i>Create Invoice
        </a>
        <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status Filter</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statuses</option>
                    <option value="Unpaid" {% if status_filter == 'Unpaid' %}selected{% endif %}>Unpaid</option>
                    <option value="Paid" {% if status_filter == 'Paid' %}selected{% endif %}>Paid</option>
                    <option value="Overdue" {% if status_filter == 'Overdue' %}selected{% endif %}>Overdue</option>
                    <option value="Cancelled" {% if status_filter == 'Cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">{{ t('Search') }}</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Search by customer name or invoice number..." value="{{ search }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ url_for('admin_invoices') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>Invoices
            <span class="badge bg-secondary ms-2">{{ invoices|length }} records</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Invoice #</th>
                        <th>Customer</th>
                        <th>Amount (TZS)</th>
                        <th>Service</th>
                        <th>Created</th>
                        <th>Due Date</th>
                        <th>{{ t('Status') }}</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td><code>{{ invoice.invoice_number }}</code></td>
                        <td>
                            {{ invoice.customer_name }}
                            {% if invoice.customer_email %}
                                <br><small class="text-muted">{{ invoice.customer_email }}</small>
                            {% endif %}
                        </td>
                        <td>{{ "{:,.0f}"|format(invoice.amount) }}</td>
                        <td>
                            <span class="d-inline-block text-truncate" style="max-width: 200px;" title="{{ invoice.service_description }}">
                                {{ invoice.service_description }}
                            </span>
                        </td>
                        <td>{{ invoice.created_at.strftime('%d/%m/%Y') }}</td>
                        <td>
                            <span class="{% if invoice.due_date < now and invoice.status == 'Unpaid' %}text-danger{% endif %}">
                                {{ invoice.due_date.strftime('%d/%m/%Y') }}
                            </span>
                        </td>
                        <td>
                            <span class="badge 
                                {% if invoice.status == 'Unpaid' %}bg-warning text-dark
                                {% elif invoice.status == 'Paid' %}bg-success
                                {% elif invoice.status == 'Overdue' %}bg-danger
                                {% elif invoice.status == 'Cancelled' %}bg-secondary
                                {% else %}bg-info{% endif %}">
                                {{ invoice.status }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_invoice', invoice_number=invoice.invoice_number) }}" 
                                   class="btn btn-outline-primary" title="View Invoice">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if invoice.status == 'Unpaid' or invoice.status == 'Overdue' %}
                                <a href="{{ url_for('payment_instructions_with_invoice', invoice_number=invoice.invoice_number) }}" 
                                   class="btn btn-outline-info" title="Payment Instructions">
                                    <i class="fas fa-mobile-alt"></i>
                                </a>
                                {% endif %}
                                {% if invoice.payment_confirmations %}
                                    {% for payment in invoice.payment_confirmations %}
                                        {% if payment.status == 'Confirmed' %}
                                        <a href="{{ url_for('payment_detail', payment_id=payment.id) }}" 
                                           class="btn btn-outline-success" title="View Payment">
                                            <i class="fas fa-receipt"></i>
                                        </a>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No invoices found</h5>
            <p class="text-muted">
                {% if status_filter != 'all' or search %}
                    Try adjusting your filters or search terms.
                {% else %}
                    Create your first invoice to get started.
                {% endif %}
            </p>
            <a href="{{ url_for('create_invoice') }}" class="btn btn-info">
                <i class="fas fa-plus me-1"></i>Create Invoice
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
