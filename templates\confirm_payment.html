{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}
    Thibitisha Malipo - M<PERSON><PERSON> wa Malipo
{% else %}
    Confirm Payment - Payment System
{% endif %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    {% if session.language == 'sw' %}
                        Thibitisha Malipo Yako
                    {% else %}
                        Confirm Your Payment
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-sms me-2"></i>
                    <strong>
                        {% if session.language == 'sw' %}
                            Baada ya kufanya malipo yako ya pesa za simu, ulipokea SMS yenye kitambulisho cha muamala. Tafadhali kiingize hapa chini ili kuthibitisha malipo yako.
                        {% else %}
                            After making your mobile money payment, you received an SMS with a transaction ID. Please enter it below to confirm your payment.
                        {% endif %}
                    </strong>
                </div>

                <form method="POST" id="confirmationForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    {% if session.language == 'sw' %}
                                        Jina Lako Kamili *
                                    {% else %}
                                        Your Full Name *
                                    {% endif %}
                                </label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name"
                                       placeholder="e.g., John Doe" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    {% if session.language == 'sw' %}
                                        Anwani ya Barua Pepe Yako *
                                    {% else %}
                                        Your Email Address *
                                    {% endif %}
                                </label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email"
                                       placeholder="e.g., <EMAIL>" required>
                                <div class="form-text">We'll send your registration link to this email</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mobile_money_sender_name" class="form-label">
                                    <i class="fas fa-user-check me-1"></i>Mobile Money Registered Name *
                                </label>
                                <input type="text" class="form-control" id="mobile_money_sender_name" name="mobile_money_sender_name"
                                       placeholder="e.g., John Doe" required>
                                <div class="form-text">Enter the exact name registered with your mobile money account</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>Amount Paid (TZS) *
                                </label>
                                <input type="number" class="form-control" id="amount" name="amount"
                                       placeholder="e.g., 15000" min="1000" max="********" step="1" required>
                                <div class="form-text">Minimum: TZS 1,000 | Maximum: TZS 10,000,000</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mobile_operator" class="form-label">
                                    <i class="fas fa-mobile-alt me-1"></i>Payment Method Used *
                                </label>
                                <select class="form-select" id="mobile_operator" name="mobile_operator" required>
                                    <option value="">Select payment method...</option>
                                    <option value="M-Pesa">M-Pesa (Vodacom)</option>
                                    <option value="TigoPesa">{{ t('Tigo Pesa') }}</option>
                                    <option value="Airtel Money">{{ t('Airtel Money') }}</option>
                                    <option value="CRDB Lipa">{{ t('CRDB Lipa') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="transaction_id" class="form-label">
                                    <i class="fas fa-sms me-1"></i>Mobile Money Transaction ID *
                                </label>
                                <input type="text" class="form-control" id="transaction_id" name="transaction_id"
                                       placeholder="e.g., TP87654321, ND12345678, AM99887766" required>
                                <div class="form-text">
                                    <i class="fas fa-mobile-alt me-1"></i>
                                    Enter the transaction ID from your SMS (M-Pesa: ND..., Tigo: TP..., Airtel: AM...)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="service_description" class="form-label">
                            <i class="fas fa-clipboard-list me-1"></i>Service/Product Paid For (Optional)
                        </label>
                        <textarea class="form-control" id="service_description" name="service_description" 
                                  rows="3" placeholder="e.g., Monthly subscription, Product order, Service payment"></textarea>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check me-2"></i>Apply payment
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h6 class="text-muted">
                    <i class="fas fa-shield-alt me-2"></i>What happens next?
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Your confirmation will be submitted securely</li>
                    <li><i class="fas fa-search text-info me-2"></i>Our team will verify your payment with mobile money records</li>
                    <li><i class="fas fa-bell text-warning me-2"></i>You'll be notified once verification is complete</li>
                    <li><i class="fas fa-receipt text-primary me-2"></i>A digital receipt will be generated upon confirmation</li>
                </ul>
            </div>
        </div>

        <div class="text-center mt-3">
            <p class="text-muted">
                Need to make a payment first? 
                <a href="{{ url_for('payment_instructions') }}">View Payment Instructions</a>
            </p>
        </div>
    </div>
</div>

<script>
document.getElementById('confirmationForm').addEventListener('submit', function(e) {
    const amount = document.getElementById('amount').value;
    const transactionId = document.getElementById('transaction_id').value;
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Please enter a valid amount greater than 0');
        return;
    }
    
    if (transactionId.length < 6) {
        e.preventDefault();
        alert('Transaction ID seems too short. Please double-check your transaction ID.');
        return;
    }
});
</script>
{% endblock %}
