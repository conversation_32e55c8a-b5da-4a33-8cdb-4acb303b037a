{% extends "base.html" %}

{% block title %}Pricing & Packages - EXLIPA{% endblock %}

{% block content %}
<div class="pricing-bg-gradient py-5">
    <div class="container">
        <!-- Header Section -->
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold elegant-gradient-text">
                {% if session.language == 'sw' %}
                    Chagua Mpango Wako wa EXLIPA
                {% else %}
                    Choose Your EXLIPA Plan
                {% endif %}
            </h1>
            <p class="lead text-muted">
                {% if session.language == 'sw' %}
                    Vifurushi vya kubadilika kwa kila biashara. Anza na vipengele sahihi na ukue kadri unavyokua.
                {% else %}
                    Flexible packages for every business. Start with the right features and scale as you grow.
                {% endif %}
            </p>
        </div>

        <!-- Pricing Cards -->
        <div class="row justify-content-center g-4">
            {% for tier in pricing_tiers %}
            <div class="col-12 col-md-6 col-xl-4">
                <div class="compact-pricing-card {% if tier.name == 'Business' %}popular-tier{% endif %} position-relative">
                    {% if tier.name == 'Business' %}
                    <div class="popular-badge">
                        {% if session.language == 'sw' %}
                            MAARUFU
                        {% else %}
                            POPULAR
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Card Header -->
                    <div class="card-header-compact">
                        <div class="tier-icon">
                            {% if tier.name == 'Free' %}
                                <i class="fas fa-gift"></i>
                            {% elif tier.name == 'Business' %}
                                <i class="fas fa-building"></i>
                            {% else %}
                                <i class="fas fa-crown"></i>
                            {% endif %}
                        </div>
                        <h4 class="tier-name">
                            {% if session.language == 'sw' %}
                                {% if tier.name == 'Free' %}Bure{% elif tier.name == 'Business' %}Biashara{% else %}Makampuni Makubwa{% endif %}
                            {% else %}
                                {{ tier.name }}
                            {% endif %}
                        </h4>
                        <p class="tier-description">
                            {% if session.language == 'sw' %}
                                {% if tier.name == 'Free' %}Anza bure - miamala 100 ya kwanza{% elif tier.name == 'Business' %}Kwa makampuni yanayokua{% else %}Kwa makampuni makubwa{% endif %}
                            {% else %}
                                {{ tier.description }}
                            {% endif %}
                        </p>
                    </div>

                    <!-- Pricing -->
                    <div class="pricing-section">
                        {% if tier.name == 'Free' %}
                        <div class="price-main">
                            <span class="currency">TSh</span>
                            <span class="amount">0</span>
                            <span class="period">
                                {% if session.language == 'sw' %}/mwezi{% else %}/month{% endif %}
                            </span>
                        </div>
                        <div class="setup-fee text-success">
                            {% if session.language == 'sw' %}
                                ✓ Hakuna ada za kuanzisha
                            {% else %}
                                ✓ No setup fees
                            {% endif %}
                        </div>
                        {% else %}
                        <div class="price-main">
                            <span class="currency">TSh</span>
                            <span class="amount">{{ "{:,}".format(tier.monthly_fee|int) }}</span>
                            <span class="period">
                                {% if session.language == 'sw' %}/mwezi{% else %}/month{% endif %}
                            </span>
                        </div>
                        <div class="setup-fee text-success">
                            {% if session.language == 'sw' %}
                                ✓ Hakuna ada za kuanzisha
                            {% else %}
                                ✓ No setup fees
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Features -->
                    <div class="features-section">
                        {% if tier.name == 'Free' %}
                        <div class="feature-item">
                            <i class="fas fa-gift feature-icon text-success"></i>
                            <span class="text-success">
                                {% if session.language == 'sw' %}
                                    100 miamala ya bure kila mwezi
                                {% else %}
                                    100 free transactions monthly
                                {% endif %}
                            </span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-percentage feature-icon"></i>
                            <span>{{ tier.transaction_fee_percentage }}%
                                {% if session.language == 'sw' %}ada baada ya kikomo{% else %}fee after limit{% endif %}
                            </span>
                        </div>
                        {% else %}
                        <div class="feature-item">
                            <i class="fas fa-percentage feature-icon"></i>
                            <span>{{ tier.transaction_fee_percentage }}%
                                {% if session.language == 'sw' %}ada ya muamala{% else %}transaction fee{% endif %}
                            </span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-exchange-alt feature-icon"></i>
                            <span>
                                {% if tier.max_transactions_per_month %}
                                    {{ "{:,}".format(tier.max_transactions_per_month) }}
                                    {% if session.language == 'sw' %}miamala/mwezi{% else %}transactions/month{% endif %}
                                {% else %}
                                    {% if session.language == 'sw' %}Miamala isiyo na kikomo{% else %}Unlimited transactions{% endif %}
                                {% endif %}
                            </span>
                        </div>
                        {% endif %}

                        <!-- Key Features -->
                        {% if tier.analytics_dashboard %}
                        <div class="feature-item">
                            <i class="fas fa-chart-bar feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Dashibodi ya uchambuzi{% else %}Analytics dashboard{% endif %}</span>
                        </div>
                        {% endif %}

                        {% if tier.api_access %}
                        <div class="feature-item">
                            <i class="fas fa-code feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Ufikiaji wa API{% else %}API access{% endif %}</span>
                        </div>
                        {% endif %}

                        {% if tier.custom_branding %}
                        <div class="feature-item">
                            <i class="fas fa-palette feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Ubunifu wa kibinafsi{% else %}Custom branding{% endif %}</span>
                        </div>
                        {% endif %}

                        {% if tier.priority_support %}
                        <div class="feature-item">
                            <i class="fas fa-headset feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Msaada wa kipaumbele{% else %}Priority support{% endif %}</span>
                        </div>
                        {% endif %}

                        {% if tier.multi_location %}
                        <div class="feature-item">
                            <i class="fas fa-map-marker-alt feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Maeneo mengi{% else %}Multi-location{% endif %}</span>
                        </div>
                        {% endif %}

                        {% if tier.white_label %}
                        <div class="feature-item">
                            <i class="fas fa-tag feature-icon"></i>
                            <span>{% if session.language == 'sw' %}Suluhisho la nembo yako{% else %}White-label solution{% endif %}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- CTA Button -->
                    <div class="cta-section">
                        {% if tier.name == 'Free' %}
                        <a href="{{ url_for('free_signup') }}" class="btn-cta btn-success">
                            {% if session.language == 'sw' %}
                                Anza Bure - Hakuna Malipo!
                            {% else %}
                                Start Free - No Payment!
                            {% endif %}
                        </a>
                        {% else %}
                        <a href="{{ url_for('start_signup', tier_id=tier.id) }}" class="btn-cta">
                            {% if session.language == 'sw' %}
                                Chagua Mpango
                            {% else %}
                                Choose Plan
                            {% endif %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Back Button -->
        <div class="text-center mt-5">
            <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>
                {% if session.language == 'sw' %}
                    Rudi Nyumbani
                {% else %}
                    Back to Home
                {% endif %}
            </a>
        </div>
    </div>
</div>

<style>
/* Background */
.pricing-bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
}

.pricing-bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
}

.container {
    position: relative;
    z-index: 1;
}

/* Header */
.elegant-gradient-text {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.lead {
    color: #f7fafc !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Compact Pricing Cards */
.compact-pricing-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    max-width: 320px;
    margin: 0 auto;
}

.compact-pricing-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.popular-tier {
    border: 2px solid #ffd700;
    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.2);
}

.popular-tier:hover {
    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.3);
}

/* Popular Badge */
.popular-badge {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a202c;
    font-weight: 700;
    font-size: 0.75rem;
    padding: 8px 20px;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    z-index: 10;
    letter-spacing: 1px;
    border: 2px solid #fff;
}

/* Card Header */
.card-header-compact {
    text-align: center;
    padding: 30px 20px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
}

.tier-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tier-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 8px;
}

.tier-description {
    color: #4a5568;
    font-size: 0.9rem;
    margin: 0;
    font-weight: 500;
}

/* Pricing Section */
.pricing-section {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
}

.price-main {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 10px;
}

.currency {
    font-size: 1rem;
    color: #2d3748;
    margin-right: 4px;
    font-weight: 600;
}

.amount {
    font-size: 2.2rem;
    font-weight: 800;
    color: #2d3748;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* Fallback for browsers that don't support background-clip */
    -webkit-text-stroke: 1px #667eea;
}

.period {
    font-size: 0.9rem;
    color: #4a5568;
    margin-left: 4px;
    font-weight: 500;
}

.setup-fee {
    font-size: 0.85rem;
    color: #2d3748;
    background: rgba(102, 126, 234, 0.15);
    padding: 8px 14px;
    border-radius: 12px;
    display: inline-block;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Features Section */
.features-section {
    padding: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    font-size: 0.95rem;
    color: #1a202c;
    font-weight: 500;
    line-height: 1.4;
}

.feature-icon {
    width: 18px;
    height: 18px;
    margin-right: 14px;
    color: #38a169;
    flex-shrink: 0;
    font-weight: 600;
}

/* CTA Section */
.cta-section {
    padding: 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.btn-cta {
    display: block;
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-cta:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
    text-decoration: none;
}

/* Popular tier CTA */
.popular-tier .btn-cta {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a202c;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.popular-tier .btn-cta:hover {
    background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
    color: #1a202c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.2rem;
    }

    .compact-pricing-card {
        max-width: 100%;
        margin-bottom: 30px;
    }

    .amount {
        font-size: 2rem;
    }

    .tier-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .card-header-compact {
        padding: 25px 15px 15px;
    }

    .pricing-section,
    .features-section,
    .cta-section {
        padding: 15px;
    }

    .amount {
        font-size: 1.8rem;
    }
}
</style>
{% endblock %}
