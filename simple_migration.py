#!/usr/bin/env python3
"""
Simple database migration for security fixes
"""

from app import app, db
import sqlite3
import os

def migrate_database():
    """Apply database migrations using direct SQL"""
    with app.app_context():
        print("=== SIMPLE MIGRATION ===")
        
        # Get database path
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
        else:
            db_path = 'var/app-instance/payment_system.db'

        print(f"Database: {db_path}")

        # Ensure database exists
        if not os.path.exists(db_path):
            print("Creating database...")
            db.create_all()

        # Connect directly to SQLite
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 1. Add columns to user table
            print("1. Adding user table columns...")
            
            # Check existing columns
            cursor.execute("PRAGMA table_info(user)")
            existing_columns = [row[1] for row in cursor.fetchall()]
            
            if 'failed_login_attempts' not in existing_columns:
                cursor.execute('ALTER TABLE user ADD COLUMN failed_login_attempts INTEGER DEFAULT 0')
                print("   ✅ Added failed_login_attempts")
            
            if 'account_locked_until' not in existing_columns:
                cursor.execute('ALTER TABLE user ADD COLUMN account_locked_until DATETIME')
                print("   ✅ Added account_locked_until")
            
            # 2. Add columns to payment_confirmation table
            print("2. Adding payment_confirmation table columns...")
            
            cursor.execute("PRAGMA table_info(payment_confirmation)")
            payment_columns = [row[1] for row in cursor.fetchall()]
            
            if 'mobile_money_sender_name' not in payment_columns:
                cursor.execute('ALTER TABLE payment_confirmation ADD COLUMN mobile_money_sender_name VARCHAR(100)')
                print("   ✅ Added mobile_money_sender_name")
            
            # SMS verification fields
            sms_fields = [
                ('sms_sender', 'VARCHAR(20)'),
                ('sms_amount', 'FLOAT'),
                ('sms_sender_name', 'VARCHAR(100)'),
                ('sms_transaction_ref', 'VARCHAR(100)'),
                ('admin_verification_notes', 'TEXT')
            ]
            
            for field_name, field_type in sms_fields:
                if field_name not in payment_columns:
                    cursor.execute(f'ALTER TABLE payment_confirmation ADD COLUMN {field_name} {field_type}')
                    print(f"   ✅ Added {field_name}")
            
            # 3. Add owner_user_id to client_company table
            print("3. Adding client_company table columns...")
            
            cursor.execute("PRAGMA table_info(client_company)")
            company_columns = [row[1] for row in cursor.fetchall()]
            
            if 'owner_user_id' not in company_columns:
                cursor.execute('ALTER TABLE client_company ADD COLUMN owner_user_id INTEGER')
                print("   ✅ Added owner_user_id")
            
            # 4. Update role names
            print("4. Updating role names...")
            cursor.execute("UPDATE user SET role = 'master_admin' WHERE role = 'super_admin'")
            cursor.execute("UPDATE user SET role = 'user_admin' WHERE role = 'admin' AND username != 'admin'")
            print("   ✅ Updated role names")
            
            # 5. Set default mobile money sender names
            print("5. Setting default mobile money sender names...")
            cursor.execute('''
                UPDATE payment_confirmation 
                SET mobile_money_sender_name = customer_name 
                WHERE mobile_money_sender_name IS NULL
            ''')
            print("   ✅ Set default sender names")
            
            # 6. Link companies to users
            print("6. Linking companies to users...")
            cursor.execute('''
                UPDATE client_company 
                SET owner_user_id = (
                    SELECT id FROM user 
                    WHERE user.email = client_company.company_email 
                    LIMIT 1
                )
                WHERE owner_user_id IS NULL
            ''')
            print("   ✅ Linked companies to users")
            
            # Commit all changes
            conn.commit()
            print()
            print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            
            # Verification
            print()
            print("Verification:")
            
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()[0]
            print(f"   Users: {user_count}")
            
            cursor.execute("SELECT COUNT(*) FROM payment_confirmation")
            payment_count = cursor.fetchone()[0]
            print(f"   Payments: {payment_count}")
            
            cursor.execute("SELECT COUNT(*) FROM client_company")
            company_count = cursor.fetchone()[0]
            print(f"   Companies: {company_count}")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

if __name__ == '__main__':
    migrate_database()
