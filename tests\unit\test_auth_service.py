#!/usr/bin/env python3
"""
Unit tests for authentication service
"""

import pytest
from unittest.mock import Mock, patch
from services.auth_service import AuthenticationManager
from models.user import User
from utils.validators import ValidationError

class TestAuthenticationManager:
    """Test AuthenticationManager class"""
    
    def test_get_role_info(self):
        """Test getting role information"""
        # Test valid role
        role_info = AuthenticationManager.get_role_info('super_admin')
        assert role_info['name'] == 'Super Admin'
        assert 'all' in role_info['permissions']
        
        # Test invalid role
        role_info = AuthenticationManager.get_role_info('invalid_role')
        assert role_info == {}
    
    def test_has_permission(self):
        """Test permission checking"""
        # Super admin has all permissions
        assert AuthenticationManager.has_permission('super_admin', 'payments') == True
        assert AuthenticationManager.has_permission('super_admin', 'any_permission') == True
        
        # User admin has specific permissions
        assert AuthenticationManager.has_permission('user_admin', 'payments') == True
        assert AuthenticationManager.has_permission('user_admin', 'company_management') == True
        
        # Company user has limited permissions
        assert AuthenticationManager.has_permission('company_user', 'pos') == True
        assert AuthenticationManager.has_permission('company_user', 'company_management') == False
        
        # Invalid role
        assert AuthenticationManager.has_permission('invalid_role', 'payments') == False
    
    def test_get_dashboard_for_role(self):
        """Test dashboard routing for roles"""
        assert AuthenticationManager.get_dashboard_for_role('super_admin') == 'master_admin_dashboard'
        assert AuthenticationManager.get_dashboard_for_role('user_admin') == 'user_admin_dashboard'
        assert AuthenticationManager.get_dashboard_for_role('company_user') == 'company_dashboard'
        assert AuthenticationManager.get_dashboard_for_role('invalid_role') == 'user_admin_login'
    
    def test_get_login_route_for_role(self):
        """Test login route for roles"""
        assert AuthenticationManager.get_login_route_for_role('super_admin') == 'master_admin_login'
        assert AuthenticationManager.get_login_route_for_role('user_admin') == 'user_admin_login'
        assert AuthenticationManager.get_login_route_for_role('company_user') == 'company_login'
    
    @patch('services.auth_service.login_user')
    @patch('services.auth_service.session')
    @patch('services.auth_service.logger')
    def test_login_user_with_role_success(self, mock_logger, mock_session, mock_login_user):
        """Test successful user login with role"""
        # Mock user
        mock_user = Mock()
        mock_user.role = 'user_admin'
        mock_user.id = 1
        mock_user.username = 'testuser'
        mock_user.last_login = None
        
        # Mock successful login
        mock_login_user.return_value = True
        
        # Test login
        result = AuthenticationManager.login_user_with_role(mock_user)
        
        # Assertions
        assert result == True
        mock_login_user.assert_called_once_with(mock_user, remember=False)
        mock_logger.info.assert_called_once()
        
        # Check session data is set
        assert mock_session.__setitem__.call_count >= 3  # user_role, user_id, login_time
    
    @patch('services.auth_service.login_user')
    @patch('services.auth_service.logger')
    def test_login_user_with_role_failure(self, mock_logger, mock_login_user):
        """Test failed user login with role"""
        # Mock user
        mock_user = Mock()
        mock_user.username = 'testuser'
        
        # Mock failed login (exception)
        mock_login_user.side_effect = Exception("Login failed")
        
        # Test login
        result = AuthenticationManager.login_user_with_role(mock_user)
        
        # Assertions
        assert result == False
        mock_logger.error.assert_called_once()
    
    @patch('services.auth_service.logout_user')
    @patch('services.auth_service.session')
    @patch('services.auth_service.current_user')
    @patch('services.auth_service.logger')
    def test_logout_user_safe(self, mock_logger, mock_current_user, mock_session, mock_logout_user):
        """Test safe user logout"""
        # Mock authenticated user
        mock_current_user.is_authenticated = True
        mock_current_user.username = 'testuser'
        
        # Test logout
        AuthenticationManager.logout_user_safe()
        
        # Assertions
        mock_logout_user.assert_called_once()
        mock_logger.info.assert_called_once()
        
        # Check session data is cleared
        mock_session.pop.assert_called()

class TestRoleDecorators:
    """Test role-based decorators"""
    
    @patch('services.auth_service.current_user')
    def test_require_role_decorator_success(self, mock_current_user):
        """Test role decorator with correct role"""
        from services.auth_service import require_role
        
        # Mock authenticated user with correct role
        mock_current_user.is_authenticated = True
        mock_current_user.role = 'super_admin'
        
        # Create decorated function
        @require_role('super_admin')
        def test_function():
            return "success"
        
        # Test function call
        result = test_function()
        assert result == "success"
    
    @patch('services.auth_service.current_user')
    @patch('services.auth_service.AuthenticationManager')
    def test_require_role_decorator_wrong_role(self, mock_auth_manager, mock_current_user):
        """Test role decorator with wrong role"""
        from services.auth_service import require_role
        from flask import Flask
        
        # Mock authenticated user with wrong role
        mock_current_user.is_authenticated = True
        mock_current_user.role = 'user_admin'
        
        # Mock redirect
        mock_auth_manager.smart_redirect_by_role.return_value = "redirect_response"
        
        # Create decorated function
        @require_role('super_admin')
        def test_function():
            return "success"
        
        # Test function call with Flask app context
        app = Flask(__name__)
        with app.app_context():
            with app.test_request_context():
                result = test_function()
                assert result == "redirect_response"
    
    @patch('services.auth_service.current_user')
    def test_require_permission_decorator(self, mock_current_user):
        """Test permission decorator"""
        from services.auth_service import require_permission
        
        # Mock authenticated user
        mock_current_user.is_authenticated = True
        mock_current_user.role = 'super_admin'
        
        # Create decorated function
        @require_permission('payments')
        def test_function():
            return "success"
        
        # Test function call
        result = test_function()
        assert result == "success"
    
    @patch('services.auth_service.current_user')
    def test_require_any_role_decorator(self, mock_current_user):
        """Test any role decorator"""
        from services.auth_service import require_any_role
        
        # Mock authenticated user
        mock_current_user.is_authenticated = True
        mock_current_user.role = 'user_admin'
        
        # Create decorated function
        @require_any_role('super_admin', 'user_admin')
        def test_function():
            return "success"
        
        # Test function call
        result = test_function()
        assert result == "success"
    
    @patch('services.auth_service.current_user')
    @patch('services.auth_service.session')
    @patch('services.auth_service.datetime')
    def test_session_timeout_check(self, mock_datetime, mock_session, mock_current_user):
        """Test session timeout check"""
        from services.auth_service import session_timeout_check
        from datetime import datetime, timedelta
        
        # Mock current time
        current_time = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.utcnow.return_value = current_time
        mock_datetime.fromisoformat.return_value = current_time - timedelta(minutes=30)
        
        # Mock authenticated user
        mock_current_user.is_authenticated = True
        
        # Mock session with recent login
        mock_session.get.return_value = (current_time - timedelta(minutes=30)).isoformat()
        
        # Create decorated function
        @session_timeout_check(timeout_minutes=60)
        def test_function():
            return "success"
        
        # Test function call
        result = test_function()
        assert result == "success"

class TestAuthenticationIntegration:
    """Integration tests for authentication"""
    
    def test_user_model_integration(self, app):
        """Test authentication with User model"""
        with app.app_context():
            # Create test user
            user = User(
                username='testuser',
                email='<EMAIL>',
                full_name='Test User',
                role='user_admin'
            )
            user.set_password('TestPassword123!')
            user.save()
            
            # Test password verification
            assert user.check_password('TestPassword123!') == True
            assert user.check_password('wrongpassword') == False
            
            # Test role checking
            assert user.has_role('user_admin') == True
            assert user.has_role('super_admin') == False
            
            # Test account locking
            assert user.is_account_locked() == False
            
            # Simulate failed logins
            for _ in range(5):
                user.increment_failed_login()
            
            assert user.is_account_locked() == True
            
            # Test account unlock
            user.unlock_account()
            assert user.is_account_locked() == False
            assert user.failed_login_attempts == 0
