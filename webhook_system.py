# EXLIPA Webhook System

import json
import hmac
import hashlib
import requests
import time
from datetime import datetime
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
from enum import Enum

class WebhookEventType(Enum):
    PAYMENT_CREATED = "payment.created"
    PAYMENT_CONFIRMED = "payment.confirmed"
    PAYMENT_FAILED = "payment.failed"
    INVOICE_CREATED = "invoice.created"
    INVOICE_PAID = "invoice.paid"
    COMPANY_CREATED = "company.created"
    USER_REGISTERED = "user.registered"

@dataclass
class WebhookEvent:
    event_type: WebhookEventType
    data: Dict
    timestamp: datetime
    company_id: Optional[int] = None
    user_id: Optional[int] = None

class WebhookManager:
    def __init__(self, app=None):
        self.app = app
        self.webhooks = {}  # company_id -> list of webhook configs
        self.event_queue = []
        self.retry_attempts = 3
        self.timeout = 30
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize webhook system with Flask app"""
        self.app = app
        app.webhook_manager = self
    
    def register_webhook(self, company_id: int, url: str, events: List[WebhookEventType], 
                        secret: str = None, active: bool = True):
        """Register a webhook for a company"""
        if company_id not in self.webhooks:
            self.webhooks[company_id] = []
        
        webhook_config = {
            'url': url,
            'events': events,
            'secret': secret,
            'active': active,
            'created_at': datetime.now(),
            'last_success': None,
            'last_failure': None,
            'failure_count': 0
        }
        
        self.webhooks[company_id].append(webhook_config)
        return webhook_config
    
    def trigger_event(self, event: WebhookEvent):
        """Trigger webhook event for relevant companies"""
        target_companies = []
        
        if event.company_id:
            target_companies = [event.company_id]
        else:
            # Global events - send to all companies with webhooks
            target_companies = list(self.webhooks.keys())
        
        for company_id in target_companies:
            if company_id in self.webhooks:
                for webhook_config in self.webhooks[company_id]:
                    if (webhook_config['active'] and 
                        event.event_type in webhook_config['events']):
                        self._send_webhook(webhook_config, event)
    
    def _send_webhook(self, webhook_config: Dict, event: WebhookEvent):
        """Send webhook to endpoint"""
        payload = {
            'event': event.event_type.value,
            'timestamp': event.timestamp.isoformat(),
            'data': event.data
        }
        
        if event.company_id:
            payload['company_id'] = event.company_id
        if event.user_id:
            payload['user_id'] = event.user_id
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'EXLIPA-Webhook/1.0'
        }
        
        # Add signature if secret is provided
        if webhook_config['secret']:
            signature = self._generate_signature(
                json.dumps(payload, sort_keys=True),
                webhook_config['secret']
            )
            headers['X-EXLIPA-Signature'] = signature
        
        try:
            response = requests.post(
                webhook_config['url'],
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                webhook_config['last_success'] = datetime.now()
                webhook_config['failure_count'] = 0
                logging.info(f"Webhook sent successfully to {webhook_config['url']}")
            else:
                self._handle_webhook_failure(webhook_config, response.status_code)
                
        except requests.exceptions.RequestException as e:
            self._handle_webhook_failure(webhook_config, str(e))
    
    def _generate_signature(self, payload: str, secret: str) -> str:
        """Generate HMAC signature for webhook payload"""
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"
    
    def _handle_webhook_failure(self, webhook_config: Dict, error):
        """Handle webhook delivery failure"""
        webhook_config['last_failure'] = datetime.now()
        webhook_config['failure_count'] += 1
        
        logging.error(f"Webhook failed for {webhook_config['url']}: {error}")
        
        # Disable webhook after too many failures
        if webhook_config['failure_count'] >= 10:
            webhook_config['active'] = False
            logging.warning(f"Webhook disabled due to repeated failures: {webhook_config['url']}")
    
    def get_webhook_stats(self, company_id: int) -> Dict:
        """Get webhook statistics for a company"""
        if company_id not in self.webhooks:
            return {'total': 0, 'active': 0, 'failed': 0}
        
        webhooks = self.webhooks[company_id]
        total = len(webhooks)
        active = sum(1 for w in webhooks if w['active'])
        failed = sum(1 for w in webhooks if w['failure_count'] > 0)
        
        return {
            'total': total,
            'active': active,
            'failed': failed,
            'webhooks': webhooks
        }

class BulkOperationManager:
    """Manager for bulk operations on payments, invoices, etc."""
    
    def __init__(self):
        self.operations = {}
        self.batch_size = 100
    
    def bulk_update_payments(self, payment_ids: List[int], updates: Dict) -> Dict:
        """Bulk update multiple payments"""
        from app import db, PaymentConfirmation
        
        try:
            # Process in batches
            results = {'success': 0, 'failed': 0, 'errors': []}
            
            for i in range(0, len(payment_ids), self.batch_size):
                batch = payment_ids[i:i + self.batch_size]
                
                payments = PaymentConfirmation.query.filter(
                    PaymentConfirmation.id.in_(batch)
                ).all()
                
                for payment in payments:
                    try:
                        for field, value in updates.items():
                            if hasattr(payment, field):
                                setattr(payment, field, value)
                        results['success'] += 1
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append(f"Payment {payment.id}: {str(e)}")
                
                db.session.commit()
            
            return results
            
        except Exception as e:
            db.session.rollback()
            return {'success': 0, 'failed': len(payment_ids), 'errors': [str(e)]}
    
    def bulk_export_data(self, company_id: int, data_type: str, 
                        date_from: datetime, date_to: datetime) -> Dict:
        """Bulk export data for a company"""
        from app import db, PaymentConfirmation, Invoice
        
        try:
            if data_type == 'payments':
                query = PaymentConfirmation.query.filter(
                    PaymentConfirmation.client_company_id == company_id,
                    PaymentConfirmation.submitted_at.between(date_from, date_to)
                )
            elif data_type == 'invoices':
                query = Invoice.query.filter(
                    Invoice.client_company_id == company_id,
                    Invoice.created_at.between(date_from, date_to)
                )
            else:
                return {'error': 'Invalid data type'}
            
            records = query.all()
            
            # Convert to exportable format
            export_data = []
            for record in records:
                record_dict = {}
                for column in record.__table__.columns:
                    value = getattr(record, column.name)
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    record_dict[column.name] = value
                export_data.append(record_dict)
            
            return {
                'success': True,
                'count': len(export_data),
                'data': export_data
            }
            
        except Exception as e:
            return {'error': str(e)}

class AutomationEngine:
    """Automation engine for business rules and workflows"""
    
    def __init__(self):
        self.rules = {}
        self.workflows = {}
    
    def create_automation_rule(self, company_id: int, rule_name: str, 
                             trigger: Dict, actions: List[Dict]) -> str:
        """Create an automation rule"""
        rule_id = f"{company_id}_{rule_name}_{int(time.time())}"
        
        self.rules[rule_id] = {
            'company_id': company_id,
            'name': rule_name,
            'trigger': trigger,
            'actions': actions,
            'active': True,
            'created_at': datetime.now(),
            'execution_count': 0
        }
        
        return rule_id
    
    def process_event_for_automation(self, event: WebhookEvent):
        """Process event against automation rules"""
        for rule_id, rule in self.rules.items():
            if (rule['active'] and 
                rule['company_id'] == event.company_id and
                self._matches_trigger(event, rule['trigger'])):
                
                self._execute_actions(event, rule['actions'])
                rule['execution_count'] += 1
    
    def _matches_trigger(self, event: WebhookEvent, trigger: Dict) -> bool:
        """Check if event matches automation trigger"""
        if trigger.get('event_type') != event.event_type.value:
            return False
        
        # Check conditions
        conditions = trigger.get('conditions', [])
        for condition in conditions:
            field = condition['field']
            operator = condition['operator']
            value = condition['value']
            
            event_value = event.data.get(field)
            
            if operator == 'equals' and event_value != value:
                return False
            elif operator == 'greater_than' and (not event_value or event_value <= value):
                return False
            elif operator == 'less_than' and (not event_value or event_value >= value):
                return False
            elif operator == 'contains' and (not event_value or value not in str(event_value)):
                return False
        
        return True
    
    def _execute_actions(self, event: WebhookEvent, actions: List[Dict]):
        """Execute automation actions"""
        for action in actions:
            action_type = action['type']
            
            try:
                if action_type == 'send_email':
                    self._send_email_action(event, action)
                elif action_type == 'update_status':
                    self._update_status_action(event, action)
                elif action_type == 'create_invoice':
                    self._create_invoice_action(event, action)
                elif action_type == 'webhook':
                    self._webhook_action(event, action)
                
            except Exception as e:
                logging.error(f"Automation action failed: {action_type} - {str(e)}")
    
    def _send_email_action(self, event: WebhookEvent, action: Dict):
        """Send email automation action"""
        # Implement email sending logic
        pass
    
    def _update_status_action(self, event: WebhookEvent, action: Dict):
        """Update status automation action"""
        # Implement status update logic
        pass
    
    def _create_invoice_action(self, event: WebhookEvent, action: Dict):
        """Create invoice automation action"""
        # Implement invoice creation logic
        pass
    
    def _webhook_action(self, event: WebhookEvent, action: Dict):
        """Webhook automation action"""
        # Implement webhook calling logic
        pass

# Global instances
webhook_manager = WebhookManager()
bulk_manager = BulkOperationManager()
automation_engine = AutomationEngine()

# Helper functions for Flask integration
def trigger_payment_webhook(payment_data: Dict, event_type: WebhookEventType, company_id: int):
    """Trigger payment-related webhook"""
    event = WebhookEvent(
        event_type=event_type,
        data=payment_data,
        timestamp=datetime.now(),
        company_id=company_id
    )
    
    webhook_manager.trigger_event(event)
    automation_engine.process_event_for_automation(event)

def setup_default_automations(company_id: int):
    """Setup default automation rules for new companies"""
    # Auto-confirm small payments
    automation_engine.create_automation_rule(
        company_id=company_id,
        rule_name="auto_confirm_small_payments",
        trigger={
            'event_type': 'payment.created',
            'conditions': [
                {'field': 'amount', 'operator': 'less_than', 'value': 10000}
            ]
        },
        actions=[
            {'type': 'update_status', 'status': 'Confirmed'}
        ]
    )
    
    # Send notification for large payments
    automation_engine.create_automation_rule(
        company_id=company_id,
        rule_name="notify_large_payments",
        trigger={
            'event_type': 'payment.created',
            'conditions': [
                {'field': 'amount', 'operator': 'greater_than', 'value': 100000}
            ]
        },
        actions=[
            {'type': 'send_email', 'template': 'large_payment_notification'}
        ]
    )
