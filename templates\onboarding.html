{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Onboarding - Exlipa
{%- else -%}
    Onboarding - Exlipa
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="mb-0">Welcome to Exlipa! Let's Set Up Your Company</h3>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="primary_color" class="form-label">Primary Color</label>
                            <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="#28a745">
                        </div>
                        <div class="mb-3">
                            <label for="secondary_color" class="form-label">Secondary Color</label>
                            <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="#007bff">
                        </div>
                        <div class="mb-3">
                            <label for="mpesa_till" class="form-label">M-Pesa Till</label>
                            <input type="text" class="form-control" id="mpesa_till" name="mpesa_till" placeholder="123456">
                        </div>
                        <div class="mb-3">
                            <label for="tigo_paybill" class="form-label">Tigo Paybill</label>
                            <input type="text" class="form-control" id="tigo_paybill" name="tigo_paybill" placeholder="654321">
                        </div>
                        <div class="mb-3">
                            <label for="airtel_merchant" class="form-label">Airtel Merchant</label>
                            <input type="text" class="form-control" id="airtel_merchant" name="airtel_merchant" placeholder="789012">
                        </div>
                        <div class="mb-3">
                            <label for="crdb_merchant" class="form-label">CRDB Merchant</label>
                            <input type="text" class="form-control" id="crdb_merchant" name="crdb_merchant" placeholder="890123">
                        </div>
                        <div class="mb-3">
                            <label for="team_emails" class="form-label">Invite Team Members (optional)</label>
                            <textarea class="form-control" id="team_emails" name="team_emails" rows="2" placeholder="Enter email addresses, separated by commas"></textarea>
                            <div class="form-text">Team members will receive an invite to join your company account.</div>
                        </div>
                        <button type="submit" class="btn btn-info w-100">Finish Onboarding</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
