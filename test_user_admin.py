#!/usr/bin/env python3
"""
Test user admin login specifically
"""

from app import app, db, User
from werkzeug.security import check_password_hash

def test_user_admin():
    """Test user admin login"""
    with app.app_context():
        print("=== TESTING USER ADMIN LOGIN ===")
        
        # Test credentials
        username = 'useradmin'
        password = 'useradmin123'
        
        print(f"Testing: {username} / {password}")
        
        # Check if user exists at all
        user_any = User.query.filter_by(username=username).first()
        print(f"User exists: {user_any is not None}")
        if user_any:
            print(f"  Username: {user_any.username}")
            print(f"  Role: {user_any.role}")
            print(f"  Active: {user_any.is_active}")
            print(f"  Email: {user_any.email}")
        
        # Check specific query used by login route
        user_specific = User.query.filter_by(username=username, is_active=True, role='user_admin').first()
        print(f"Specific query result: {user_specific is not None}")
        
        # Test password
        if user_any:
            password_valid = check_password_hash(user_any.password_hash, password)
            print(f"Password valid: {password_valid}")
        
        # Test account lock status
        if user_any:
            is_locked = user_any.is_account_locked()
            print(f"Account locked: {is_locked}")
        
        # Overall result
        if user_specific and not user_specific.is_account_locked() and check_password_hash(user_specific.password_hash, password):
            print("✅ USER ADMIN LOGIN SHOULD WORK")
        else:
            print("❌ USER ADMIN LOGIN WILL FAIL")
            if not user_specific:
                print("   Reason: User not found with correct role/active status")
            elif user_specific.is_account_locked():
                print("   Reason: Account locked")
            elif not check_password_hash(user_specific.password_hash, password):
                print("   Reason: Invalid password")

if __name__ == '__main__':
    test_user_admin()
