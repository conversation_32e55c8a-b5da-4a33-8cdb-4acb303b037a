{% extends "base.html" %}

{% block title %}{{ company.landing_page_title or (company.company_name + ' - Pay Online') }}{% endblock %}

{% block content %}
<style>
:root {
    --company-primary: {{ company.primary_color or '#007bff' }};
    --company-secondary: {{ company.secondary_color or '#6c757d' }};
    --gradient-primary: linear-gradient(135deg, var(--company-primary), {{ company.secondary_color or '#667eea' }});
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.landing-container {
    max-width: 420px;
    margin: 0 auto;
    padding: 1.5rem 1rem;
}

.flash-messages {
    margin-bottom: 2rem;
}

.alert {
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
}

.alert-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: white !important;
}

.btn-close {
    filter: brightness(0) invert(1);
}

.company-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
}

.company-header:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.company-logo {
    max-height: 60px;
    margin-bottom: 1rem;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.company-title {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.company-website {
    color: var(--company-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.company-website:hover {
    color: var(--company-secondary);
    transform: translateY(-1px);
}

.company-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.5;
    margin: 0.75rem 0;
}

.custom-message {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin: 1rem 0;
    border: none;
    box-shadow: 0 3px 15px rgba(59, 130, 246, 0.3);
    font-size: 0.95rem;
}



.warning-card {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 1.5rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
}

.confirmation-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.confirmation-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
}

.confirmation-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.25rem;
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.form-grid {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.4rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.form-control, .form-select {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0.75rem 0.875rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
    border-color: var(--company-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: #cbd5e1;
}

.payment-method-selection {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.payment-option-small {
    padding: 0.875rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.payment-option-small:hover {
    border-color: var(--company-primary);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.payment-option-small.selected {
    border-color: var(--company-primary);
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.payment-option-small input[type="radio"] {
    display: none;
}

.payment-content {
    display: flex;
    align-items: center;
    gap: 0.875rem;
}

.payment-icon-small {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.payment-details {
    flex: 1;
}

.payment-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.2rem;
    line-height: 1.2;
}

.payment-number {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 600;
    font-size: 0.85rem;
    opacity: 0.8;
    letter-spacing: 0.02em;
}

.payment-option-small.selected .payment-name,
.payment-option-small.selected .payment-number {
    color: white;
}

.payment-option-small.selected .payment-icon-small {
    background: rgba(255, 255, 255, 0.2);
}

.confirm-button {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.875rem 1.75rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    justify-content: center;
    cursor: pointer;
}

.confirm-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
    color: white;
}

.confirm-button:active {
    transform: translateY(0);
}

.confirm-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        padding: 1.25rem;
        gap: 1rem;
    }
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .landing-container {
        max-width: 100%;
        padding: 1rem;
    }

    .company-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.875rem;
    }

    .btn {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.875rem 1.5rem;
    }

    .payment-option-small {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .payment-icon-small {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .landing-container { padding: 1rem 0.75rem; }
    .company-header { padding: 1.25rem; margin-bottom: 1.25rem; }
    .company-title { font-size: 1.5rem; }
    .form-grid { padding: 1rem; gap: 0.875rem; }
    .payment-option-small { padding: 0.75rem; }
    .payment-content { gap: 0.75rem; }
    .payment-icon-small { width: 32px; height: 32px; font-size: 1rem; }
    .confirm-button { padding: 0.75rem 1.5rem; font-size: 0.95rem; min-width: 160px; }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .payment-option-small {
        min-height: 60px;
        touch-action: manipulation;
    }

    .btn {
        min-height: 48px;
        touch-action: manipulation;
    }

    .form-control {
        min-height: 48px;
    }
}

/* Form validation styles */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Payment guidance styles */
#payment-guidance {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    font-size: 0.9rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<div class="landing-container">
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- {%- if session.language == "sw" -%}Kampuni{%- else -%}Company{%- endif -%} Header -->
    <div class="company-header">
        {% if company.logo_filename %}
            <img src="{{ url_for('static', filename='company_logos/' ~ company.logo_filename) }}"
                 alt="{{ company.company_name }} Logo" class="company-logo">
        {% endif %}

        <h1 class="company-title">{{ company.company_name }}</h1>

        {% if company.company_website %}
            <div class="mb-3">
                <a href="{{ company.company_website }}" target="_blank" class="company-website">
                    <i class="fas fa-globe me-2"></i>{{ company.company_website }}
                </a>
            </div>
        {% endif %}

        {% if company.landing_page_description %}
            <p class="company-description">{{ company.landing_page_description }}</p>
        {% endif %}

        {% if company.custom_message %}
            <div class="custom-message">
                <i class="fas fa-info-circle me-2"></i>{{ company.custom_message }}
            </div>
        {% endif %}
    </div>


    <!-- {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Confirmation Section -->
    {% if company.mpesa_till or company.tigo_paybill or company.airtel_merchant or company.crdb_merchant %}
    <div class="confirmation-card">
        <div class="confirmation-header">
            <i class="fas fa-check-circle me-2"></i>
            {% if session.language == 'sw' %}
                Thibitisha Malipo Yako
            {% else %}
                Confirm Your Payment
            {% endif %}
        </div>

        <form method="POST" id="confirmationForm">
            <div class="form-grid">
                <div class="form-group">
                    <label for="customer_name" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        {% if session.language == 'sw' %}
                            Jina Lako Kamili
                        {% else %}
                            Your Full Name
                        {% endif %}
                    </label>
                    <input type="text" class="form-control" id="customer_name" name="customer_name"
                           placeholder="Enter your full name" required>
                </div>



                <div class="form-group">
                    <label for="amount" class="form-label">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        {% if session.language == 'sw' %}
                            Kiasi Kilicholipwa (TZS)
                        {% else %}
                            Amount Paid (TZS)
                        {% endif %}
                    </label>
                    <input type="number" class="form-control" id="amount" name="amount"
                           placeholder="0.00" required step="0.01" min="0.01">
                </div>

                <div class="form-group full-width">
                    <label class="form-label">
                        <i class="fas fa-mobile-alt me-2"></i>
                        {% if session.language == 'sw' %}
                            Njia ya Malipo Iliyotumika
                        {% else %}
                            Payment Method Used
                        {% endif %}
                    </label>
                    <div class="payment-method-selection">
                        {% if company.mpesa_till %}
                        <label class="payment-option-small" for="mpesa">
                            <input type="radio" id="mpesa" name="payment_method" value="M-Pesa" required>
                            <div class="payment-content">
                                <div class="payment-icon-small mpesa-icon">M</div>
                                <div class="payment-details">
                                    <div class="payment-name">{{ t('M-Pesa') }}</div>
                                    <div class="payment-number">{{ company.mpesa_till }}</div>
                                </div>
                            </div>
                        </label>
                        {% endif %}

                        {% if company.tigo_paybill %}
                        <label class="payment-option-small" for="tigo">
                            <input type="radio" id="tigo" name="payment_method" value="Tigo Pesa" required>
                            <div class="payment-content">
                                <div class="payment-icon-small tigo-icon">T</div>
                                <div class="payment-details">
                                    <div class="payment-name">{{ t('Tigo Pesa') }}</div>
                                    <div class="payment-number">{{ company.tigo_paybill }}</div>
                                </div>
                            </div>
                        </label>
                        {% endif %}

                        {% if company.airtel_merchant %}
                        <label class="payment-option-small" for="airtel">
                            <input type="radio" id="airtel" name="payment_method" value="Airtel Money" required>
                            <div class="payment-content">
                                <div class="payment-icon-small airtel-icon">A</div>
                                <div class="payment-details">
                                    <div class="payment-name">{{ t('Airtel Money') }}</div>
                                    <div class="payment-number">{{ company.airtel_merchant }}</div>
                                </div>
                            </div>
                        </label>
                        {% endif %}

                        {% if company.crdb_merchant %}
                        <label class="payment-option-small" for="crdb">
                            <input type="radio" id="crdb" name="payment_method" value="CRDB" required>
                            <div class="payment-content">
                                <div class="payment-icon-small crdb-icon">C</div>
                                <div class="payment-details">
                                    <div class="payment-name">CRDB Bank</div>
                                    <div class="payment-number">{{ company.crdb_merchant }}</div>
                                </div>
                            </div>
                        </label>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group">
                    <label for="transaction_id" class="form-label">
                        <i class="fas fa-receipt me-2"></i>Transaction ID / Reference Number
                    </label>
                    <input type="text" class="form-control" id="transaction_id" name="transaction_id"
                           placeholder="Enter transaction ID from SMS (e.g., QH12345678)" required
                           pattern="[A-Z0-9]{6,15}" title="Transaction ID should be 6-15 characters">
                    <div class="form-text">
                        <i class="fas fa-info-circle me-1"></i>
                        {% if session.language == 'sw' %}
                            Angalia SMS yako kwa msimbo wa uthibitisho wa muamala baada ya kufanya malipo
                        {% else %}
                            Check your SMS for the transaction confirmation code after making payment
                        {% endif %}
                    </div>
                </div>

                <div class="form-group">
                    <label for="sender_phone" class="form-label">
                        <i class="fas fa-phone me-2"></i>Phone Number Used for Payment
                    </label>
                    <input type="tel" class="form-control" id="sender_phone" name="sender_phone"
                           placeholder="e.g., +255712345678 or 0712345678"
                           pattern="^(\+255|0)[67][0-9]{8}$"
                           title="Enter valid Tanzanian mobile number">
                    <div class="form-text">
                        <i class="fas fa-info-circle me-1"></i>
                        Enter the phone number you used to send the payment
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes" class="form-label">
                        <i class="fas fa-comment me-2"></i>Notes (Optional)
                    </label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"
                              placeholder="Additional information" style="resize: vertical; min-height: 60px;"></textarea>
                </div>
            </div>

            <div class="text-center" style="margin-top: 1.5rem;">
                <button type="submit" class="confirm-button" id="submitBtn">
                    <i class="fas fa-paper-plane"></i>
                    <span>Submit Confirmation</span>
                </button>
            </div>
        </form>
    </div>
    {% else %}
    <div class="text-center">
        <div class="warning-card">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Payment methods are being configured.</strong><br>
            Please contact us directly for payment instructions.
        </div>
    </div>
    {% endif %}
</div>

<script>
// Payment method selection in confirmation form
document.addEventListener('DOMContentLoaded', function() {
    const radioButtons = document.querySelectorAll('input[name="payment_method"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove selected class from all options
            document.querySelectorAll('.payment-option-small').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to chosen option
            this.closest('.payment-option-small').classList.add('selected');
        });
    });
});

// Form validation and submission
document.getElementById('confirmationForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('span');

    // Disable button and show loading state
    submitBtn.disabled = true;
    btnText.textContent = 'Processing...';

    // Re-enable after 3 seconds if form doesn't submit (fallback)
    setTimeout(() => {
        if (submitBtn.disabled) {
            submitBtn.disabled = false;
            btnText.textContent = 'Submit Payment Confirmation';
        }
    }, 3000);
});

// Auto-format amount input
document.getElementById('amount').addEventListener('input', function(e) {
    let value = e.target.value;
    if (value && !isNaN(value)) {
        // Format to 2 decimal places when user stops typing
        clearTimeout(this.formatTimer);
        this.formatTimer = setTimeout(() => {
            e.target.value = parseFloat(value).toFixed(2);
        }, 1000);
    }
});

// Enhanced mobile money validation
document.getElementById('sender_phone')?.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\s/g, ''); // Remove spaces

    // Auto-format phone number
    if (value.startsWith('0')) {
        // Convert 0712345678 to +255712345678
        if (value.length === 10) {
            value = '+255' + value.substring(1);
            e.target.value = value;
        }
    } else if (value.startsWith('255')) {
        // Convert 255712345678 to +255712345678
        if (value.length === 12) {
            value = '+' + value;
            e.target.value = value;
        }
    }

    // Validate phone number format
    const phonePattern = /^(\+255|0)[67][0-9]{8}$/;
    const isValid = phonePattern.test(value);

    if (value.length > 0) {
        if (isValid) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
        } else {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
        }
    } else {
        e.target.classList.remove('is-valid', 'is-invalid');
    }
});

// Transaction ID validation
document.getElementById('transaction_id')?.addEventListener('input', function(e) {
    let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, ''); // Only alphanumeric
    e.target.value = value;

    // Validate transaction ID format
    const isValid = value.length >= 6 && value.length <= 15;

    if (value.length > 0) {
        if (isValid) {
            e.target.classList.remove('is-invalid');
            e.target.classList.add('is-valid');
        } else {
            e.target.classList.remove('is-valid');
            e.target.classList.add('is-invalid');
        }
    } else {
        e.target.classList.remove('is-valid', 'is-invalid');
    }
});

// Payment method selection guidance
document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const method = this.value;
        const guidanceDiv = document.getElementById('payment-guidance');

        if (!guidanceDiv) {
            // Create guidance div if it doesn't exist
            const guidance = document.createElement('div');
            guidance.id = 'payment-guidance';
            guidance.className = 'alert alert-info mt-3';
            guidance.style.display = 'none';
            this.closest('.payment-method-selection').appendChild(guidance);
        }

        const guidance = document.getElementById('payment-guidance');
        let guidanceText = '';

        switch(method) {
            case 'M-Pesa':
                guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>M-Pesa Payment:</strong> Dial *150*00# → Lipa Kwa M-Pesa → Lipa Kwa Simu → Enter Till Number → Enter Amount → Enter PIN';
                break;
            case 'Tigo Pesa':
                guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Tigo Pesa Payment:</strong> Dial *150*01# → Lipa Bili → Enter Paybill Number → Enter Amount → Enter PIN';
                break;
            case 'Airtel Money':
                guidanceText = '<i class="fas fa-info-circle me-2"></i><strong>Airtel Money Payment:</strong> Dial *150*60# → Lipa Bili → Enter Merchant Code → Enter Amount → Enter PIN';
                break;
            default:
                guidanceText = '<i class="fas fa-info-circle me-2"></i>Follow the payment instructions for your selected method.';
        }

        guidance.innerHTML = guidanceText;
        guidance.style.display = 'block';
    });
});

// Smooth scroll and animations on load
document.addEventListener('DOMContentLoaded', function() {
    // Animate elements on load
    const elements = document.querySelectorAll('.company-header, .confirmation-card');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>

{% endblock %}
