<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}
        {% if session.language == 'sw' %}
            <PERSON><PERSON><PERSON> wa Uthibitisho wa Malipo
        {% else %}
            Payment Confirmation System
        {% endif %}
    {% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/advanced-ui.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/visibility-fixes.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/admin-consistent.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-bolt me-2" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
                <span style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 900; font-size: 2rem; letter-spacing: -0.02em;">EXLIPA</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="mainNavbar">
                <div class="navbar-nav ms-auto">
                    <!-- Language Switcher -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            {% if session.language == 'sw' %}
                                🇹🇿 Kiswahili
                            {% else %}
                                🇬🇧 English
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item {% if session.language != 'sw' %}active{% endif %}"
                                   href="{{ url_for('set_language', language='en') }}">
                                    🇬🇧 English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {% if session.language == 'sw' %}active{% endif %}"
                                   href="{{ url_for('set_language', language='sw') }}">
                                    🇹🇿 Kiswahili
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Navigation Links -->
                    <a class="nav-link" href="{{ url_for('pricing') }}">
                        <i class="fas fa-tags me-1"></i>{{ t('Pricing') }}
                    </a>
                    <a class="nav-link" href="{{ url_for('contact') }}">
                        <i class="fas fa-phone me-1"></i>{{ t('Contact Us') }}
                    </a>

                    {% if current_user.is_authenticated %}
                        {% if current_user.role == 'super_admin' %}
                            <a class="nav-link" href="{{ url_for('master_admin_dashboard') }}">
                                <i class="fas fa-crown me-1"></i>{{ t('Master Admin') }}
                            </a>
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>{{ t('Logout') }}
                            </a>
                        {% elif current_user.role == 'user_admin' %}
                            <a class="nav-link" href="{{ url_for('user_admin_dashboard') }}">
                                <i class="fas fa-building me-1"></i>{{ t('Company Dashboard') }}
                            </a>
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>{{ t('Logout') }}
                            </a>
                        {% endif %}
                    {% else %}
                        <a class="nav-link" href="{{ url_for('user_admin_login') }}">
                            <i class="fas fa-user me-1"></i>{{ t('Login') }}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">
                {% if session.language == 'sw' %}
                    &copy; 2024 EXLIPA Payment Solutions Ltd. Haki zote zimehifadhiwa.
                {% else %}
                    &copy; 2024 EXLIPA Payment Solutions Ltd. All rights reserved.
                {% endif %}
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/smart-help.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
