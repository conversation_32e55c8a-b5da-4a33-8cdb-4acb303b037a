{% extends "base.html" %}

{% block title %}Company Profile - EXLIPA{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><i class="fas fa-building me-2 text-primary"></i>Company Profile</h2>
                    <p class="text-muted mb-0">Update your company information and payment details</p>
                </div>
                <div class="d-flex gap-2">
                    {% if current_user.role == 'user_admin' %}
                        <a href="/user-admin" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    {% else %}
                        <a href="{{ url_for('company_dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Profile Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="glass-card p-4">
                        <form method="POST" class="needs-validation" novalidate>
                            <h5 class="fw-bold mb-3"><i class="fas fa-info-circle me-2 text-info"></i>Company Information</h5>
                            
                            <!-- Basic Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="company_name" class="form-label">Company Name *</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="{{ company.company_name }}" required>
                                        <div class="invalid-feedback">Please provide a company name.</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="company_phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                               value="{{ company.company_phone or '' }}" placeholder="+255 XXX XXX XXX">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="company_address" class="form-label">Business Address</label>
                                <textarea class="form-control" id="company_address" name="company_address" 
                                          rows="3" placeholder="Enter your business address">{{ company.company_address or '' }}</textarea>
                            </div>

                            <div class="mb-4">
                                <label for="company_website" class="form-label">Company Website</label>
                                <input type="url" class="form-control" id="company_website" name="company_website" 
                                       value="{{ company.company_website or '' }}" placeholder="https://www.example.com">
                            </div>

                            <!-- Brand Colors -->
                            <h5 class="fw-bold mb-3"><i class="fas fa-palette me-2 text-warning"></i>Brand Colors</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="primary_color" class="form-label">Primary Brand Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="primary_color" name="primary_color" 
                                                   value="{{ company.primary_color or '#007bff' }}">
                                            <input type="text" class="form-control" 
                                                   value="{{ company.primary_color or '#007bff' }}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="secondary_color" class="form-label">Secondary Brand Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" 
                                                   id="secondary_color" name="secondary_color" 
                                                   value="{{ company.secondary_color or '#6c757d' }}">
                                            <input type="text" class="form-control" 
                                                   value="{{ company.secondary_color or '#6c757d' }}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Information -->
                            <h5 class="fw-bold mb-3"><i class="fas fa-credit-card me-2 text-success"></i>{{ t('Payment Methods') }}</h5>
                            <p class="text-muted small mb-3">Configure your payment method details for customer payments</p>
                            
                            <!-- Mobile Money Services -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="mpesa_till" class="form-label">M-Pesa Till Number</label>
                                        <input type="text" class="form-control" id="mpesa_till" name="mpesa_till" 
                                               value="{{ company.mpesa_till or '' }}" placeholder="e.g. 123456">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tigo_paybill" class="form-label">Tigo Pesa Paybill</label>
                                        <input type="text" class="form-control" id="tigo_paybill" name="tigo_paybill" 
                                               value="{{ company.tigo_paybill or '' }}" placeholder="e.g. 123456">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="airtel_merchant" class="form-label">Airtel Money Merchant</label>
                                        <input type="text" class="form-control" id="airtel_merchant" name="airtel_merchant" 
                                               value="{{ company.airtel_merchant or '' }}" placeholder="e.g. 123456">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="crdb_merchant" class="form-label">CRDB Bank Merchant</label>
                                        <input type="text" class="form-control" id="crdb_merchant" name="crdb_merchant" 
                                               value="{{ company.crdb_merchant or '' }}" placeholder="e.g. 123456">
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-flex justify-content-end gap-2 mt-4">
                                <a href="{{ url_for('company_dashboard') }}" class="btn btn-outline-secondary">{{ t('Cancel') }}</a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Info Panel -->
                <div class="col-lg-4">
                    <div class="glass-card p-4">
                        <h5 class="fw-bold mb-3"><i class="fas fa-info-circle me-2 text-info"></i>Profile Information</h5>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ t('Company Email') }}</label>
                            <p class="text-muted">{{ company.company_email }}</p>
                            <small class="text-muted">Contact admin to change email address</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Subscription Status</label>
                            <span class="badge bg-{{ 'success' if company.subscription_status == 'Active' else 'warning' }}">
                                {{ company.subscription_status }}
                            </span>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Billing Tier</label>
                            <p class="text-muted">{{ company.billing_tier|title }}</p>
                        </div>

                        {% if company.dynamic_pos_enabled %}
                        <div class="mb-3">
                            <label class="form-label fw-bold">POS System</label>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Enabled
                            </span>
                        </div>
                        {% endif %}

                        <hr>
                        <h6 class="fw-bold mb-2">Need Help?</h6>
                        <p class="small text-muted">
                            Contact our support team at <strong><EMAIL></strong> if you need assistance updating your profile.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Color picker sync
document.getElementById('primary_color').addEventListener('change', function() {
    this.nextElementSibling.value = this.value;
});

document.getElementById('secondary_color').addEventListener('change', function() {
    this.nextElementSibling.value = this.value;
});
</script>
{% endblock %}
