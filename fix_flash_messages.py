#!/usr/bin/env python3
"""
Fix all flash messages in app.py to use translation function
"""

import re

def fix_flash_messages():
    """Replace all flash messages with translation function calls"""
    
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Flash message patterns to replace
    flash_replacements = [
        # Basic flash patterns
        (r"flash\('([^']+)'\)", r"flash(get_translation('\1'))"),
        (r'flash\("([^"]+)"\)', r'flash(get_translation("\1"))'),
        
        # Flash with category patterns
        (r"flash\('([^']+)',\s*'([^']+)'\)", r"flash(get_translation('\1'), '\2')"),
        (r'flash\("([^"]+)",\s*"([^"]+)"\)', r'flash(get_translation("\1"), "\2")'),
        (r"flash\('([^']+)',\s*\"([^\"]+)\"\)", r"flash(get_translation('\1'), \"\2\")"),
        (r'flash\("([^"]+)",\s*\'([^\']+)\'\)', r'flash(get_translation("\1"), \'\2\')'),
    ]
    
    original_content = content
    
    # Apply replacements
    for pattern, replacement in flash_replacements:
        # Skip if already using get_translation
        if 'get_translation(' not in content:
            content = re.sub(pattern, replacement, content)
        else:
            # More careful replacement to avoid double-wrapping
            matches = re.finditer(pattern, content)
            for match in reversed(list(matches)):
                full_match = match.group(0)
                if 'get_translation(' not in full_match:
                    start, end = match.span()
                    new_text = re.sub(pattern, replacement, full_match)
                    content = content[:start] + new_text + content[end:]
    
    # Special cases for f-strings and complex messages
    special_cases = [
        # f-string patterns
        (r"flash\(f'([^']*\{[^}]+\}[^']*)'\)", r"flash(f'{get_translation(\"\1\")}')"),
        (r'flash\(f"([^"]*\{[^}]+\}[^"]*)"\)', r'flash(f"{get_translation(\'\1\')}\")')
    ]
    
    for pattern, replacement in special_cases:
        matches = re.finditer(pattern, content)
        for match in reversed(list(matches)):
            full_match = match.group(0)
            if 'get_translation(' not in full_match:
                start, end = match.span()
                # For f-strings, we need more careful handling
                message_part = match.group(1)
                # Extract the base message without variables
                base_message = re.sub(r'\{[^}]+\}', '{}', message_part)
                new_flash = f"flash(get_translation('{base_message}').format(" + re.findall(r'\{([^}]+)\}', message_part)[0] + "))"
                content = content[:start] + new_flash + content[end:]
    
    # Write back if changed
    if content != original_content:
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Flash messages updated with translation functions")
        return True
    else:
        print("ℹ️  No flash messages needed updating")
        return False

if __name__ == '__main__':
    print("🔧 Fixing Flash Messages for Translation")
    print("=" * 50)
    
    success = fix_flash_messages()
    
    if success:
        print("🎉 All flash messages now support translation!")
        print("🌍 Messages will appear in Swahili when language is switched")
    else:
        print("📝 Flash messages already use translation functions")
    
    print("\n🚀 Ready for testing!")
