#!/usr/bin/env python3
"""
Authentication Service for EXLIPA
Handles role-based authentication and session management
"""

from flask import session, request, redirect, url_for, flash
from flask_login import current_user, login_user, logout_user
from functools import wraps
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AuthenticationManager:
    """Manages authentication for different user roles"""
    
    # Define role hierarchy and permissions
    ROLES = {
        'super_admin': {
            'name': 'Super Admin',
            'description': 'EXLIPA platform administrator',
            'permissions': ['all'],
            'dashboard': 'master_admin_dashboard',
            'login_route': 'master_admin_login'
        },
        'user_admin': {
            'name': 'User Admin', 
            'description': 'Company customer with paid tier',
            'permissions': ['company_management', 'payments', 'invoices', 'analytics'],
            'dashboard': 'user_admin_dashboard',
            'login_route': 'user_admin_login'
        },
        'company_user': {
            'name': 'Company User',
            'description': 'Company employee with limited access',
            'permissions': ['pos', 'sales', 'basic_reports'],
            'dashboard': 'company_dashboard', 
            'login_route': 'company_login'
        }
    }
    
    @classmethod
    def get_role_info(cls, role):
        """Get role information"""
        return cls.ROLES.get(role, {})
    
    @classmethod
    def has_permission(cls, user_role, required_permission):
        """Check if user role has required permission"""
        role_info = cls.get_role_info(user_role)
        permissions = role_info.get('permissions', [])
        
        # Super admin has all permissions
        if 'all' in permissions:
            return True
            
        return required_permission in permissions
    
    @classmethod
    def login_user_with_role(cls, user, remember=False):
        """Login user and set role-specific session data"""
        try:
            # Standard Flask-Login
            login_user(user, remember=remember)
            
            # Set role-specific session data
            session['user_role'] = user.role
            session['user_id'] = user.id
            session['login_time'] = datetime.utcnow().isoformat()
            
            # Update user login timestamp
            user.last_login = datetime.utcnow()
            
            logger.info(f"User {user.username} logged in with role {user.role}")
            return True
            
        except Exception as e:
            logger.error(f"Login failed for user {user.username}: {str(e)}")
            return False
    
    @classmethod
    def logout_user_safe(cls):
        """Safely logout user and clear session data"""
        try:
            if current_user.is_authenticated:
                logger.info(f"User {current_user.username} logged out")
            
            # Clear role-specific session data
            session.pop('user_role', None)
            session.pop('user_id', None) 
            session.pop('login_time', None)
            
            # Standard Flask-Login logout
            logout_user()
            
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
    
    @classmethod
    def get_dashboard_for_role(cls, role):
        """Get appropriate dashboard route for role"""
        role_info = cls.get_role_info(role)
        return role_info.get('dashboard', 'user_admin_login')
    
    @classmethod
    def get_login_route_for_role(cls, role):
        """Get appropriate login route for role"""
        role_info = cls.get_role_info(role)
        return role_info.get('login_route', 'user_admin_login')
    
    @classmethod
    def smart_redirect_to_login(cls):
        """Smart redirect based on the current route context"""
        endpoint = request.endpoint
        
        # Super admin only routes
        if endpoint in ['master_admin_dashboard', 'admin_users', 'create_user', 'edit_user', 'admin_pricing_tiers']:
            return redirect(url_for('master_admin_login'))
        
        # Company/POS routes
        elif endpoint and (endpoint.startswith('pos_') or endpoint in ['company_dashboard']):
            return redirect(url_for('company_login'))
        
        # Default to user admin login
        else:
            return redirect(url_for('user_admin_login'))
    
    @classmethod
    def smart_redirect_by_role(cls, user=None):
        """Redirect user to appropriate dashboard based on their role"""
        if not user:
            user = current_user
        
        if not user.is_authenticated:
            return cls.smart_redirect_to_login()
        
        dashboard_route = cls.get_dashboard_for_role(user.role)
        return redirect(url_for(dashboard_route))

# Role-based decorators
def require_role(required_role):
    """Decorator to require specific role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return AuthenticationManager.smart_redirect_to_login()
            
            if current_user.role != required_role:
                flash(f'Access denied. {required_role} role required.', 'danger')
                return AuthenticationManager.smart_redirect_by_role()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_permission(required_permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return AuthenticationManager.smart_redirect_to_login()
            
            if not AuthenticationManager.has_permission(current_user.role, required_permission):
                flash(f'Access denied. {required_permission} permission required.', 'danger')
                return AuthenticationManager.smart_redirect_by_role()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_any_role(*allowed_roles):
    """Decorator to require any of the specified roles"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return AuthenticationManager.smart_redirect_to_login()
            
            if current_user.role not in allowed_roles:
                flash(f'Access denied. One of these roles required: {", ".join(allowed_roles)}', 'danger')
                return AuthenticationManager.smart_redirect_by_role()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Specific role decorators for convenience
require_super_admin = require_role('super_admin')
require_user_admin = require_role('user_admin') 
require_company_user = require_role('company_user')

# Permission-based decorators
require_company_management = require_permission('company_management')
require_payments = require_permission('payments')
require_pos = require_permission('pos')

def admin_required(f):
    """Decorator for routes that require admin access (super_admin or user_admin)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return AuthenticationManager.smart_redirect_to_login()
        
        if current_user.role not in ['super_admin', 'user_admin']:
            flash('Admin access required.', 'danger')
            return AuthenticationManager.smart_redirect_by_role()
        
        return f(*args, **kwargs)
    return decorated_function

def session_timeout_check(timeout_minutes=60):
    """Check if session has timed out"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if current_user.is_authenticated:
                login_time_str = session.get('login_time')
                if login_time_str:
                    login_time = datetime.fromisoformat(login_time_str)
                    if datetime.utcnow() - login_time > timedelta(minutes=timeout_minutes):
                        AuthenticationManager.logout_user_safe()
                        flash('Session expired. Please login again.', 'warning')
                        return AuthenticationManager.smart_redirect_to_login()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
