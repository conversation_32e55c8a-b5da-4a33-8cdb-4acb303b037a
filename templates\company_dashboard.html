{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}
    Dashibodi ya Kampuni - EXLIPA
{% else %}
    Company Dashboard - EXLIPA
{% endif %}
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="fw-bold mb-1 elegant-gradient-text">
                {% if session.language == 'sw' %}
                    <PERSON><PERSON><PERSON>, {{ current_user.full_name or current_user.username }}
                {% else %}
                    Welcome, {{ current_user.full_name or current_user.username }}
                {% endif %}
            </h2>
            <div class="text-muted mb-2">
                {% if session.language == 'sw' %}
                    Kampuni: <strong>{{ company.company_name }}</strong>
                {% else %}
                    Company: <strong>{{ company.company_name }}</strong>
                {% endif %}
            </div>
            <span class="badge 
                {% if company.subscription_status == 'Active' %}bg-success
                {% elif company.subscription_status == 'Suspended' %}bg-warning
                {% elif company.subscription_status == 'Cancelled' %}bg-danger
                {% else %}bg-secondary{% endif %}">
                {{ company.subscription_status }}
            </span>
        </div>
    </div>
    <div class="row g-3 mb-4">
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-file-invoice fa-2x text-primary mb-2"></i>
                <h4 class="mb-0">{{ company.invoices|length if company.invoices else 0 }}</h4>
                <div class="small text-muted">{{ t('Invoices') }}</div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-credit-card fa-2x text-success mb-2"></i>
                <h4 class="mb-0">{{ company.payments|length if company.payments else 0 }}</h4>
                <div class="small text-muted">{{ t('Payments') }}</div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-users fa-2x text-info mb-2"></i>
                <h4 class="mb-0">{{ team_members|length if team_members else 1 }}</h4>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}
                        Timu
                    {% else %}
                        Team
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-cash-register fa-2x text-warning mb-2"></i>
                <h4 class="mb-0">POS</h4>
                <div class="small text-muted">
                  {% if company.dynamic_pos_enabled %}
                    <span class="text-success">{{ t('Active') }}</span>
                  {% elif company.dynamic_pos_payment_pending %}
                    <span class="text-warning">
                        {% if session.language == 'sw' %}
                            Kuamilishwa Kunasubiri
                        {% else %}
                            Activation Pending
                        {% endif %}
                    </span>
                  {% else %}
                    <span class="text-danger">Locked</span>
                  {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-8">
            <div class="glass-card p-4 h-100">
                <h5 class="fw-bold mb-3"><i class="fas fa-bullhorn me-2 text-primary"></i>
                    {% if session.language == 'sw' %}
                        Vitendo vya Haraka
                    {% else %}
                        Quick Actions
                    {% endif %}
                </h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ url_for('company_invoices') }}" class="btn btn-outline-primary flex-fill">
                        <i class="fas fa-file-invoice me-1"></i>
                        {% if session.language == 'sw' %}
                            Ona Ankara
                        {% else %}
                            View Invoices
                        {% endif %}
                    </a>
                    <a href="{{ url_for('company_payments') }}" class="btn btn-outline-success flex-fill">
                        <i class="fas fa-credit-card me-1"></i>
                        {% if session.language == 'sw' %}
                            Ona Malipo
                        {% else %}
                            View Payments
                        {% endif %}
                    </a>
                    <a href="{{ url_for('team_management') }}" class="btn btn-outline-info flex-fill">
                        <i class="fas fa-users me-1"></i>
                        {% if session.language == 'sw' %}
                            Simamia Timu
                        {% else %}
                            Manage Team
                        {% endif %}
                    </a>
                    <a href="/company-profile" class="btn btn-outline-secondary flex-fill">
                        <i class="fas fa-edit me-1"></i>
                        {% if session.language == 'sw' %}
                            Hariri Wasifu
                        {% else %}
                            Edit Profile
                        {% endif %}
                    </a>
                    <a href="{{ url_for('advanced_landing', company_id=company.id) }}" class="btn btn-outline-primary flex-fill"><i class="fas fa-globe me-1"></i>{{ t('Landing Page') }}</a>
                    {% if company.dynamic_pos_enabled %}
                        <a href="/pos" class="btn btn-warning flex-fill">
                            <i class="fas fa-cash-register me-1"></i>
                            {% if session.language == 'sw' %}
                                Anzisha POS
                            {% else %}
                                Launch POS
                            {% endif %}
                        </a>
                    {% else %}
                        <button class="btn btn-outline-warning flex-fill" disabled>
                            <i class="fas fa-cash-register me-1"></i>
                            {% if session.language == 'sw' %}
                                POS Imefungwa
                            {% else %}
                                POS Locked
                            {% endif %}
                        </button>
                    {% endif %}
                </div>
                {% if not company.dynamic_pos_enabled %}
                  <div class="alert alert-warning mt-3 mb-0">
                    {% if company.dynamic_pos_payment_pending %}
                      <i class="fas fa-hourglass-half me-1"></i>
                      {% if session.language == 'sw' %}
                          Kuamilishwa kwa POS yako kunasubiri uthibitisho wa malipo. Tafadhali subiri idhini ya msimamizi.
                      {% else %}
                          Your POS activation is pending payment confirmation. Please wait for admin approval.
                      {% endif %}
                    {% else %}
                      <i class="fas fa-lock me-1"></i>
                      {% if session.language == 'sw' %}
                          Kipengele cha POS kimefungwa. <strong>Ili kufungua, tafadhali omba na ulipe kwa POS hapa chini.</strong>
                      {% else %}
                          POS feature is locked. <strong>To unlock, please request and pay for POS below.</strong>
                      {% endif %}
                      <form action="{{ url_for('request_pos') }}" method="get" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-primary ms-2">
                            {% if session.language == 'sw' %}
                                Omba Kufungua POS
                            {% else %}
                                Request POS Unlock
                            {% endif %}
                        </button>
                      </form>
                    {% endif %}
                  </div>
                {% endif %}
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="glass-card p-4 h-100">
                <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2 text-info"></i>
                    {% if session.language == 'sw' %}
                        Taarifa za Kampuni
                    {% else %}
                        Company Info
                    {% endif %}
                </h6>
                <div class="mb-1">
                    <strong>
                        {% if session.language == 'sw' %}Barua pepe:{% else %}Email:{% endif %}
                    </strong> {{ company.company_email }}
                </div>
                <div class="mb-1">
                    <strong>
                        {% if session.language == 'sw' %}Simu:{% else %}Phone:{% endif %}
                    </strong> {{ company.company_phone or 'N/A' }}
                </div>
                <div class="mb-1">
                    <strong>
                        {% if session.language == 'sw' %}Tovuti:{% else %}Website:{% endif %}
                    </strong> {{ company.company_website or 'N/A' }}
                </div>
                <div class="mb-1">
                    <strong>
                        {% if session.language == 'sw' %}Kiwango cha Bei:{% else %}Pricing Tier:{% endif %}
                    </strong> {{ company.pricing_tier.name if company.pricing_tier else 'N/A' }}
                </div>
            </div>
        </div>
    </div>
    <div class="row g-3">
        <div class="col-12">
            <div class="glass-card p-4">
                <h6 class="fw-bold mb-2"><i class="fas fa-star me-2 text-warning"></i>{{ t('Features') }}</h6>
                <ul class="list-inline mb-0">
                    <li class="list-inline-item">
                        <span class="badge bg-primary">
                            <i class="fas fa-file-invoice me-1"></i>
                            {% if session.language == 'sw' %}
                                Ankara na Malipo
                            {% else %}
                                Billing & Invoicing
                            {% endif %}
                        </span>
                    </li>
                    <li class="list-inline-item">
                        <span class="badge bg-success">
                            <i class="fas fa-credit-card me-1"></i>
                            {% if session.language == 'sw' %}
                                Ufuatiliaji wa Malipo
                            {% else %}
                                Payment Tracking
                            {% endif %}
                        </span>
                    </li>
                    <li class="list-inline-item">
                        <span class="badge bg-info">
                            <i class="fas fa-users me-1"></i>
                            {% if session.language == 'sw' %}
                                Usimamizi wa Timu
                            {% else %}
                                Team Management
                            {% endif %}
                        </span>
                    </li>
                    <li class="list-inline-item">
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-cash-register me-1"></i>POS
                            {% if company.dynamic_pos_enabled %}
                                {% if session.language == 'sw' %}Hai{% else %}Active{% endif %}
                            {% elif company.dynamic_pos_payment_pending %}
                                {% if session.language == 'sw' %}Inasubiri{% else %}Pending{% endif %}
                            {% else %}
                                {% if session.language == 'sw' %}Imefungwa{% else %}Locked{% endif %}
                            {% endif %}
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<style>
    .glass-card { background: rgba(255,255,255,0.85); box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10); border-radius: 1.2rem; border: 1px solid rgba(255,255,255,0.18); transition: box-shadow 0.3s; }
    .glass-card:hover { box-shadow: 0 12px 36px 0 rgba(31,38,135,0.18); }
    .elegant-gradient-text { background: linear-gradient(90deg, #764ba2 0%, #667eea 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
    @media (max-width: 767px) {
        .glass-card { padding: 1.2rem !important; }
        h2 { font-size: 1.5rem; }
        h4 { font-size: 1.1rem; }
        .btn, .badge { font-size: 0.95rem; }
    }
</style>
{% endblock %}
