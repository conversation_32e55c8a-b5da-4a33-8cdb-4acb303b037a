<!DOCTYPE html>
<html>
<head>
    <title>Simple Landing Page Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2>Simple Landing Page Editor</h2>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="card">
            <div class="card-body">
                <form method="POST" id="landingForm">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_public_page_enabled" 
                                   name="is_public_page_enabled" {{ 'checked' if company.is_public_page_enabled else '' }}>
                            <label class="form-check-label" for="is_public_page_enabled">
                                Enable Public Landing Page
                            </label>
                        </div>
                        <small class="text-muted">
                            When enabled, customers can access your payment page at: 
                            <strong>http://localhost:5000/company/{{ company.id }}</strong>
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="landing_page_title" class="form-label">Page Title</label>
                        <input type="text" class="form-control" id="landing_page_title" 
                               name="landing_page_title" value="{{ company.landing_page_title or '' }}"
                               placeholder="Your {%- if session.language == "sw" -%}Kampuni{%- else -%}Company{%- endif -%} - Pay Online">
                        <small class="text-muted">This appears in the browser tab and search results</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="landing_page_description" class="form-label">Page Description</label>
                        <textarea class="form-control" id="landing_page_description" 
                                  name="landing_page_description" rows="3" 
                                  placeholder="Brief description of your business">{{ company.landing_page_description or '' }}</textarea>
                        <small class="text-muted">Optional description shown below your company name</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="custom_message" class="form-label">Custom Message</label>
                        <textarea class="form-control" id="custom_message" 
                                  name="custom_message" rows="3" 
                                  placeholder="Special instructions, promotions, or important information">{{ company.custom_message or '' }}</textarea>
                        <small class="text-muted">This message will be highlighted in a blue box on your landing page</small>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success" id="saveBtn">
                            <i class="fas fa-save me-1"></i>Save Landing Page
                        </button>
                        <a href="{{ url_for('user_admin_dashboard') }}" class="btn btn-outline-secondary">{{ t('{%- if session.language == "sw" -%}Ghairi{%- else -%}Cancel{%- endif -%}') }}</a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mt-4">
            <h5>Company Info</h5>
            <p><strong>Name:</strong> {{ company.company_name }}</p>
            <p><strong>Email:</strong> {{ company.company_email }}</p>
            <p><strong>Current Status:</strong> 
                <span class="badge bg-{{ 'success' if company.is_public_page_enabled else 'secondary' }}">
                    {{ 'Enabled' if company.is_public_page_enabled else 'Disabled' }}
                </span>
            </p>
        </div>
    </div>
    
    <script>
        console.log('Simple landing page loaded');
        
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('landingForm');
            const saveBtn = document.getElementById('saveBtn');
            
            console.log('Form found:', !!form);
            console.log('Save button found:', !!saveBtn);
            
            if (form) {
                form.addEventListener('submit', function(e) {
                    console.log('Form submitted!');
                    console.log('Form data:', new FormData(form));
                    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
                    saveBtn.disabled = true;
                });
            }
            
            if (saveBtn) {
                saveBtn.addEventListener('click', function(e) {
                    console.log('Save button clicked!');
                });
            }
        });
    </script>
</body>
</html>
