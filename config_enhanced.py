#!/usr/bin/env python3
"""
Enhanced Configuration Management for EXLIPA
Provides environment-specific configurations with proper validation
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class BaseConfig:
    """Base configuration class"""
    
    # Core Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Security settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 7200  # 2 hours
    
    # Rate limiting
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL') or 'memory://'
    RATELIMIT_DEFAULT = "100 per hour"
    
    # Mail configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    
    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
    
    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'exlipa.log'
    
    # Feature flags
    ENHANCED_FEATURES = os.environ.get('ENHANCED_FEATURES', 'True').lower() == 'true'
    ENABLE_API = os.environ.get('ENABLE_API', 'True').lower() == 'true'
    ENABLE_POS = os.environ.get('ENABLE_POS', 'True').lower() == 'true'
    ENABLE_ANALYTICS = os.environ.get('ENABLE_ANALYTICS', 'True').lower() == 'true'
    
    # Business settings
    DEFAULT_CURRENCY = os.environ.get('DEFAULT_CURRENCY') or 'TZS'
    MIN_PAYMENT_AMOUNT = float(os.environ.get('MIN_PAYMENT_AMOUNT') or 1000)
    MAX_PAYMENT_AMOUNT = float(os.environ.get('MAX_PAYMENT_AMOUNT') or 10000000)
    
    # Mobile money operators
    MOBILE_OPERATORS = [
        'M-Pesa', 'Tigo Pesa', 'Airtel Money', 'CRDB Lipa', 'Halo Pesa'
    ]
    
    # Supported languages
    LANGUAGES = {
        'en': 'English',
        'sw': 'Kiswahili'
    }
    DEFAULT_LANGUAGE = 'en'

class DevelopmentConfig(BaseConfig):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Database
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///payment_system_dev.db'
    SQLALCHEMY_ECHO = True  # Log SQL queries
    
    # Security (relaxed for development)
    WTF_CSRF_ENABLED = False  # Disable CSRF for easier testing
    SESSION_COOKIE_SECURE = False
    
    # Enhanced logging for development
    LOG_LEVEL = 'DEBUG'
    
    # Development-specific features
    ENHANCED_FEATURES = True

class TestingConfig(BaseConfig):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    
    # In-memory database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False
    
    # Disable rate limiting for testing
    RATELIMIT_ENABLED = False
    
    # Test-specific settings
    SECRET_KEY = 'test-secret-key'

class ProductionConfig(BaseConfig):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Database - must be set via environment variable
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/exlipa_production'
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    # Strict rate limiting
    RATELIMIT_DEFAULT = "50 per hour"
    
    @classmethod
    def init_app(cls, app):
        """Initialize production-specific settings"""
        # Ensure critical environment variables are set
        required_vars = [
            'SECRET_KEY',
            'DATABASE_URL',
            'MAIL_USERNAME',
            'MAIL_PASSWORD'
        ]
        
        missing_vars = [var for var in required_vars if not os.environ.get(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

class StagingConfig(ProductionConfig):
    """Staging configuration - like production but with debug info"""
    DEBUG = True
    LOG_LEVEL = 'INFO'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'staging': StagingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """Get configuration class by name"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])

class FeatureFlags:
    """Feature flag management"""
    
    @staticmethod
    def is_enabled(feature_name, default=False):
        """Check if a feature is enabled"""
        env_var = f"FEATURE_{feature_name.upper()}"
        return os.environ.get(env_var, str(default)).lower() == 'true'
    
    @staticmethod
    def get_all_flags():
        """Get all feature flags"""
        flags = {}
        for key, value in os.environ.items():
            if key.startswith('FEATURE_'):
                feature_name = key[8:].lower()  # Remove 'FEATURE_' prefix
                flags[feature_name] = value.lower() == 'true'
        return flags

# Business configuration constants
class BusinessConfig:
    """Business-specific configuration"""
    
    # Payment limits
    PAYMENT_LIMITS = {
        'min_amount': 1000,      # TZS 1,000
        'max_amount': 10000000,  # TZS 10,000,000
        'daily_limit': 50000000, # TZS 50,000,000 per day
    }
    
    # Fee structure
    FEE_STRUCTURE = {
        'default_percentage': 2.5,
        'minimum_fee': 100,
        'maximum_fee': 5000,
        'mobile_money_fees': {
            'M-Pesa': {'percentage': 0.0, 'fixed': 0.0},
            'Tigo Pesa': {'percentage': 0.0, 'fixed': 0.0},
            'Airtel Money': {'percentage': 0.0, 'fixed': 0.0},
            'CRDB Lipa': {'percentage': 0.5, 'fixed': 50.0},
            'Halo Pesa': {'percentage': 0.3, 'fixed': 30.0}
        }
    }
    
    # Subscription discounts
    SUBSCRIPTION_DISCOUNTS = {
        1: 0.0,   # Monthly - no discount
        3: 5.0,   # Quarterly - 5% discount
        6: 10.0,  # Semi-annual - 10% discount
        12: 15.0  # Annual - 15% discount
    }
    
    # POS settings
    POS_SETTINGS = {
        'low_stock_threshold': 10,
        'enable_inventory_tracking': True,
        'enable_barcode_scanning': True,
        'tax_rate': 18.0,  # 18% VAT in Tanzania
        'receipt_footer': 'Thank you for your business!'
    }
    
    # API settings
    API_SETTINGS = {
        'rate_limit': '1000 per hour',
        'max_request_size': '1MB',
        'timeout': 30,  # seconds
        'retry_attempts': 3
    }

# Security configuration
class SecurityConfig:
    """Security-specific configuration"""
    
    # Password requirements
    PASSWORD_REQUIREMENTS = {
        'min_length': 8,
        'require_uppercase': True,
        'require_lowercase': True,
        'require_digit': True,
        'require_special_char': False
    }
    
    # Session settings
    SESSION_SETTINGS = {
        'timeout_minutes': 120,  # 2 hours
        'max_concurrent_sessions': 3,
        'require_fresh_login_for_sensitive_ops': True
    }
    
    # Rate limiting
    RATE_LIMITS = {
        'login_attempts': '5 per minute',
        'api_calls': '1000 per hour',
        'password_reset': '3 per hour',
        'registration': '10 per hour'
    }
    
    # Two-factor authentication
    TWO_FACTOR_AUTH = {
        'enabled': False,  # Can be enabled via environment variable
        'backup_codes_count': 10,
        'totp_issuer': 'EXLIPA',
        'sms_provider': 'twilio'  # or 'africastalking'
    }
