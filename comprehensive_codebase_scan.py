#!/usr/bin/env python3
"""
Comprehensive EXLIPA Codebase Scanner
Checks for errors, inconsistencies, and potential issues
"""

import os
import re
import ast
import sys
from pathlib import Path

class CodebaseScanner:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        self.files_scanned = 0
        
    def log_error(self, file_path, line_num, message):
        self.errors.append(f"❌ ERROR: {file_path}:{line_num} - {message}")
        
    def log_warning(self, file_path, line_num, message):
        self.warnings.append(f"⚠️  WARNING: {file_path}:{line_num} - {message}")
        
    def log_info(self, file_path, message):
        self.info.append(f"ℹ️  INFO: {file_path} - {message}")

    def scan_python_file(self, file_path):
        """Scan Python files for syntax errors and issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check syntax
            try:
                ast.parse(content)
            except SyntaxError as e:
                self.log_error(file_path, e.lineno, f"Syntax Error: {e.msg}")
                return
                
            lines = content.split('\n')
            
            # Check for common issues
            for i, line in enumerate(lines, 1):
                # Check for TODO/FIXME comments
                if 'TODO' in line or 'FIXME' in line:
                    self.log_warning(file_path, i, f"TODO/FIXME found: {line.strip()}")
                
                # Check for print statements (should use logging)
                if re.search(r'\bprint\s*\(', line) and 'logger' not in line:
                    self.log_warning(file_path, i, "Print statement found - consider using logging")
                
                # Check for hardcoded credentials
                if re.search(r'password\s*=\s*["\'][^"\']+["\']', line, re.IGNORECASE):
                    self.log_error(file_path, i, "Hardcoded password detected")
                
                # Check for SQL injection risks
                if re.search(r'execute\s*\(\s*["\'].*%.*["\']', line):
                    self.log_warning(file_path, i, "Potential SQL injection risk - use parameterized queries")
                    
        except Exception as e:
            self.log_error(file_path, 0, f"Failed to scan file: {str(e)}")

    def scan_html_file(self, file_path):
        """Scan HTML/template files for issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            
            # Track template blocks
            extends_found = False
            block_stack = []
            
            for i, line in enumerate(lines, 1):
                # Check for template inheritance
                if '{% extends' in line:
                    extends_found = True
                    
                # Check for block tags
                if '{% block' in line:
                    block_name = re.search(r'{% block (\w+)', line)
                    if block_name:
                        block_stack.append(block_name.group(1))
                        
                if '{% endblock' in line:
                    if block_stack:
                        block_stack.pop()
                    else:
                        self.log_warning(file_path, i, "Unmatched endblock tag")
                
                # Check for missing translations
                if re.search(r'>[^<{%]*[A-Za-z]{3,}[^<{%]*<', line):
                    if not re.search(r'{%.*t\(|{{.*t\(', line):
                        # Skip common HTML elements and short words
                        text_content = re.findall(r'>([^<{%]*[A-Za-z]{4,}[^<{%]*)<', line)
                        for text in text_content:
                            if text.strip() and not any(skip in text.lower() for skip in ['html', 'head', 'body', 'div', 'span']):
                                self.log_info(file_path, f"Line {i}: Possible untranslated text: '{text.strip()}'")
                
                # Check for inline styles (should use CSS classes)
                if 'style=' in line and len(re.findall(r'style="[^"]*"', line)) > 2:
                    self.log_warning(file_path, i, "Multiple inline styles - consider using CSS classes")
                
                # Check for missing alt attributes on images
                if '<img' in line and 'alt=' not in line:
                    self.log_warning(file_path, i, "Image missing alt attribute")
                    
            # Check for unmatched blocks
            if block_stack:
                self.log_warning(file_path, 0, f"Unmatched block tags: {block_stack}")
                
        except Exception as e:
            self.log_error(file_path, 0, f"Failed to scan HTML file: {str(e)}")

    def scan_css_js_file(self, file_path):
        """Scan CSS/JS files for issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                # Check for console.log in JS
                if file_path.endswith('.js') and 'console.log' in line:
                    self.log_warning(file_path, i, "console.log found - remove for production")
                
                # Check for !important in CSS
                if file_path.endswith('.css') and '!important' in line:
                    self.log_info(file_path, f"Line {i}: !important used - consider refactoring CSS specificity")
                    
        except Exception as e:
            self.log_error(file_path, 0, f"Failed to scan CSS/JS file: {str(e)}")

    def check_file_structure(self):
        """Check overall file structure and organization"""
        required_files = [
            'app.py',
            'requirements.txt',
            'templates/base.html',
            'templates/index.html',
            'static/css/',
            'static/js/'
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                self.log_error("Project Structure", 0, f"Missing required file/directory: {file_path}")
            else:
                self.log_info("Project Structure", f"Found required file: {file_path}")

    def check_security_issues(self):
        """Check for common security issues"""
        # Check app.py for security configurations
        if os.path.exists('app.py'):
            with open('app.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for debug mode in production
            if 'debug=True' in content:
                self.log_warning('app.py', 0, "Debug mode enabled - disable for production")
                
            # Check for secret key configuration
            if 'SECRET_KEY' not in content:
                self.log_error('app.py', 0, "SECRET_KEY not configured")
                
            # Check for CSRF protection
            if 'csrf' not in content.lower():
                self.log_warning('app.py', 0, "CSRF protection not detected")

    def check_translation_consistency(self):
        """Check translation consistency across templates"""
        translation_patterns = {}
        
        # Scan all HTML files for translation calls
        for root, dirs, files in os.walk('templates'):
            for file in files:
                if file.endswith('.html'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # Find all translation calls
                        translations = re.findall(r't\(["\']([^"\']+)["\']\)', content)
                        for trans in translations:
                            if trans not in translation_patterns:
                                translation_patterns[trans] = []
                            translation_patterns[trans].append(file_path)
                            
                    except Exception as e:
                        self.log_error(file_path, 0, f"Failed to check translations: {str(e)}")
        
        # Check if translations are defined in app.py
        if os.path.exists('app.py'):
            with open('app.py', 'r', encoding='utf-8') as f:
                app_content = f.read()
                
            for trans_key in translation_patterns:
                if f"'{trans_key}'" not in app_content and f'"{trans_key}"' not in app_content:
                    files_using = ', '.join(translation_patterns[trans_key])
                    self.log_warning('Translation', 0, f"Translation key '{trans_key}' used in {files_using} but not found in app.py")

    def scan_all_files(self):
        """Scan all files in the project"""
        print("🔍 Starting comprehensive codebase scan...")
        
        # Check project structure
        self.check_file_structure()
        
        # Check security issues
        self.check_security_issues()
        
        # Check translation consistency
        self.check_translation_consistency()
        
        # Scan all files
        for root, dirs, files in os.walk('.'):
            # Skip certain directories
            if any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache', 'node_modules']):
                continue
                
            for file in files:
                file_path = os.path.join(root, file)
                self.files_scanned += 1
                
                if file.endswith('.py'):
                    self.scan_python_file(file_path)
                elif file.endswith(('.html', '.htm')):
                    self.scan_html_file(file_path)
                elif file.endswith(('.css', '.js')):
                    self.scan_css_js_file(file_path)

    def generate_report(self):
        """Generate comprehensive report"""
        print("\n" + "="*80)
        print("📊 EXLIPA CODEBASE SCAN REPORT")
        print("="*80)
        
        print(f"\n📁 Files Scanned: {self.files_scanned}")
        print(f"❌ Errors Found: {len(self.errors)}")
        print(f"⚠️  Warnings Found: {len(self.warnings)}")
        print(f"ℹ️  Info Items: {len(self.info)}")
        
        if self.errors:
            print("\n" + "="*50)
            print("❌ CRITICAL ERRORS")
            print("="*50)
            for error in self.errors:
                print(error)
        
        if self.warnings:
            print("\n" + "="*50)
            print("⚠️  WARNINGS")
            print("="*50)
            for warning in self.warnings[:20]:  # Limit to first 20
                print(warning)
            if len(self.warnings) > 20:
                print(f"... and {len(self.warnings) - 20} more warnings")
        
        if self.info:
            print("\n" + "="*50)
            print("ℹ️  INFORMATION")
            print("="*50)
            for info in self.info[:10]:  # Limit to first 10
                print(info)
            if len(self.info) > 10:
                print(f"... and {len(self.info) - 10} more info items")
        
        # Overall assessment
        print("\n" + "="*50)
        print("🎯 OVERALL ASSESSMENT")
        print("="*50)
        
        if len(self.errors) == 0:
            print("✅ No critical errors found!")
        else:
            print(f"❌ {len(self.errors)} critical errors need immediate attention")
            
        if len(self.warnings) < 10:
            print("✅ Low number of warnings - good code quality")
        elif len(self.warnings) < 30:
            print("⚠️  Moderate number of warnings - consider addressing")
        else:
            print("❌ High number of warnings - code quality needs improvement")
            
        print("\n🚀 RECOMMENDATIONS:")
        if self.errors:
            print("1. Fix all critical errors before deployment")
        if len(self.warnings) > 20:
            print("2. Address high-priority warnings")
        print("3. Review translation consistency")
        print("4. Ensure security configurations are production-ready")
        
        return len(self.errors) == 0 and len(self.warnings) < 30

def main():
    scanner = CodebaseScanner()
    scanner.scan_all_files()
    success = scanner.generate_report()
    
    if success:
        print("\n🎉 CODEBASE SCAN PASSED - Ready for production!")
        return 0
    else:
        print("\n❌ CODEBASE SCAN FAILED - Issues need attention")
        return 1

if __name__ == '__main__':
    sys.exit(main())
