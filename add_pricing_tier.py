#!/usr/bin/env python3
"""
Add the missing pricing_tier column
"""

import sqlite3

def add_pricing_tier_column():
    try:
        conn = sqlite3.connect('payment_system.db')
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(client_company)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'pricing_tier' not in columns:
            cursor.execute("ALTER TABLE client_company ADD COLUMN pricing_tier VARCHAR(20) DEFAULT 'Starter'")
            conn.commit()
            print('✅ Added pricing_tier column')
        else:
            print('ℹ️  pricing_tier column already exists')
        
        # Verify
        cursor.execute("PRAGMA table_info(client_company)")
        final_columns = [row[1] for row in cursor.fetchall()]
        
        if 'pricing_tier' in final_columns:
            print('✅ pricing_tier column confirmed')
        else:
            print('❌ pricing_tier column still missing')
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == '__main__':
    add_pricing_tier_column()
