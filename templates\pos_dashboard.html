{% extends "base.html" %}

{% block title %}POS {%- if session.language == "sw" -%}Mfumo{%- else -%}System{%- endif -%} - {{ company.company_name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-cash-register me-2 text-warning"></i>POS System</h2>
            <p class="text-muted mb-0">{{ company.company_name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ url_for('pos_products') }}" class="btn btn-outline-primary">
                <i class="fas fa-boxes me-1"></i>Products
            </a>
            <a href="{{ url_for('pos_sales') }}" class="btn btn-outline-success">
                <i class="fas fa-chart-line me-1"></i>Sales
            </a>
            <a href="{{ url_for('company_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Left Panel - Products -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-shopping-basket me-2"></i>Products</h5>
                    <span class="badge bg-info">{{ products|length }} items</span>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    {% if products %}
                        <div class="row g-3">
                            {% for product in products %}
                            <div class="col-md-6 col-lg-4">
                                <div class="card product-card h-100" 
                                     data-id="{{ product.id }}"
                                     data-name="{{ product.name }}"
                                     data-price="{{ product.price }}"
                                     data-stock="{{ product.stock_quantity }}"
                                     data-track-inventory="{{ product.track_inventory }}">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ product.name }}</h6>
                                        <p class="text-success fw-bold mb-2">
                                            TZS {{ "{:,.0f}".format(product.price) }}
                                        </p>
                                        {% if product.track_inventory %}
                                            <small class="text-muted d-block mb-2">
                                                Stock: {{ product.stock_quantity }}
                                                {% if product.stock_quantity <= product.low_stock_alert %}
                                                    <span class="text-danger">(Low)</span>
                                                {% endif %}
                                            </small>
                                        {% endif %}
                                        <button class="btn btn-primary btn-sm add-to-cart" 
                                                {% if product.track_inventory and product.stock_quantity <= 0 %}disabled{% endif %}>
                                            <i class="fas fa-plus me-1"></i>Add
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No products available</h5>
                            <p class="text-muted">Add products to start selling</p>
                            <a href="{{ url_for('add_pos_product') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Add First Product
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Right Panel - Cart & Checkout -->
        <div class="col-lg-4">
            <div class="card sticky-top">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Cart</h5>
                    <button id="clear-cart" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <!-- {%- if session.language == "sw" -%}Mteja{%- else -%}Customer{%- endif -%} {%- if session.language == "sw" -%}Taarifa{%- else -%}Info{%- endif -%} -->
                    <div class="mb-3">
                        <label class="form-label">Customer ({%- if session.language == "sw" -%}Si Lazima{%- else -%}Optional{%- endif -%})</label>
                        <input type="text" id="customer-name" class="form-control form-control-sm" placeholder="Customer name">
                        <input type="text" id="customer-phone" class="form-control form-control-sm mt-1" placeholder="Phone number">
                    </div>

                    <!-- Cart Items -->
                    <div id="cart-items" class="mb-3" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p class="mb-0">Cart is empty</p>
                        </div>
                    </div>

                    <!-- {%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}s -->
                    <div class="border-top pt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="subtotal">TZS 0</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (18%):</span>
                            <span id="tax">TZS 0</span>
                        </div>
                        <div class="d-flex justify-content-between fw-bold fs-5 border-top pt-2">
                            <span>{%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}:</span>
                            <span id="total" class="text-success">TZS 0</span>
                        </div>
                    </div>

                    <!-- {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} {%- if session.language == "sw" -%}Njia{%- else -%}Method{%- endif -%} -->
                    <div class="mt-3">
                        <label class="form-label">{{ t('Payment Method') }}</label>
                        <select id="payment-method" class="form-select form-select-sm">
                            <option value="Cash">Cash</option>
                            <option value="Mobile Money">{{ t('Mobile Money') }}</option>
                            <option value="Card">Card</option>
                        </select>
                    </div>

                    <!-- Checkout Button -->
                    <button id="checkout-btn" class="btn btn-success w-100 mt-3" disabled>
                        <i class="fas fa-credit-card me-1"></i>Complete Sale
                    </button>
                </div>
            </div>

            <!-- {%- if session.language == "sw" -%}Leo{%- else -%}Today{%- endif -%}'s Sales -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                    <h6 class="mb-1">Today's Sales</h6>
                    <h4 class="text-success mb-0">TZS {{ "{:,.0f}".format(today_sales) }}</h4>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cash {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Modal -->
<div class="modal fade" id="cashPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-money-bill-wave text-success me-2"></i>Cash Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Sale Summary</h6>
                        <div class="mb-2">
                            <span class="text-muted">Subtotal:</span>
                            <span class="float-end fw-bold" id="cash-subtotal">TZS 0</span>
                        </div>
                        <div class="mb-2">
                            <span class="text-muted">Tax:</span>
                            <span class="float-end fw-bold" id="cash-tax">TZS 0</span>
                        </div>
                        <hr>
                        <div class="mb-3">
                            <span class="text-muted">{%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}:</span>
                            <span class="float-end fw-bold fs-5 text-success" id="cash-total">TZS 0</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Cash Handling</h6>
                        <div class="mb-3">
                            <label for="amount-received" class="form-label">Amount Received</label>
                            <input type="number" class="form-control form-control-lg" id="amount-received" 
                                   placeholder="0.00" step="0.01" min="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Change Due</label>
                            <div class="form-control form-control-lg bg-light fw-bold fs-5" id="change-due">TZS 0.00</div>
                        </div>
                        <div class="alert alert-warning d-none" id="insufficient-amount">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {%- if session.language == "sw" -%}Kiasi{%- else -%}Amount{%- endif -%} received is less than total due
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">{{ t('{%- if session.language == "sw" -%}Ghairi{%- else -%}Cancel{%- endif -%}') }}</button>
                <button type="button" class="btn btn-success" id="complete-cash-sale" disabled>
                    <i class="fas fa-check me-2"></i>Complete Sale
                </button>
            </div>
        </div>
    </div>
</div>

<!-- {%- if session.language == "sw" -%}Mafanikio{%- else -%}Success{%- endif -%} Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-check-circle text-success me-2"></i>Sale Completed</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <h4 class="text-success mb-3">Sale Successfully Processed!</h4>
                <p class="mb-2">Sale {%- if session.language == "sw" -%}Nambari{%- else -%}Number{%- endif -%}: <strong id="sale-number"></strong></p>
                <p class="mb-2">{%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}: <strong id="sale-total"></strong></p>
                <p class="mb-0 text-muted" id="change-message"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">{{ t('{%- if session.language == "sw" -%}Endelea{%- else -%}Continue{%- endif -%}') }}</button>
            </div>
        </div>
    </div>
</div>

<style>
.product-card {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.cart-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.quantity-control {
    width: 80px;
}

.sticky-top {
    top: 20px;
}
</style>

<script>
let cart = [];
let taxRate = 0.18; // 18% VAT

// {%- if session.language == "sw" -%}Ongeza{%- else -%}Add{%- endif -%} to cart functionality
document.querySelectorAll('.add-to-cart').forEach(btn => {
    btn.addEventListener('click', function() {
        const card = this.closest('.product-card');
        const productId = parseInt(card.dataset.id);
        const productName = card.dataset.name;
        const productPrice = parseFloat(card.dataset.price);
        const stock = parseInt(card.dataset.stock);
        const trackInventory = card.dataset.trackInventory === 'True';

        // Check if product exists in cart
        const existingItem = cart.find(item => item.id === productId);
        
        if (existingItem) {
            if (trackInventory && existingItem.quantity >= stock) {
                alert('Not enough stock available');
                return;
            }
            existingItem.quantity += 1;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            if (trackInventory && stock <= 0) {
                alert('Product out of stock');
                return;
            }
            cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                quantity: 1,
                total: productPrice,
                maxStock: trackInventory ? stock : null
            });
        }

        updateCartDisplay();
    });
});

// {%- if session.language == "sw" -%}Sasisha{%- else -%}Update{%- endif -%} cart display
function updateCartDisplay() {
    const cartItemsDiv = document.getElementById('cart-items');
    const subtotalSpan = document.getElementById('subtotal');
    const taxSpan = document.getElementById('tax');
    const totalSpan = document.getElementById('total');
    const checkoutBtn = document.getElementById('checkout-btn');

    if (cart.length === 0) {
        cartItemsDiv.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <p class="mb-0">Cart is empty</p>
            </div>
        `;
        subtotalSpan.textContent = 'TZS 0';
        taxSpan.textContent = 'TZS 0';
        totalSpan.textContent = 'TZS 0';
        checkoutBtn.disabled = true;
        return;
    }

    // Generate cart items HTML
    let cartHTML = '';
    let subtotal = 0;

    cart.forEach((item, index) => {
        subtotal += item.total;
        cartHTML += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">TZS ${item.price.toLocaleString()}</small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${index}, -1)">-</button>
                        <span class="quantity-control text-center">${item.quantity}</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${index}, 1)" 
                                ${item.maxStock && item.quantity >= item.maxStock ? 'disabled' : ''}>+</button>
                        <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="text-end mt-1">
                    <strong>TZS ${item.total.toLocaleString()}</strong>
                </div>
            </div>
        `;
    });

    cartItemsDiv.innerHTML = cartHTML;

    // Calculate totals
    const tax = subtotal * taxRate;
    const total = subtotal + tax;

    subtotalSpan.textContent = `TZS ${subtotal.toLocaleString()}`;
    taxSpan.textContent = `TZS ${tax.toLocaleString()}`;
    totalSpan.textContent = `TZS ${total.toLocaleString()}`;
    
    checkoutBtn.disabled = false;
}

// {%- if session.language == "sw" -%}Sasisha{%- else -%}Update{%- endif -%} quantity
function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    if (item.maxStock && newQuantity > item.maxStock) {
        alert('Not enough stock available');
        return;
    }
    
    item.quantity = newQuantity;
    item.total = item.quantity * item.price;
    updateCartDisplay();
}

// Remove from cart
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

// Clear cart
document.getElementById('clear-cart').addEventListener('click', function() {
    cart = [];
    updateCartDisplay();
});

// Checkout
document.getElementById('checkout-btn').addEventListener('click', function() {
    if (cart.length === 0) return;

    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * taxRate;
    const total = subtotal + tax;
    const paymentMethod = document.getElementById('payment-method').value;

    // For cash payments, show cash handling modal
    if (paymentMethod === 'Cash') {
        showCashPaymentModal(total, subtotal, tax);
        return;
    }

    const saleData = {
        customer_name: document.getElementById('customer-name').value,
        customer_phone: document.getElementById('customer-phone').value,
        items: cart,
        subtotal: subtotal,
        tax_amount: tax,
        total_amount: total,
        payment_method: paymentMethod
    };

    // Process sale
    fetch('/pos/sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            document.getElementById('sale-number').textContent = data.sale_number;
            document.getElementById('sale-total').textContent = `TZS ${total.toLocaleString()}`;
            
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
            
            // Clear cart
            cart = [];
            updateCartDisplay();
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';
            
            // {%- if session.language == "sw" -%}Sasisha{%- else -%}Update{%- endif -%} today's sales (simple reload for now)
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('Error processing sale: ' + data.error);
        }
    })
    .catch(error => {
        console.error('{%- if session.language == "sw" -%}Hitilafu{%- else -%}Error{%- endif -%}:', error);
        alert('Error processing sale');
    });
});

// Cash {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Modal Functions
function showCashPaymentModal(total, subtotal, tax) {
    document.getElementById('cash-subtotal').textContent = `TZS ${subtotal.toLocaleString()}`;
    document.getElementById('cash-tax').textContent = `TZS ${tax.toLocaleString()}`;
    document.getElementById('cash-total').textContent = `TZS ${total.toLocaleString()}`;
    document.getElementById('amount-received').value = '';
    document.getElementById('change-due').textContent = 'TZS 0.00';
    document.getElementById('complete-cash-sale').disabled = true;
    document.getElementById('insufficient-amount').classList.add('d-none');
    
    const modal = new bootstrap.Modal(document.getElementById('cashPaymentModal'));
    modal.show();
    
    // Store values for later use
    window.cashSaleData = {
        total: total,
        subtotal: subtotal,
        tax: tax
    };
}

// {%- if session.language == "sw" -%}Kiasi{%- else -%}Amount{%- endif -%} received input handler
document.getElementById('amount-received').addEventListener('input', function() {
    const amountReceived = parseFloat(this.value) || 0;
    const total = window.cashSaleData.total;
    const change = Math.max(0, amountReceived - total);
    
    document.getElementById('change-due').textContent = `TZS ${change.toLocaleString()}`;
    
    if (amountReceived < total) {
        document.getElementById('insufficient-amount').classList.remove('d-none');
        document.getElementById('complete-cash-sale').disabled = true;
    } else {
        document.getElementById('insufficient-amount').classList.add('d-none');
        document.getElementById('complete-cash-sale').disabled = false;
    }
});

// {%- if session.language == "sw" -%}Kamili{%- else -%}Complete{%- endif -%} cash sale
document.getElementById('complete-cash-sale').addEventListener('click', function() {
    const amountReceived = parseFloat(document.getElementById('amount-received').value) || 0;
    const total = window.cashSaleData.total;
    const change = Math.max(0, amountReceived - total);
    
    const saleData = {
        customer_name: document.getElementById('customer-name').value,
        customer_phone: document.getElementById('customer-phone').value,
        items: cart,
        subtotal: window.cashSaleData.subtotal,
        tax_amount: window.cashSaleData.tax,
        total_amount: total,
        payment_method: 'Cash',
        amount_received: amountReceived,
        change_given: change
    };
    
    // Close cash modal
    bootstrap.Modal.getInstance(document.getElementById('cashPaymentModal')).hide();
    
    // Process sale
    fetch('/pos/sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            document.getElementById('sale-number').textContent = data.sale_number;
            document.getElementById('sale-total').textContent = `TZS ${total.toLocaleString()}`;
            document.getElementById('change-message').textContent = 
                change > 0 ? `Change given: TZS ${change.toLocaleString()}` : '';
            
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
            
            // Clear cart
            cart = [];
            updateCartDisplay();
        } else {
            alert('{%- if session.language == "sw" -%}Hitilafu{%- else -%}Error{%- endif -%}: ' + data.error);
        }
    })
    .catch(error => {
        console.error('{%- if session.language == "sw" -%}Hitilafu{%- else -%}Error{%- endif -%}:', error);
        alert('Error processing sale');
    });
});
</script>
{% endblock %}
