{% extends "base.html" %}

{% block title %}My Invoices - EXLIPA{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fw-bold gradient-text">
            <i class="fas fa-file-invoice me-2"></i>My Invoices
        </h2>
        <div>
            <a href="/user-admin" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
            <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Create Invoice
            </a>
        </div>
    </div>

    {% if company %}
    <div class="glass-card p-3 mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1">{{ company.company_name }}</h5>
                <p class="text-muted mb-0">{{ company.company_email }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-success">
                    {{ company.pricing_tier.name if company.pricing_tier else 'Free' }} Tier
                </span>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="glass-card">
        <div class="card-header bg-transparent">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Invoice History
            </h5>
        </div>
        <div class="card-body">
            {% if invoices %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Customer</th>
                            <th>{{ t('Amount') }}</th>
                            <th>{{ t('Status') }}</th>
                            <th>Due Date</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>
                                <strong>{{ invoice.invoice_number }}</strong>
                            </td>
                            <td>{{ invoice.customer_name }}</td>
                            <td>
                                <strong>{{ invoice.currency_symbol }} {{ "{:,.2f}".format(invoice.total_amount) }}</strong>
                            </td>
                            <td>
                                {% if invoice.status == 'Paid' %}
                                    <span class="badge bg-success">Paid</span>
                                {% elif invoice.status == 'Pending' %}
                                    <span class="badge bg-warning">{{ t('Pending') }}</span>
                                {% elif invoice.status == 'Overdue' %}
                                    <span class="badge bg-danger">Overdue</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ invoice.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if invoice.due_date %}
                                    {{ invoice.due_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>{{ invoice.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('invoice_detail', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if invoice.status != 'Paid' %}
                                    <a href="{{ url_for('edit_invoice', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-warning" title="{{ t('Edit') }}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{{ url_for('download_invoice', invoice_id=invoice.id) }}" 
                                       class="btn btn-outline-success" title="Download PDF">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Invoices Yet</h5>
                <p class="text-muted">You haven't created any invoices yet.</p>
                <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create Your First Invoice
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    {% if invoices %}
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">Total Invoices</h6>
                <h4 class="mb-0">{{ invoices|length }}</h4>
            </div>
        </div>
        <div class="col-md-4">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">Paid Invoices</h6>
                <h4 class="mb-0 text-success">{{ invoices|selectattr('status', 'equalto', 'Paid')|list|length }}</h4>
            </div>
        </div>
        <div class="col-md-4">
            <div class="glass-card p-3 text-center">
                <h6 class="text-muted mb-1">Pending Invoices</h6>
                <h4 class="mb-0 text-warning">{{ invoices|selectattr('status', 'equalto', 'Pending')|list|length }}</h4>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
