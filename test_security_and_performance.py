#!/usr/bin/env python3
"""
Comprehensive test suite for security and performance improvements
"""

import pytest
import json
from datetime import datetime, timedelta
from app import app, db, User, ClientCompany, PaymentConfirmation, Invoice, PricingTier
from werkzeug.security import generate_password_hash

@pytest.fixture
def client():
    """Create test client"""
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing
    
    with app.test_client() as client:
        with app.app_context():
            db.create_all()
            create_test_data()
            yield client

def create_test_data():
    """Create test data for tests"""
    # Create admin user
    admin = User(
        username='testadmin',
        password_hash=generate_password_hash('testpass'),
        email='<EMAIL>',
        role='admin'
    )
    db.session.add(admin)
    
    # Create pricing tier
    tier = PricingTier(
        name='Test Tier',
        monthly_fee=75000,
        setup_fee=200000,
        transaction_fee_percentage=2.0
    )
    db.session.add(tier)
    
    # Create test company
    company = ClientCompany(
        company_name='Test Company',
        pricing_tier_id=1,
        created_by=1
    )
    db.session.add(company)
    
    # Create test invoice
    invoice = Invoice(
        invoice_number='INV-2024-TEST01',
        customer_name='Test Customer',
        customer_email='<EMAIL>',
        amount=50000.0,
        service_description='Test Service'
    )
    db.session.add(invoice)
    
    db.session.commit()

class TestSecurityFeatures:
    """Test security enhancements"""
    
    def test_health_check_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get('/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'timestamp' in data
        assert data['database'] == 'connected'
    
    def test_metrics_endpoint(self, client):
        """Test metrics endpoint"""
        response = client.get('/metrics')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'companies' in data
        assert 'payments' in data
        assert 'invoices' in data
        assert 'revenue' in data
        assert 'timestamp' in data
    
    def test_api_key_required(self, client):
        """Test API key requirement for cart endpoint"""
        cart_data = {
            'customer_name': 'Test Customer',
            'customer_email': '<EMAIL>',
            'cart_items': [{'name': 'Test Item', 'quantity': 1}],
            'total_amount': 25000
        }
        
        # Request without API key should fail
        response = client.post('/create-invoice-from-cart',
                             json=cart_data,
                             content_type='application/json')
        assert response.status_code == 401
        
        data = json.loads(response.data)
        assert 'API key required' in data['error']
    
    def test_api_key_valid(self, client):
        """Test valid API key for cart endpoint"""
        cart_data = {
            'customer_name': 'Test Customer',
            'customer_email': '<EMAIL>',
            'cart_items': [{'name': 'Test Item', 'quantity': 1}],
            'total_amount': 25000
        }
        
        # Request with valid API key should succeed
        response = client.post('/create-invoice-from-cart',
                             json=cart_data,
                             headers={'X-API-Key': 'exlipa-api-key-2024'},
                             content_type='application/json')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'invoice_number' in data

class TestPerformanceFeatures:
    """Test performance improvements"""
    
    def test_metrics_caching(self, client):
        """Test that metrics are cached"""
        # First request
        response1 = client.get('/metrics')
        assert response1.status_code == 200
        
        # Second request should be faster (cached)
        response2 = client.get('/metrics')
        assert response2.status_code == 200
        
        # Should return same data
        data1 = json.loads(response1.data)
        data2 = json.loads(response2.data)
        assert data1['timestamp'] == data2['timestamp']  # Cached response
    
    def test_company_settings_function(self, client):
        """Test company settings retrieval"""
        from app import get_company_settings
        
        # Test with existing company
        settings = get_company_settings(1)
        assert settings.company_name == 'Test Company'
        
        # Test with non-existent company (should return default)
        default_settings = get_company_settings(999)
        assert hasattr(default_settings, 'company_name')

class TestBusinessLogic:
    """Test business logic functions"""
    
    def test_fee_calculation(self, client):
        """Test transaction fee calculation"""
        from app import calculate_transaction_fee, ClientCompany
        
        company = ClientCompany.query.first()
        fee = calculate_transaction_fee(100000, company)
        
        # Should be 2% of 100,000 = 2,000
        assert fee == 2000.0
    
    def test_invoice_number_generation(self, client):
        """Test unique invoice number generation"""
        from app import generate_invoice_number
        
        # Generate multiple invoice numbers
        numbers = [generate_invoice_number() for _ in range(5)]
        
        # All should be unique
        assert len(set(numbers)) == 5
        
        # All should follow correct format
        for number in numbers:
            assert number.startswith('INV-2024-')
            assert len(number) == 14  # INV-YYYY-XXXXXX
    
    def test_bill_number_generation(self, client):
        """Test unique bill number generation"""
        from app import generate_bill_number
        
        # Generate multiple bill numbers
        numbers = [generate_bill_number() for _ in range(5)]
        
        # All should be unique
        assert len(set(numbers)) == 5
        
        # All should follow correct format
        for number in numbers:
            assert number.startswith('BILL-2024-')
            assert len(number) == 15  # BILL-YYYY-XXXXXX

class TestErrorHandling:
    """Test error handling improvements"""
    
    def test_invalid_json_data(self, client):
        """Test handling of invalid JSON data"""
        response = client.post('/create-invoice-from-cart',
                             data='invalid json',
                             headers={'X-API-Key': 'exlipa-api-key-2024'},
                             content_type='application/json')
        
        # Should handle gracefully
        assert response.status_code in [400, 500]
    
    def test_missing_required_fields(self, client):
        """Test handling of missing required fields"""
        incomplete_data = {
            'customer_name': 'Test Customer'
            # Missing required fields
        }
        
        response = client.post('/create-invoice-from-cart',
                             json=incomplete_data,
                             headers={'X-API-Key': 'exlipa-api-key-2024'},
                             content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'Missing required field' in data['error']

class TestDatabaseIntegrity:
    """Test database integrity and relationships"""
    
    def test_user_company_relationship(self, client):
        """Test user-company relationship"""
        user = User.query.first()
        company = ClientCompany.query.first()
        
        assert company.created_by == user.id
    
    def test_payment_confirmation_creation(self, client):
        """Test payment confirmation creation"""
        confirmation = PaymentConfirmation(
            customer_name='Test Customer',
            amount=25000.0,
            mobile_operator='M-Pesa',
            transaction_id='TEST123456'
        )
        
        db.session.add(confirmation)
        db.session.commit()
        
        # Verify creation
        saved_confirmation = PaymentConfirmation.query.filter_by(
            transaction_id='TEST123456'
        ).first()
        
        assert saved_confirmation is not None
        assert saved_confirmation.customer_name == 'Test Customer'
        assert saved_confirmation.status == 'Pending'  # Default status

if __name__ == "__main__":
    print("Running comprehensive security and performance tests...")
    
    # Run basic import tests
    try:
        from app import app, db, logger
        print("✅ All imports successful")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        exit(1)
    
    # Test basic functionality
    try:
        with app.app_context():
            # Test database
            db.session.execute('SELECT 1')
            print("✅ Database connection working")
            
            # Test logging
            logger.info("Test log message")
            print("✅ Logging system working")
            
            # Test cache (simple test)
            from app import cache
            cache.set('test_key', 'test_value', timeout=10)
            value = cache.get('test_key')
            if value == 'test_value':
                print("✅ Caching system working")
            else:
                print("⚠️ Caching system may not be working properly")
                
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        exit(1)
    
    print("\n🎉 All basic tests passed!")
    print("\nTo run full test suite:")
    print("1. Install pytest: pip install pytest")
    print("2. Run tests: pytest test_security_and_performance.py -v")
    print("3. Run with coverage: pytest test_security_and_performance.py --cov=app")
