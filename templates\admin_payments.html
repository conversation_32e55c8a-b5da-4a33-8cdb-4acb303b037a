{% extends "base.html" %}

{% block title %}Payment Management - Payment System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-list me-2"></i>Payment Confirmations</h1>
    <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status Filter</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statuses</option>
                    <option value="Pending" {% if status_filter == 'Pending' %}selected{% endif %}>{{ t('Pending') }}</option>
                    <option value="Confirmed" {% if status_filter == 'Confirmed' %}selected{% endif %}>{{ t('Confirmed') }}</option>
                    <option value="Rejected" {% if status_filter == 'Rejected' %}selected{% endif %}>{{ t('Rejected') }}</option>
                    <option value="Processed" {% if status_filter == 'Processed' %}selected{% endif %}>Processed</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">{{ t('Search') }}</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Search by customer name or transaction ID..." value="{{ search }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ url_for('admin_payments') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Payments Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>Payment Confirmations
            <span class="badge bg-secondary ms-2">{{ payments|length }} records</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if payments %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Customer</th>
                        <th>Amount (TZS)</th>
                        <th>Operator</th>
                        <th>{{ t('Transaction ID') }}</th>
                        <th>Submitted</th>
                        <th>{{ t('Status') }}</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>REF{{ "%06d"|format(payment.id) }}</td>
                        <td>
                            <div>{{ payment.customer_name }}</div>
                            {% if payment.customer_email %}
                            <small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>{{ payment.customer_email }}
                            </small>
                            {% endif %}
                        </td>
                        <td>{{ "{:,.0f}".format(payment.amount or 0) }}</td>
                        <td>
                            <span class="badge 
                                {% if payment.mobile_operator == 'TigoPesa' %}bg-primary
                                {% elif payment.mobile_operator == 'Airtel Money' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {{ payment.mobile_operator }}
                            </span>
                        </td>
                        <td><code>{{ payment.transaction_id }}</code></td>
                        <td>{{ payment.submitted_at.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td>
                            <span class="badge 
                                {% if payment.status == 'Pending' %}bg-warning text-dark
                                {% elif payment.status == 'Confirmed' %}bg-success
                                {% elif payment.status == 'Rejected' %}bg-danger
                                {% elif payment.status == 'Processed' %}bg-info
                                {% else %}bg-secondary{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                        <td>
                            <a href="{{ url_for('payment_detail', payment_id=payment.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No payments found</h5>
            <p class="text-muted">
                {% if status_filter != 'all' or search %}
                    Try adjusting your filters or search terms.
                {% else %}
                    Payment confirmations will appear here once customers submit them.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
