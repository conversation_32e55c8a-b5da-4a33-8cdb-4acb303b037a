{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Request POS Unlock - Exlipa
{%- else -%}
    Request POS Unlock - Exlipa
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-7">
            <div class="glass-card p-4">
                <h3 class="fw-bold mb-3 text-center"><i class="fas fa-cash-register me-2 text-warning"></i>Request POS Unlock</h3>
                <p class="mb-3">To unlock the Dynamic POS feature for your company, please pay the one-time POS add-on fee and submit your request below. Once payment is confirmed, the admin will activate POS for your account.</p>
                <div class="alert alert-info mb-3">
                    <strong>POS Add-on Price:</strong> <span class="text-success">TZS {{ "{:,}".format(pos_price|int) }}</span><br>
                    <strong>Pay to:</strong> <br>
                    <ul class="mb-1">
                        <li>M-Pesa Till: <strong>123456</strong></li>
                        <li>Tigo Pesa Paybill: <strong>654321</strong></li>
                        <li>Airtel Money Merchant: <strong>789012</strong></li>
                        <li>CRDB Lipa: <strong>890123</strong></li>
                    </ul>
                    <small>Use your company name as reference.</small>
                </div>
                <form method="POST">
                    <div class="mb-3">
                        <label for="payment_reference" class="form-label">Payment Reference / Transaction ID</label>
                        <input type="text" class="form-control" id="payment_reference" name="payment_reference" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Submit POS Unlock Request</button>
                    </div>
                </form>
                <div class="mt-3 text-center">
                    <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .glass-card { background: rgba(255,255,255,0.85); box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10); border-radius: 1.2rem; border: 1px solid rgba(255,255,255,0.18); transition: box-shadow 0.3s; }
    .glass-card:hover { box-shadow: 0 12px 36px 0 rgba(31,38,135,0.18); }
</style>
{% endblock %}
