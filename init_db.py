#!/usr/bin/env python3
"""
Initialize database with all tables including new columns
"""

from app import app, db, User, ClientCompany, PricingTier
from sqlalchemy import inspect

def initialize_database():
    """Initialize the database with all tables"""
    with app.app_context():
        print('🔄 Initializing database...')
        
        # Drop all tables first (clean slate)
        db.drop_all()
        print('🗑️  Dropped existing tables')
        
        # Create all tables with current schema
        db.create_all()
        print('✅ Created all tables')
        
        # Verify tables were created
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        print(f'📊 Tables created: {len(tables)}')
        for table in tables:
            print(f'  - {table}')
        
        # Check client_company table specifically
        if 'client_company' in tables:
            columns = inspector.get_columns('client_company')
            print(f'\n🔍 client_company table has {len(columns)} columns:')
            for col in columns:
                col_name = col['name']
                col_type = str(col['type'])
                print(f'  - {col_name} ({col_type})')
            
            # Check for our new columns
            column_names = [col['name'] for col in columns]
            new_columns = ['pricing_tier', 'primary_color', 'secondary_color', 'font_family', 'template_style']
            
            print(f'\n🎯 Checking for new landing page columns:')
            for new_col in new_columns:
                if new_col in column_names:
                    print(f'  ✅ {new_col} - present')
                else:
                    print(f'  ❌ {new_col} - missing')
        
        print('\n🎉 Database initialization completed!')
        return True

if __name__ == '__main__':
    try:
        initialize_database()
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
