from flask import Flask, render_template, request, redirect, url_for, flash, session, make_response, abort, jsonify, g
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
import io
import random
import string
import json
import secrets
from dotenv import load_dotenv
from receipt_generator import generate_receipt_pdf, generate_receipt_filename
from werkzeug.utils import secure_filename
from itsdangerous import URLSafeTimedSerializer
from flask_mail import Mail, Message
from functools import wraps
import time

# Import new services
from services.auth_service import (
    AuthenticationManager, require_super_admin, require_user_admin,
    require_company_user, admin_required, require_permission, session_timeout_check
)
from utils.validators import InputValidator, ValidationError
from services.payment_verifier import PaymentValidator, MobileMoneyVerifier

# Performance optimization imports
try:
    from performance_optimizer import (
        perf_monitor, cache_result, QueryOptimizer,
        optimize_request, track_request_performance,
        FrontendOptimizer
    )
    PERFORMANCE_ENABLED = True
except ImportError:
    PERFORMANCE_ENABLED = False
    print("Performance optimizer not available - running in basic mode")

# Advanced features imports
try:
    from webhook_system import (
        webhook_manager, bulk_manager, automation_engine,
        WebhookEventType, trigger_payment_webhook, setup_default_automations
    )
    ADVANCED_FEATURES_ENABLED = True
except ImportError:
    ADVANCED_FEATURES_ENABLED = False
    print("Advanced features not available - running in basic mode")

# Import enhanced features with fallbacks
try:
    from flask_wtf.csrf import CSRFProtect
    from flask_limiter import Limiter
    from flask_limiter.util import get_remote_address
    from flask_caching import Cache
    from flask_babel import Babel, gettext, ngettext, lazy_gettext, get_locale
    import structlog
    import logging
    from functools import wraps
    ENHANCED_FEATURES = True
except ImportError as e:
    print(f"Enhanced features not available: {e}")
    print("Running in basic mode. Install: pip install flask-limiter flask-caching structlog redis flask-babel")
    ENHANCED_FEATURES = False

    # Fallback for Babel with simple translation loading
    def load_translations(lang='en'):
        """Load translations from compiled MO files"""
        try:
            mo_file = f'translations/{lang}/LC_MESSAGES/messages.mo'
            if os.path.exists(mo_file):
                translations = {}
                with open(mo_file, 'rb') as f:
                    content = f.read().decode('utf-8', errors='ignore')
                    for line in content.split('\n'):
                        if '|' in line and not line.startswith('EXLIPA_TRANSLATIONS'):
                            parts = line.split('|', 1)
                            if len(parts) == 2:
                                translations[parts[0]] = parts[1]
                return translations
        except:
            pass
        return {}

    # Load translations for current session
    def gettext(text):
        lang = session.get('language', 'en')
        if lang == 'en':
            return text

        if not hasattr(g, 'translations'):
            g.translations = load_translations(lang)

        return g.translations.get(text, text)

    def ngettext(singular, plural, num):
        return gettext(singular) if num == 1 else gettext(plural)

    def lazy_gettext(text):
        return gettext(text)

    def get_locale():
        return session.get('language', 'en')

# Simple rate limiting for basic mode
login_attempts = {}
from collections import defaultdict
import time

def simple_rate_limit(key, max_attempts=5, window_minutes=15):
    """Simple rate limiting when flask-limiter is not available"""
    if not ENHANCED_FEATURES:
        current_time = time.time()
        window_seconds = window_minutes * 60

        if key not in login_attempts:
            login_attempts[key] = []

        # Clean old attempts
        login_attempts[key] = [t for t in login_attempts[key] if current_time - t < window_seconds]

        # Check if limit exceeded
        if len(login_attempts[key]) >= max_attempts:
            return False

        # Record this attempt
        login_attempts[key].append(current_time)
        return True
    return True

def validate_payment_amount(amount):
    """Validate payment amount"""
    try:
        amount_float = float(amount)
        if amount_float < 1000:
            raise ValueError("Minimum payment amount is TZS 1,000")
        if amount_float > 10000000:
            raise ValueError("Maximum payment amount is TZS 10,000,000")
        return amount_float
    except (ValueError, TypeError):
        raise ValueError("Invalid amount format")

def validate_transaction_id(transaction_id, mobile_operator):
    """Validate transaction ID format"""
    if not transaction_id or len(transaction_id) < 8:
        raise ValueError("Transaction ID must be at least 8 characters")

    # Check for duplicate transaction ID
    existing = PaymentConfirmation.query.filter_by(transaction_id=transaction_id).first()
    if existing:
        raise ValueError("This transaction ID has already been used")

    # Format validation based on operator
    transaction_id_upper = transaction_id.upper()
    if mobile_operator == 'M-Pesa' and not transaction_id_upper.startswith(('ND', 'NE', 'NF')):
        raise ValueError("M-Pesa transaction ID should start with ND, NE, or NF")
    elif mobile_operator == 'TigoPesa' and not transaction_id_upper.startswith('TP'):
        raise ValueError("Tigo Pesa transaction ID should start with TP")
    elif mobile_operator == 'Airtel Money' and not transaction_id_upper.startswith('AM'):
        raise ValueError("Airtel Money transaction ID should start with AM")

    return transaction_id

def validate_mobile_money_name(name):
    """Validate mobile money sender name"""
    if not name or len(name.strip()) < 2:
        raise ValueError("Mobile money sender name is required")

    # Basic name validation - only letters and spaces
    if not all(c.isalpha() or c.isspace() for c in name):
        raise ValueError("Mobile money sender name should only contain letters and spaces")

    return name.strip().title()  # Capitalize properly

def sanitize_input(text, max_length=None):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""

    # Remove potentially dangerous characters
    import re
    text = re.sub(r'[<>"\']', '', str(text))

    if max_length:
        text = text[:max_length]

    return text.strip()

def validate_email_format(email):
    """Validate email format"""
    if not email:
        return False

    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone_format(phone):
    """Validate phone number format"""
    if not phone:
        return True  # Phone is optional

    import re
    # Allow Tanzanian phone formats
    pattern = r'^(\+255|0)[67]\d{8}$'
    return re.match(pattern, phone) is not None

def validate_tin_format(tin):
    """Validate TIN format"""
    if not tin:
        return True  # TIN is optional

    import re
    # Basic TIN format validation
    pattern = r'^\d{3}-\d{3}-\d{3}$'
    return re.match(pattern, tin) is not None

def secure_database_operation(operation_func, *args, **kwargs):
    """Wrapper for secure database operations with rollback"""
    try:
        result = operation_func(*args, **kwargs)
        db.session.commit()
        return result
    except Exception as e:
        db.session.rollback()
        if ENHANCED_FEATURES:
            logger.error("Database operation failed", error=str(e), operation=operation_func.__name__)
        raise e

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///payment_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Babel configuration for multilingual support
app.config['LANGUAGES'] = {
    'en': 'English',
    'sw': 'Kiswahili'
}
app.config['BABEL_DEFAULT_LOCALE'] = 'en'
app.config['BABEL_DEFAULT_TIMEZONE'] = 'Africa/Dar_es_Salaam'

# Security Configuration
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # 1 hour

# Cache Configuration
app.config['CACHE_TYPE'] = 'simple'  # Use 'redis' for production
app.config['CACHE_DEFAULT_TIMEOUT'] = 300  # 5 minutes

# Initialize Flask-Mail
app.config['MAIL_SERVER'] = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
app.config['MAIL_PORT'] = int(os.environ.get('MAIL_PORT', 587))
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = os.environ.get('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.environ.get('MAIL_PASSWORD')
app.config['MAIL_DEFAULT_SENDER'] = os.environ.get('MAIL_DEFAULT_SENDER', app.config['MAIL_USERNAME'])

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
# Don't set a global login_view - we'll handle redirects based on context

# Initialize Babel for multilingual support
if ENHANCED_FEATURES:
    try:
        babel = Babel(app)

        @babel.localeselector
        def get_locale():
            # 1. Check URL parameter
            if request.args.get('lang'):
                session['language'] = request.args.get('lang')

            # 2. Check session
            if 'language' in session:
                return session['language']

            # 3. Check user preference (if logged in)
            if current_user.is_authenticated and hasattr(current_user, 'preferred_language'):
                return current_user.preferred_language

            # 4. Check browser language
            return request.accept_languages.best_match(app.config['LANGUAGES'].keys()) or 'en'
    except:
        pass

# Template helper functions for translations
@app.template_global()
def _(text):
    """Translation function for templates"""
    return get_translation(text)

@app.template_global()
def t(text):
    """Short translation function for templates"""
    return get_translation(text)

@app.template_global()
def get_current_language():
    """Get current language for templates"""
    return session.get('language', 'en')

# Legacy functions - replaced by AuthenticationManager
# Keeping for backward compatibility during migration
def smart_redirect_to_login():
    """Legacy function - use AuthenticationManager.smart_redirect_to_login()"""
    return AuthenticationManager.smart_redirect_to_login()

def smart_redirect_by_role(user=None):
    """Legacy function - use AuthenticationManager.smart_redirect_by_role()"""
    return AuthenticationManager.smart_redirect_by_role(user)

# Set up Flask-Login unauthorized handler
@login_manager.unauthorized_handler
def unauthorized():
    return AuthenticationManager.smart_redirect_to_login()

# Initialize Extensions with fallbacks
if ENHANCED_FEATURES:
    # Initialize Security and Performance Extensions
    csrf = CSRFProtect(app)
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=["1000 per day", "100 per hour"],
        storage_uri="memory://"  # Use Redis URI in production
    )
    cache = Cache(app)
    
    # Configure Structured Logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer()
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    logger = structlog.get_logger()
else:
    # Fallback implementations
    csrf = None
    limiter = None
    cache = None
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

# Initialize Mail
mail = Mail(app)

# Error Handlers
@app.errorhandler(404)
def not_found_error(error):
    if ENHANCED_FEATURES:
        logger.warning("404 error", path=request.path, ip=get_remote_address())
    else:
        logger.warning(f"404 error: {request.path}")
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    if ENHANCED_FEATURES:
        logger.error("500 error", error=str(error), path=request.path, ip=get_remote_address())
    else:
        logger.error(f"500 error: {error}")
    db.session.rollback()
    return render_template('errors/500.html'), 500

if ENHANCED_FEATURES:
    @app.errorhandler(429)
    def ratelimit_handler(e):
        logger.warning("Rate limit exceeded", 
                      limit=str(e.limit), 
                      ip=get_remote_address(),
                      endpoint=request.endpoint)
        return jsonify({'error': 'Rate limit exceeded', 'message': str(e.description)}), 429

# API Authentication
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

def require_api_key(f):
    """Decorator to require API key for certain endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            if ENHANCED_FEATURES:
                logger.warning("API request without API key", endpoint=request.endpoint)
            else:
                logger.warning(f"API request without API key: {request.endpoint}")
            return jsonify({'error': 'API key required'}), 401
        
        # Simple API key validation - in production, use proper key management
        expected_key = os.environ.get('API_KEY', 'exlipa-api-key-2024')
        if api_key != expected_key:
            if ENHANCED_FEATURES:
                logger.warning("Invalid API key", endpoint=request.endpoint, api_key=api_key[:10])
            else:
                logger.warning(f"Invalid API key for {request.endpoint}")
            return jsonify({'error': 'Invalid API key'}), 401
            
        return f(*args, **kwargs)
    return decorated_function

# Safe limiter decorator that works even without flask-limiter
def safe_limit(limit_string):
    """Decorator that applies rate limiting only if limiter is available"""
    def decorator(f):
        if ENHANCED_FEATURES and limiter:
            return limiter.limit(limit_string)(f)
        else:
            return f
    return decorator

# Allowed logo extensions
ALLOWED_LOGO_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# Helper to check allowed file
def allowed_logo_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_LOGO_EXTENSIONS

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # super_admin (EXLIPA control), user_admin (company customer)
    is_active = db.Column(db.Boolean, default=True)
    preferred_language = db.Column(db.String(5), default='en')  # Language preference (en, sw)
    last_login = db.Column(db.DateTime)
    failed_login_attempts = db.Column(db.Integer, default=0)
    account_locked_until = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until and self.account_locked_until > datetime.utcnow():
            return True
        return False

    def lock_account(self, minutes=15):
        """Lock account for specified minutes"""
        self.account_locked_until = datetime.utcnow() + timedelta(minutes=minutes)
        db.session.commit()

    def unlock_account(self):
        """Unlock account and reset failed attempts"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        db.session.commit()

    def increment_failed_login(self):
        """Increment failed login attempts and lock if necessary"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.lock_account(15)  # Lock for 15 minutes
        db.session.commit()

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    amount = db.Column(db.Float, nullable=False)
    service_description = db.Column(db.Text, nullable=False)
    due_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='Unpaid')  # Unpaid, Paid, Overdue, Cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Cart integration fields
    cart_items = db.Column(db.Text)  # JSON string of cart items
    subtotal = db.Column(db.Float)
    tax_amount = db.Column(db.Float)
    discount_amount = db.Column(db.Float, default=0.0)
    shipping_cost = db.Column(db.Float, default=0.0)

class PaymentConfirmation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=True)
    client_company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=True)
    pricing_tier_id = db.Column(db.Integer, db.ForeignKey('pricing_tier.id'), nullable=True)  # Determined from payment amount
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(120), nullable=True)  # Email address for registration link
    mobile_money_sender_name = db.Column(db.String(100), nullable=False)  # Name registered with mobile money
    amount = db.Column(db.Float, nullable=False)
    transaction_fee = db.Column(db.Float, default=0.0)  # Calculated transaction fee
    net_amount = db.Column(db.Float, nullable=False)  # Amount after fees
    mobile_operator = db.Column(db.String(50), nullable=False)
    transaction_id = db.Column(db.String(100), nullable=False, unique=True)  # Ensure uniqueness
    service_description = db.Column(db.Text)
    status = db.Column(db.String(20), default='Pending')  # Pending, Confirmed, Rejected, Processed
    rejection_reason = db.Column(db.Text)
    internal_notes = db.Column(db.Text)

    # SMS Verification fields for admin
    sms_sender = db.Column(db.String(20))  # MPESA, TIGOPESA, etc.
    sms_amount = db.Column(db.Float)  # Amount from SMS
    sms_sender_name = db.Column(db.String(100))  # Sender name from SMS
    sms_transaction_ref = db.Column(db.String(100))  # Transaction ref from SMS
    admin_verification_notes = db.Column(db.Text)  # Admin notes during verification

    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    processed_at = db.Column(db.DateTime)
    processed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    receipt_generated = db.Column(db.Boolean, default=False)

    # Fee tracking
    transaction_fee = db.Column(db.Float, default=0.0)
    fee_calculated = db.Column(db.Boolean, default=False)

    # Validation constraints
    __table_args__ = (
        db.CheckConstraint('amount >= 1000', name='check_minimum_amount'),
        db.CheckConstraint('amount <= 10000000', name='check_maximum_amount'),
        db.CheckConstraint('invoice_id IS NOT NULL OR client_company_id IS NOT NULL',
                          name='check_payment_association'),
    )

    # Relationship to invoice
    invoice = db.relationship('Invoice', backref='payment_confirmations')

    def validate_amount(self):
        """Validate payment amount"""
        if self.amount < 1000:
            raise ValueError("Minimum payment amount is TZS 1,000")
        if self.amount > 10000000:
            raise ValueError("Maximum payment amount is TZS 10,000,000")

    def validate_transaction_id_format(self):
        """Validate transaction ID format based on mobile operator"""
        if not self.transaction_id or len(self.transaction_id) < 8:
            raise ValueError("Transaction ID must be at least 8 characters")

        # Basic format validation for different operators
        if self.mobile_operator == 'M-Pesa' and not self.transaction_id.upper().startswith(('ND', 'NE', 'NF')):
            raise ValueError("M-Pesa transaction ID should start with ND, NE, or NF")
        elif self.mobile_operator == 'TigoPesa' and not self.transaction_id.upper().startswith('TP'):
            raise ValueError("Tigo Pesa transaction ID should start with TP")
        elif self.mobile_operator == 'Airtel Money' and not self.transaction_id.upper().startswith('AM'):
            raise ValueError("Airtel Money transaction ID should start with AM")

    def validate_mobile_money_name(self):
        """Validate mobile money sender name"""
        if not self.mobile_money_sender_name or len(self.mobile_money_sender_name.strip()) < 2:
            raise ValueError("Mobile money sender name is required")

        # Basic name validation
        if not all(c.isalpha() or c.isspace() for c in self.mobile_money_sender_name):
            raise ValueError("Mobile money sender name should only contain letters and spaces")

class ClientCompany(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    company_name = db.Column(db.String(200), nullable=False)
    company_address = db.Column(db.Text)
    company_phone = db.Column(db.String(50))
    company_email = db.Column(db.String(100))
    company_website = db.Column(db.String(100))
    company_tin = db.Column(db.String(50))

    # Owner relationship - proper user ownership
    owner_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # Nullable for migration

    # Payment method configurations
    mpesa_till = db.Column(db.String(20))
    tigo_paybill = db.Column(db.String(20))
    airtel_merchant = db.Column(db.String(20))
    crdb_merchant = db.Column(db.String(20))
    
    # Branding
    logo_filename = db.Column(db.String(200))
    primary_color = db.Column(db.String(7), default='#28a745')
    secondary_color = db.Column(db.String(7), default='#007bff')
    currency_code = db.Column(db.String(3), default='TZS')
    currency_symbol = db.Column(db.String(5), default='TZS')
    
    # Subscription details
    pricing_tier_id = db.Column(db.Integer, db.ForeignKey('pricing_tier.id'))
    subscription_status = db.Column(db.String(20), default='Active')  # Active, Suspended, Cancelled
    setup_fee_paid = db.Column(db.Boolean, default=True)  # No setup fees in new model
    setup_fee_amount = db.Column(db.Float, default=0.0)  # Always 0 in new model

    # Freemium Usage Tracking
    monthly_transaction_count = db.Column(db.Integer, default=0)  # Current month transactions
    total_transaction_count = db.Column(db.Integer, default=0)  # All-time transactions
    free_transactions_used = db.Column(db.Integer, default=0)  # Free transactions used this month
    last_transaction_reset = db.Column(db.DateTime, default=datetime.utcnow)  # Monthly reset date
    subscription_start_date = db.Column(db.DateTime, default=datetime.utcnow)  # When subscription started
    
    # Billing
    billing_cycle = db.Column(db.String(20), default='Monthly')  # Monthly, Annual
    next_billing_date = db.Column(db.DateTime)
    total_transaction_volume = db.Column(db.Float, default=0.0)
    
    # Dynamic POS Feature
    dynamic_pos_enabled = db.Column(db.Boolean, default=False)
    dynamic_pos_payment_pending = db.Column(db.Boolean, default=False)
    
    # Public Landing Page
    is_public_page_enabled = db.Column(db.Boolean, default=True)
    custom_message = db.Column(db.Text)
    landing_page_title = db.Column(db.String(200))
    landing_page_description = db.Column(db.Text)

    # API Access (Business and Enterprise tiers)
    api_key = db.Column(db.String(64), unique=True, nullable=True)  # API key for external access
    api_secret = db.Column(db.String(128), nullable=True)  # API secret for authentication
    api_enabled = db.Column(db.Boolean, default=False)  # Whether API access is enabled
    api_rate_limit = db.Column(db.Integer, default=1000)  # Requests per hour
    api_last_used = db.Column(db.DateTime, nullable=True)  # Last API usage timestamp

    # Pricing tier and advanced customization (temporarily commented out for database issues)
    # pricing_tier = db.Column(db.String(20), default='Starter')  # Starter, Business, Enterprise
    # primary_color = db.Column(db.String(7), default='#3b82f6')
    # secondary_color = db.Column(db.String(7), default='#1d4ed8')
    # font_family = db.Column(db.String(50), default='Inter')
    # template_style = db.Column(db.String(50), default='basic')
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    pricing_tier = db.relationship('PricingTier', backref='companies')
    payment_confirmations = db.relationship('PaymentConfirmation', backref='client_company')
    owner = db.relationship('User', foreign_keys=[owner_user_id], backref='owned_companies')
    creator = db.relationship('User', foreign_keys=[created_by])

    def is_owned_by(self, user):
        """Check if company is owned by specific user"""
        return self.owner_user_id == user.id

    def can_be_accessed_by(self, user):
        """Check if user can access this company"""
        # Owner can always access
        if self.owner_user_id == user.id:
            return True
        # Master admins can access all companies
        if user.role == 'master_admin':
            return True
        # Company email matching (legacy support)
        if user.role == 'company_user' and self.company_email == user.email:
            return True
        return False

class PricingTier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # Free, Business, Enterprise
    description = db.Column(db.Text)

    # New Pricing Model (Market-Competitive)
    setup_fee = db.Column(db.Float, default=0.0)  # Eliminated for all tiers
    monthly_fee = db.Column(db.Float, default=0.0)  # Free: 0, Business: 50k, Enterprise: 150k
    annual_fee = db.Column(db.Float, default=0.0)  # Annual discount option
    transaction_fee_percentage = db.Column(db.Float, default=2.5)  # Free: 2.5%, Business: 2.0%, Enterprise: 1.5%
    fixed_transaction_fee = db.Column(db.Float, default=0.0)  # No fixed fees

    # Freemium Model Limits
    max_transactions_per_month = db.Column(db.Integer, default=0)  # Free: 100, Business/Enterprise: unlimited
    max_transaction_amount = db.Column(db.Float, default=10000000.0)  # TSh 10M max per transaction
    free_transaction_limit = db.Column(db.Integer, default=0)  # Free transactions before fees apply
    
    # Features
    custom_branding = db.Column(db.Boolean, default=False)
    api_access = db.Column(db.Boolean, default=False)
    priority_support = db.Column(db.Boolean, default=False)
    analytics_dashboard = db.Column(db.Boolean, default=False)
    white_label = db.Column(db.Boolean, default=False)
    multi_location = db.Column(db.Boolean, default=False)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    @property
    def features(self):
        return {
            'Custom Branding': self.custom_branding,
            'API Access': self.api_access,
            'Priority Support': self.priority_support,
            'Analytics Dashboard': self.analytics_dashboard,
            'White Label': self.white_label,
            'Multi Location': self.multi_location,
        }

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    pricing_tier_id = db.Column(db.Integer, db.ForeignKey('pricing_tier.id'), nullable=False)
    
    # Subscription details
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='Active')  # Active, Suspended, Cancelled, Expired
    billing_cycle = db.Column(db.String(20), default='Monthly')
    
    # Billing
    next_billing_date = db.Column(db.DateTime)
    last_billing_date = db.Column(db.DateTime)
    total_amount_billed = db.Column(db.Float, default=0.0)
    
    # Relationships
    client_company = db.relationship('ClientCompany')
    pricing_tier = db.relationship('PricingTier')

class PosProduct(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    
    # Product details
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100))
    sku = db.Column(db.String(50))
    
    # Inventory
    stock_quantity = db.Column(db.Integer, default=0)
    track_inventory = db.Column(db.Boolean, default=True)
    low_stock_alert = db.Column(db.Integer, default=5)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = db.relationship('ClientCompany', backref='pos_products')

class PosSale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    sale_number = db.Column(db.String(50), unique=True, nullable=False)
    
    # Customer details
    customer_name = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    customer_email = db.Column(db.String(100))
    
    # Sale details
    subtotal = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    
    # Payment
    payment_method = db.Column(db.String(50))  # Cash, Mobile Money, Card
    payment_status = db.Column(db.String(20), default='Completed')  # Completed, Pending, Refunded
    
    # Cash handling (for cash payments only)
    amount_received = db.Column(db.Float)  # Amount customer paid
    change_given = db.Column(db.Float)  # Change returned
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    company = db.relationship('ClientCompany', backref='pos_sales')
    creator = db.relationship('User')

# Cash Drawer Management
class CashDrawerSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    
    # Session details
    session_number = db.Column(db.String(50), unique=True, nullable=False)
    opening_balance = db.Column(db.Float, nullable=False, default=0.0)
    closing_balance = db.Column(db.Float)
    expected_balance = db.Column(db.Float)
    variance = db.Column(db.Float)  # Difference between expected and actual
    
    # Session status
    status = db.Column(db.String(20), default='Open')  # Open, Closed
    
    # Timestamps
    opened_at = db.Column(db.DateTime, default=datetime.utcnow)
    closed_at = db.Column(db.DateTime)
    opened_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    closed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    company = db.relationship('ClientCompany', backref='cash_drawer_sessions')
    opener = db.relationship('User', foreign_keys=[opened_by])
    closer = db.relationship('User', foreign_keys=[closed_by])

class CashDrawerTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('cash_drawer_session.id'), nullable=False)
    
    # Transaction details
    transaction_type = db.Column(db.String(20), nullable=False)  # Sale, Drawing, Drop, Float
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.String(200))
    reference_id = db.Column(db.Integer)  # Links to sale_id if transaction_type is 'Sale'
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    session = db.relationship('CashDrawerSession', backref='transactions')
    creator = db.relationship('User')

class PosSaleItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('pos_sale.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('pos_product.id'), nullable=False)
    
    # Item details
    product_name = db.Column(db.String(200), nullable=False)  # Store name at time of sale
    unit_price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    # Relationships
    sale = db.relationship('PosSale', backref='sale_items')
    product = db.relationship('PosProduct')

class Bill(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    bill_number = db.Column(db.String(50), unique=True, nullable=False)
    client_company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    
    # Bill details
    bill_type = db.Column(db.String(20), nullable=False)  # Setup, Monthly, Annual, Transaction
    billing_period_start = db.Column(db.DateTime)
    billing_period_end = db.Column(db.DateTime)
    
    # Amounts
    base_amount = db.Column(db.Float, default=0.0)
    transaction_fees = db.Column(db.Float, default=0.0)
    setup_fees = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    
    # Payment tracking
    status = db.Column(db.String(20), default='Pending')  # Pending, Paid, Overdue, Cancelled
    due_date = db.Column(db.DateTime)
    paid_date = db.Column(db.DateTime)
    payment_method = db.Column(db.String(50))
    payment_reference = db.Column(db.String(100))
    
    # Transaction count for billing period
    transaction_count = db.Column(db.Integer, default=0)
    transaction_volume = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    client_company = db.relationship('ClientCompany')
    subscription = db.relationship('Subscription')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def generate_invoice_number():
    """Generate a unique invoice number"""
    while True:
        # Format: INV-YYYY-XXXXXX (INV-2024-001234)
        year = datetime.now().year
        random_part = ''.join(random.choices(string.digits, k=6))
        invoice_number = f"INV-{year}-{random_part}"
        
        # Check if this number already exists
        if not Invoice.query.filter_by(invoice_number=invoice_number).first():
            return invoice_number

def generate_bill_number():
    """Generate a unique bill number"""
    while True:
        # Format: BILL-YYYY-XXXXXX (BILL-2024-001234)
        year = datetime.now().year
        random_part = ''.join(random.choices(string.digits, k=6))
        bill_number = f"BILL-{year}-{random_part}"
        
        # Check if this number already exists
        if not Bill.query.filter_by(bill_number=bill_number).first():
            return bill_number

def generate_sale_number():
    """Generate a unique sale number for POS transactions"""
    while True:
        # Format: SALE-YYYY-XXXXXX (SALE-2024-001234)
        year = datetime.now().year
        random_part = ''.join(random.choices(string.digits, k=6))
        sale_number = f"SALE-{year}-{random_part}"
        
        # Check if this number already exists
        if not PosSale.query.filter_by(sale_number=sale_number).first():
            return sale_number

def generate_session_number():
    """Generate a unique session number for cash drawer sessions"""
    while True:
        # Format: SESSION-YYYY-XXXXXX (SESSION-2024-001234)
        year = datetime.now().year
        random_part = ''.join(random.choices(string.digits, k=6))
        session_number = f"SESSION-{year}-{random_part}"
        
        # Check if this number already exists
        if not CashDrawerSession.query.filter_by(session_number=session_number).first():
            return session_number

def validate_cash_drawer_session(company_id, user_id):
    """Validate cash drawer session for operations"""
    current_session = CashDrawerSession.query.filter_by(
        company_id=company_id,
        status='Open'
    ).first()

    if not current_session:
        raise ValueError("No active cash drawer session. Please open the cash drawer first.")

    # Validate session hasn't been open too long (24 hours max)
    if current_session.opened_at:
        hours_open = (datetime.utcnow() - current_session.opened_at).total_seconds() / 3600
        if hours_open > 24:
            raise ValueError("Cash drawer session has been open too long. Please close and reopen.")

    return current_session

def validate_cash_transaction(session_id, transaction_type, amount, user_id):
    """Validate cash drawer transaction"""
    if amount <= 0:
        raise ValueError("Transaction amount must be positive")

    if transaction_type not in ['Sale', 'Refund', 'Cash In', 'Cash Out', 'Opening Balance']:
        raise ValueError("Invalid transaction type")

    # Check for reasonable transaction limits
    if amount > 1000000:  # 1M TZS limit
        raise ValueError("Transaction amount exceeds maximum limit (TZS 1,000,000)")

    return True

def calculate_transaction_fee(amount, client_company):
    """Calculate transaction fee based on pricing tier"""
    if not client_company or not client_company.pricing_tier:
        return 0.0
    
    tier = client_company.pricing_tier
    percentage_fee = (amount * tier.transaction_fee_percentage) / 100
    total_fee = percentage_fee + tier.fixed_transaction_fee
    
    return round(total_fee, 2)

def get_default_company_settings():
    """Get default Exlipa company settings"""
    return {
        'company_name': 'Exlipa Payment Solutions Ltd',
        'company_address': 'P.O. Box 12345\nDar es Salaam, Tanzania',
        'company_phone': '+*********** 789',
        'company_email': '<EMAIL>',
        'company_website': 'www.exlipa.co.tz',
        'company_tin': '***********',
        'mpesa_till': '123456',
        'tigo_paybill': '654321',
        'airtel_merchant': '789012',
        'crdb_merchant': '890123',
        'primary_color': '#28a745',
        'secondary_color': '#007bff',
        'currency_code': 'TZS',
        'currency_symbol': 'TZS'
    }

def get_company_settings(company_id=None):
    """Get company settings - either specific company or default Exlipa"""
    if company_id:
        company = ClientCompany.query.get(company_id)
        if company:
            return company

    # Return default Exlipa settings
    return type('DefaultSettings', (), get_default_company_settings())()

def calculate_transaction_fee(company, amount):
    """Calculate transaction fee based on company's pricing tier"""
    if not company or not company.pricing_tier_id:
        return amount * 0.025  # Default 2.5% for unassigned companies

    pricing_tier = PricingTier.query.get(company.pricing_tier_id)
    if not pricing_tier:
        return amount * 0.025  # Default 2.5%

    # Check if company has free transactions remaining (Free tier only)
    if pricing_tier.name == 'Free' and company.free_transactions_used < pricing_tier.free_transaction_limit:
        return 0.0  # No fee for free transactions

    # Calculate percentage-based fee
    fee_percentage = pricing_tier.transaction_fee_percentage / 100.0
    return amount * fee_percentage

def track_transaction_usage(company, amount):
    """Track transaction usage for freemium model"""
    if not company:
        return

    # Reset monthly counters if needed
    now = datetime.utcnow()
    if company.last_transaction_reset and (now - company.last_transaction_reset).days >= 30:
        company.monthly_transaction_count = 0
        company.free_transactions_used = 0
        company.last_transaction_reset = now

    # Increment counters
    company.monthly_transaction_count += 1
    company.total_transaction_count += 1

    # Track free transactions for Free tier
    pricing_tier = PricingTier.query.get(company.pricing_tier_id) if company.pricing_tier_id else None
    if pricing_tier and pricing_tier.name == 'Free' and company.free_transactions_used < pricing_tier.free_transaction_limit:
        company.free_transactions_used += 1

    db.session.commit()

def get_tier_limits_status(company):
    """Get current usage status against tier limits"""
    if not company or not company.pricing_tier_id:
        return {
            'tier_name': 'Unassigned',
            'transactions_used': 0,
            'transactions_limit': 0,
            'free_transactions_remaining': 0,
            'can_process_transaction': True,
            'should_show_upgrade': False
        }

    pricing_tier = PricingTier.query.get(company.pricing_tier_id)
    if not pricing_tier:
        return {'tier_name': 'Unknown', 'can_process_transaction': False, 'should_show_upgrade': False}

    free_remaining = max(0, pricing_tier.free_transaction_limit - company.free_transactions_used) if pricing_tier.name == 'Free' else 0

    # Determine if upgrade prompt should be shown
    should_show_upgrade = False
    if pricing_tier.name == 'Free':
        # Show upgrade if less than 20 free transactions remaining
        should_show_upgrade = free_remaining <= 20
    elif pricing_tier.name == 'Business':
        # Show upgrade to Enterprise if high usage
        should_show_upgrade = company.monthly_transaction_count > 5000

    return {
        'tier_name': pricing_tier.name,
        'transactions_used': company.monthly_transaction_count,
        'transactions_limit': pricing_tier.max_transactions_per_month,
        'free_transactions_remaining': free_remaining,
        'can_process_transaction': True,  # All tiers can process (fees may apply)
        'transaction_fee_percentage': pricing_tier.transaction_fee_percentage,
        'should_show_upgrade': should_show_upgrade,
        'next_tier': get_next_tier(pricing_tier.name)
    }

def get_next_tier(current_tier_name):
    """Get the next tier for upgrade suggestions"""
    if current_tier_name == 'Free':
        return 'Business'
    elif current_tier_name == 'Business':
        return 'Enterprise'
    else:
        return None

def get_translation(key, language=None):
    """Get translation for a given key"""
    if language is None:
        language = get_current_language()

    translations = {
        'en': {
            'dashboard': 'Dashboard',
            'analytics': 'Analytics',
            'payments': 'Payments',
            'invoices': 'Invoices',
            'settings': 'Settings',
            'logout': 'Logout',
            'login': 'Login',
            'register': 'Register',
            'save': 'Save',
            'cancel': 'Cancel',
            'delete': 'Delete',
            'edit': 'Edit',
            'view': 'View',
            'create': 'Create',
            'update': 'Update',
            'search': 'Search',
            'filter': 'Filter',
            'export': 'Export',
            'import': 'Import',
            'total': 'Total',
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'rejected': 'Rejected',
            'active': 'Active',
            'inactive': 'Inactive',
            'enabled': 'Enabled',
            'disabled': 'Disabled'
        },
        'sw': {
            # Navigation & Common
            'dashboard': 'Dashibodi',
            'analytics': 'Uchambuzi',
            'payments': 'Malipo',
            'invoices': 'Ankara',
            'settings': 'Mipangilio',
            'logout': 'Toka',
            'login': 'Ingia',
            'register': 'Jisajili',
            'save': 'Hifadhi',
            'cancel': 'Ghairi',
            'delete': 'Futa',
            'edit': 'Hariri',
            'view': 'Ona',
            'create': 'Unda',
            'update': 'Sasisha',
            'search': 'Tafuta',
            'filter': 'Chuja',
            'export': 'Hamisha',
            'import': 'Leta',
            'total': 'Jumla',
            'pending': 'Inasubiri',
            'confirmed': 'Imehakikiwa',
            'rejected': 'Imekataliwa',
            'active': 'Inatumika',
            'inactive': 'Haitumiki',
            'enabled': 'Imewashwa',
            'disabled': 'Imezimwa',

            # Admin & User Roles
            'Master Admin': 'Msimamizi Mkuu',
            'User Admin': 'Msimamizi wa Mtumiaji',
            'Super Admin': 'Msimamizi Mkuu',
            'Company User': 'Mtumiaji wa Kampuni',
            'Admin Dashboard': 'Dashibodi ya Msimamizi',
            'User Dashboard': 'Dashibodi ya Mtumiaji',
            'Master Admin Dashboard': 'Dashibodi ya Msimamizi Mkuu',
            'User Admin Dashboard': 'Dashibodi ya Msimamizi wa Mtumiaji',
            'Admin Login': 'Kuingia kwa Msimamizi',
            'User Login': 'Kuingia kwa Mtumiaji',
            'Company Dashboard': 'Dashibodi ya Kampuni',
            'Welcome': 'Karibu',
            'Welcome back': 'Karibu tena',
            'Role': 'Jukumu',
            'Administrative Access': 'Ufikiaji wa Kiutawala',
            'No company assigned': 'Hakuna kampuni iliyopangiwa',

            # Payment System
            'Payment Gateway': 'Lango la Malipo',
            'Payment Confirmation': 'Uthibitisho wa Malipo',
            'Payment Status': 'Hali ya Malipo',
            'Payment Method': 'Njia ya Malipo',
            'Payment Methods': 'Njia za Malipo',
            'Mobile Money': 'Pesa za Simu',
            'Bank Transfer': 'Uhamisho wa Benki',
            'M-Pesa': 'M-Pesa',
            'Tigo Pesa': 'Tigo Pesa',
            'Airtel Money': 'Airtel Money',
            'CRDB Lipa': 'CRDB Lipa',
            'Amount': 'Kiasi',
            'Total Amount': 'Jumla ya Kiasi',
            'Transaction ID': 'Nambari ya Muamala',
            'Reference Number': 'Nambari ya Kumbuka',
            'Customer Name': 'Jina la Mteja',
            'Customer Email': 'Barua Pepe ya Mteja',
            'Pay Now': 'Lipa Sasa',
            'Make Payment': 'Fanya Malipo',
            'Check Payment Status': 'Angalia Hali ya Malipo',

            # Company & Business
            'Company': 'Kampuni',
            'Company Name': 'Jina la Kampuni',
            'Company Email': 'Barua Pepe ya Kampuni',
            'Company Phone': 'Simu ya Kampuni',
            'Business Type': 'Aina ya Biashara',
            'Landing Page': 'Ukurasa wa Kufikia',
            'Share': 'Shiriki',
            'Share Your Landing Page': 'Shiriki Ukurasa Wako wa Kufikia',
            'Copy URL': 'Nakili URL',
            'Preview': 'Onyesho la Awali',
            'Templates': 'Mifano',
            'Basic': 'Msingi',
            'Modern': 'Kisasa',
            'Minimal': 'Kidogo',
            'Professional': 'Kitaalamu',
            'Luxury': 'Anasa',
            'My Invoices': 'Ankara Zangu',
            'My Payments': 'Malipo Yangu',
            'Edit Profile': 'Hariri Wasifu',
            'User Administrator': 'Msimamizi wa Mtumiaji',
            'Go Back': 'Rudi',
            'The page you are looking for does not exist': 'Ukurasa unaoutafuta haupo',
            'Go back to homepage': 'Rudi kwenye ukurasa wa nyumbani',
            'Total Invoices': 'Ankara Zote',
            'Unpaid Invoices': 'Ankara Ambazo Hazijalipiwa',
            'Pending Payments': 'Malipo Yanayosubiri',
            'Today': 'Leo',
            'Companies': 'Makampuni',
            'Welcome': 'Karibu',
            'Total Companies': 'Makampuni Yote',
            'Active': 'Hai',
            'Suspended': 'Imesimamishwa',
            'Cancelled': 'Imeghairiwa',
            'Name': 'Jina',
            'Description': 'Maelezo',
            'Features': 'Vipengele',
            'Status': 'Hali',
            'Company Dashboard': 'Dashibodi ya Kampuni',
            'Master Admin': 'Msimamizi Mkuu',
            'Login': 'Ingia',
            'Logout': 'Toka',
            'Landing Page': 'Ukurasa wa Kuapisha',
            'Invoices': 'Ankara',
            'Payments': 'Malipo',
            'Password': 'Nywila',

            # Subscription & Pricing
            'Subscription': 'Usajili',
            'Package': 'Kifurushi',
            'Pricing': 'Bei',
            'Plan': 'Mpango',
            'Tier': 'Kiwango',
            'Starter': 'Mwanzo',
            'Business': 'Biashara',
            'Enterprise': 'Makampuni',
            'Free': 'Bure',
            'Monthly Fee': 'Ada ya Kila Mwezi',
            'Setup Fee': 'Ada ya Kuanzisha',
            'Transaction Fee': 'Ada ya Muamala',
            'Features': 'Vipengele',
            'Upgrade': 'Boresha',
            'Downgrade': 'Shuka',

            # Forms & Fields
            'Name': 'Jina',
            'Email': 'Barua Pepe',
            'Phone': 'Simu',
            'Password': 'Nenosiri',
            'Confirm Password': 'Thibitisha Nenosiri',
            'Address': 'Anwani',
            'Description': 'Maelezo',
            'Message': 'Ujumbe',
            'Subject': 'Mada',
            'Date': 'Tarehe',
            'Time': 'Muda',
            'Status': 'Hali',
            'Type': 'Aina',
            'Category': 'Jamii',

            # Actions & Buttons
            'Submit': 'Wasilisha',
            'Send': 'Tuma',
            'Back': 'Rudi',
            'Back to Dashboard': 'Rudi kwenye Dashibodi',
            'Next': 'Ifuatayo',
            'Previous': 'Iliyotangulia',
            'Continue': 'Endelea',
            'Finish': 'Maliza',
            'Close': 'Funga',
            'Open': 'Fungua',
            'Download': 'Pakua',
            'Upload': 'Pakia',
            'Print': 'Chapisha',
            'Copy': 'Nakili',
            'Refresh': 'Onyesha Upya',
            'Reset': 'Weka Upya',
            'Clear': 'Futa',
            'Select': 'Chagua',
            'Choose': 'Chagua',
            'Browse': 'Vinjari',

            # Status & States
            'Success': 'Mafanikio',
            'Error': 'Hitilafu',
            'Warning': 'Onyo',
            'Info': 'Taarifa',
            'Loading': 'Inapakia',
            'Processing': 'Inachakata',
            'Complete': 'Kamili',
            'Incomplete': 'Haijamalizika',
            'Failed': 'Imeshindwa',
            'Cancelled': 'Imeghairiwa',
            'Expired': 'Imeisha',
            'Valid': 'Halali',
            'Invalid': 'Si Halali',
            'Available': 'Inapatikana',
            'Unavailable': 'Haipatikani',
            'Online': 'Mtandaoni',
            'Offline': 'Nje ya Mtandao',

            # Flash Messages & Notifications
            'Payment confirmation submitted successfully! We will verify and process your payment shortly.': 'Uthibitisho wa malipo umewasilishwa kwa mafanikio! Tutahakiki na kuchakata malipo yako hivi karibuni.',
            'Payment confirmed! Registration link sent to': 'Malipo yamehakikiwa! Kiungo cha usajili kimetumwa kwa',
            'Payment rejected!': 'Malipo yamekataliwa!',
            'Registration link sent to': 'Kiungo cha usajili kimetumwa kwa',
            'No email address found for this payment. Please send the registration link manually.': 'Hakuna anwani ya barua pepe iliyopatikana kwa malipo haya. Tafadhali tuma kiungo cha usajili kwa mikono.',
            'Payment confirmed! Registration link ready. Email sending disabled for testing.': 'Malipo yamehakikiwa! Kiungo cha usajili kiko tayari. Kutuma barua pepe kumezimwa kwa majaribio.',
            'Company profile updated successfully!': 'Wasifu wa kampuni umesasishwa kwa mafanikio!',
            'No company associated with your account.': 'Hakuna kampuni inayohusiana na akaunti yako.',
            'Invalid credentials': 'Taarifa za kuingia si sahihi',
            'Login successful': 'Kuingia kumefanikiwa',
            'Logout successful': 'Kutoka kumefanikiwa',
            'Access denied': 'Ufikiaji umekataliwa',
            'Permission denied': 'Ruhusa imekataliwa',
            'Session expired': 'Kipindi kimeisha',
            'Invalid request': 'Ombi si halali',
            'Operation successful': 'Operesheni imefanikiwa',
            'Operation failed': 'Operesheni imeshindwa',
            'Data saved successfully': 'Data imehifadhiwa kwa mafanikio',
            'Data updated successfully': 'Data imesasishwa kwa mafanikio',
            'Data deleted successfully': 'Data imefutwa kwa mafanikio',
            'File uploaded successfully': 'Faili imepakiwa kwa mafanikio',
            'File download failed': 'Kupakua faili kumeshindwa',
            'Email sent successfully': 'Barua pepe imetumwa kwa mafanikio',
            'Email sending failed': 'Kutuma barua pepe kumeshindwa',
            'Invalid email address': 'Anwani ya barua pepe si halali',
            'Invalid phone number': 'Nambari ya simu si halali',
            'Password too weak': 'Nenosiri ni dhaifu sana',
            'Passwords do not match': 'Maneno ya siri hayalingani',
            'Account created successfully': 'Akaunti imeundwa kwa mafanikio',
            'Account updated successfully': 'Akaunti imesasishwa kwa mafanikio',
            'Account deleted successfully': 'Akaunti imefutwa kwa mafanikio',
            'Profile updated successfully': 'Wasifu umesasishwa kwa mafanikio',
            'Settings saved successfully': 'Mipangilio imehifadhiwa kwa mafanikio',
            'Configuration updated successfully': 'Usanidi umesasishwa kwa mafanikio',
            'System error occurred': 'Hitilafu ya mfumo imetokea',
            'Database error': 'Hitilafu ya hifadhidata',
            'Network error': 'Hitilafu ya mtandao',
            'Connection failed': 'Muunganisho umeshindwa',
            'Timeout error': 'Hitilafu ya muda',
            'Service unavailable': 'Huduma haipatikani',
            'Maintenance mode': 'Hali ya matengenezo',
            'Feature not available': 'Kipengele hakipatikani',
            'Coming soon': 'Kinakuja hivi karibuni',
            'Under development': 'Chini ya maendeleo',
            'Beta version': 'Toleo la beta',
            'Trial expired': 'Jaribio limeisha',
            'Subscription required': 'Usajili unahitajika',
            'Upgrade required': 'Kuboresha kunahitajika',
            'Payment required': 'Malipo yanahitajika',
            'Insufficient funds': 'Fedha hazitoshi',
            'Transaction failed': 'Muamala umeshindwa',
            'Transaction successful': 'Muamala umefanikiwa',
            'Transaction pending': 'Muamala unasubiri',
            'Transaction cancelled': 'Muamala umeghairiwa',
            'Refund processed': 'Rejesho limechakatwa',
            'Refund failed': 'Rejesho limeshindwa',
            'Invoice created': 'Ankara imeundwa',
            'Invoice sent': 'Ankara imetumwa',
            'Invoice paid': 'Ankara imelipwa',
            'Invoice overdue': 'Ankara imechelewa',
            'Invoice cancelled': 'Ankara imeghairiwa',
            'Receipt generated': 'Risiti imetengenezwa',
            'Report generated': 'Ripoti imetengenezwa',
            'Export completed': 'Uhamishaji umekamilika',
            'Import completed': 'Uingizaji umekamilika',
            'Backup created': 'Nakala ya hifadhi imeundwa',
            'Backup restored': 'Nakala ya hifadhi imerejelewa',
            'Cache cleared': 'Hifadhi ya muda imefutwa',
            'System restarted': 'Mfumo umeanzishwa upya',
            'Update available': 'Sasisha linapatikana',
            'Update installed': 'Sasisha limesakinishwa',
            'Security alert': 'Tahadhari ya usalama',
            'Suspicious activity detected': 'Shughuli za kutilia shaka zimegunduliwa',
            'Account locked': 'Akaunti imefungwa',
            'Account unlocked': 'Akaunti imefunguliwa',
            'Password changed': 'Nenosiri limebadilishwa',
            'Password reset': 'Nenosiri limewekwa upya',
            'Two-factor authentication enabled': 'Uthibitisho wa hatua mbili umewashwa',
            'Two-factor authentication disabled': 'Uthibitisho wa hatua mbili umezimwa',
            'API key generated': 'Ufunguo wa API umetengenezwa',
            'API key revoked': 'Ufunguo wa API umebatilishwa',
            'Webhook configured': 'Webhook imesanidiwa',
            'Integration successful': 'Muunganisho umefanikiwa',
            'Integration failed': 'Muunganisho umeshindwa',
            'Test successful': 'Jaribio limefanikiwa',
            'Test failed': 'Jaribio limeshindwa',
            'Validation error': 'Hitilafu ya uthibitisho',
            'Required field missing': 'Sehemu inayohitajika haipo',
            'Invalid format': 'Muundo si halali',
            'Value out of range': 'Thamani nje ya mipaka',
            'Duplicate entry': 'Ingizo la nakala',
            'Record not found': 'Rekodi haijapatikana',
            'Record exists': 'Rekodi ipo',
            'Record created': 'Rekodi imeundwa',
            'Record updated': 'Rekodi imesasishwa',
            'Record deleted': 'Rekodi imefutwa',
            'Records imported': 'Rekodi zimeingizwa',
            'Records exported': 'Rekodi zimehamishwa',
            'Search completed': 'Utafutaji umekamilika',
            'No results found': 'Hakuna matokeo yaliyopatikana',
            'Filter applied': 'Kichujio kimetumika',
            'Filter removed': 'Kichujio kimeondolewa',
            'Sort applied': 'Upangaji umetumika',
            'View changed': 'Mwonekano umebadilishwa',
            'Page not found': 'Ukurasa haujapatikana',
            'Access forbidden': 'Ufikiaji umekatazwa',
            'Server error': 'Hitilafu ya seva',
            'Bad request': 'Ombi baya',
            'Unauthorized': 'Hauruhusiwi',
            'Forbidden': 'Imekatazwa',
            'Not found': 'Haijapatikana',
            'Method not allowed': 'Njia hairuhusiwi',
            'Conflict': 'Mgongano',
            'Gone': 'Imeondoka',
            'Too many requests': 'Maombi mengi sana',
            'Internal server error': 'Hitilafu ya ndani ya seva',
            'Service unavailable': 'Huduma haipatikani',
            'Gateway timeout': 'Muda wa lango umeisha',

            # Email Content
            'Welcome to Exlipa!': 'Karibu EXLIPA!',
            'The Exlipa Team': 'Timu ya EXLIPA',
            'Registration Link': 'Kiungo cha Usajili',
            'Your registration link': 'Kiungo chako cha usajili',
            'Click here to complete registration': 'Bofya hapa kukamilisha usajili',
            'Payment Confirmation': 'Uthibitisho wa Malipo',
            'Your payment has been confirmed': 'Malipo yako yamehakikiwa',
            'Thank you for your payment': 'Asante kwa malipo yako',
            'Invoice': 'Ankara',
            'Receipt': 'Risiti',
            'Transaction Details': 'Maelezo ya Muamala',
            'Customer Support': 'Msaada wa Wateja',
            'Contact us': 'Wasiliana nasi',
            'Best regards': 'Heshima nyingi',
            'Sincerely': 'Kwa dhati',

            # Error Pages
            'Page Not Found': 'Ukurasa Haujapatikana',
            'The page you are looking for does not exist': 'Ukurasa unaoutafuta haupo',
            'Go back to homepage': 'Rudi kwenye ukurasa wa nyumbani',
            'Internal Server Error': 'Hitilafu ya Ndani ya Seva',
            'Something went wrong': 'Kuna kitu kimekwenda vibaya',
            'Please try again later': 'Tafadhali jaribu tena baadaye',
            'Contact support if the problem persists': 'Wasiliana na msaada ikiwa tatizo linaendelea',
            'Access Denied': 'Ufikiaji Umekataliwa',
            'You do not have permission to access this page': 'Huna ruhusa ya kufikia ukurasa huu',
            'Please login to continue': 'Tafadhali ingia ili kuendelea',
            'Rate Limit Exceeded': 'Kikomo cha Kiwango Kimezidishwa',
            'Too many requests': 'Maombi mengi sana',
            'Please wait before trying again': 'Tafadhali subiri kabla ya kujaribu tena',

            'Dashboard': 'Dashibodi',
            'Admin': 'Msimamizi',
            'Master': 'Mkuu',
            'User': 'Mtumiaji',
            'Payment': 'Malipo',
            'Customer': 'Mteja',
            'Billing': 'Malipo',
            'Confirm': 'Thibitisha',
            'Cancel': 'Ghairi',
            'Edit': 'Hariri',
            'Delete': 'Futa',
            'View': 'Ona',
            'Manage': 'Simamia',
            'Create': 'Unda',
            'Add': 'Ongeza',
            'Update': 'Sasisha',
            'Save': 'Hifadhi',
            'Inactive': 'Haifanyi Kazi',
            'Pending': 'Inasubiri',
            'Confirmed': 'Imethibitishwa',
            'Rejected': 'Imekataliwa',
            'Approved': 'Imeidhinishwa',
            'Locked': 'Imefungwa',
            'Actions': 'Vitendo',
            'Settings': 'Mipangilio',
            'Profile': 'Wasifu',
            'Username': 'Jina la Mtumiaji',
            'Search': 'Tafuta',
            'Filter': 'Chuja',
            'Sort': 'Panga',
            'Export': 'Hamisha',
            'Import': 'Leta',
            'Team': 'Timu',
            'Members': 'Wanachama',
            'Users': 'Watumiaji',
            'Organizations': 'Mashirika',
            'Analytics': 'Uchambuzi',
            'Reports': 'Ripoti',
            'Statistics': 'Takwimu',
            'Metrics': 'Vipimo',
            'Performance': 'Utendaji',
            'Help': 'Msaada',
            'Support': 'Msaada',
            'Contact': 'Wasiliana',
            'About': 'Kuhusu',
            'FAQ': 'Maswali Yanayoulizwa Mara kwa Mara',
            'Documentation': 'Nyaraka',
            'Remember': 'Kumbuka',
            'Forgot': 'Umesahau',
            'Change': 'Badilisha',
            'Notification': 'Arifa',
            'Home': 'Nyumbani',
            'Total': 'Jumla',
            'Count': 'Idadi',
            'Number': 'Nambari',
            'Quantity': 'Wingi',
            'Price': 'Bei',
            'Cost': 'Gharama',
            'Fee': 'Ada',
            'Charge': 'Malipo',
            'Yesterday': 'Jana',
            'Tomorrow': 'Kesho',
            'Week': 'Wiki',
            'Month': 'Mwezi',
            'Year': 'Mwaka',
            'All': 'Yote',
            'None': 'Hakuna',
            'Any': 'Yoyote',
            'Some': 'Baadhi',
            'Every': 'Kila',
            'Each': 'Kila',
            'Other': 'Nyingine',
            'More': 'Zaidi',
            'Less': 'Kidogo',
            'System': 'Mfumo',
            'Service': 'Huduma',
            'Product': 'Bidhaa',
            'Order': 'Agizo',
            'Transaction': 'Muamala',
            'Reference': 'Rejea',
            'Code': 'Msimbo',
            'ID': 'Kitambulisho',
            'Method': 'Njia',
            'Group': 'Kundi',
            'List': 'Orodha',
            'Table': 'Jedwali',
            'Form': 'Fomu',
            'Field': 'Uga',
            'Required': 'Inahitajika',
            'Optional': 'Si Lazima',

            # Additional translations for recent template fixes
            'Ready to upgrade?': 'Tayari kuboresha?',
            'Upgrade Now': 'Boresha Sasa',
            'Current Plan': 'Mpango wa Sasa',
            'Upgrade Plan': 'Boresha Mpango',
            'View Analytics': 'Ona Takwimu',
            'free transactions remaining': 'miamala ya bure imebaki',
            'Transaction fee after free limit': 'Ada ya muamala baada ya kikomo cha bure',
            'No invoices found': 'Hakuna ankara zilizopatikana',
            'No payments found': 'Hakuna malipo yaliyopatikana',
            'Back to Dashboard': 'Rudi kwenye Dashibodi',
            'Payment Confirmation Submitted!': 'Uthibitisho wa Malipo Umetumwa!',
            'Your Reference Number': 'Namba Yako ya Kumbukumbu',
            'Thank you! Your payment confirmation has been received successfully.': 'Asante! Uthibitisho wako wa malipo umepokelewa kwa ufanisi.',
            'What\'s Next?': 'Ni Nini Kifuatacho?',
            'Our team will verify your payment and update you shortly.': 'Timu yetu itathibitisha malipo yako na kukutaarifu hivi karibuni.',
            'Verification Time': 'Muda wa Uthibitisho',
            'Usually takes 2-4 hours during business hours.': 'Kwa kawaida inachukua masaa 2-4 wakati wa masaa ya kazi.',
            'Check Payment Status': 'Angalia Hali ya Malipo',
            'Track your payment and get your registration link': 'Fuatilia malipo yako na upate kiungo chako cha usajili',
            'Need help?': 'Unahitaji msaada?',
            'Reference ID (Optional)': 'Kitambulisho cha Kumbukumbu (Si lazima)',
            'The reference ID you received after submitting your payment confirmation': 'Kitambulisho cha kumbukumbu ulichopokea baada ya kutuma uthibitisho wa malipo yako',
            'Payment Details': 'Maelezo ya Malipo',
            'Back to List': 'Rudi kwenye Orodha',
            'Generate Receipt': 'Tengeneza Risiti',
            'How to Pay Us via Mobile Money': 'Jinsi ya Kutulipa kwa Pesa za Simu',
            'Invoice Details': 'Maelezo ya Ankara',
            'Invoice Number': 'Namba ya Ankara',
            'Amount to Pay': 'Kiasi cha Kulipa',
            'Check Another Payment': 'Angalia Malipo Mengine',
            'Payment Confirmed': 'Malipo Yamethibitishwa',
            'Payment Pending Review': 'Malipo Yanasubiri Ukaguzi',
            'Payment Rejected': 'Malipo Yamekataliwa',
            'Payment Processed': 'Malipo Yamechakatwa',
            'Reference ID': 'Kitambulisho cha Kumbukumbu',

            # Contact page translations
            'Contact Us': 'Wasiliana Nasi',
            'We\'re here to help. Get in touch with us through any convenient way.': 'Tuko hapa kukusaidia. Wasiliana nasi kwa njia yoyote inayokufaa.',
            'Phone Support': 'Msaada wa Simu',
            'Call us for immediate assistance': 'Piga simu kwa msaada wa haraka',
            'Monday - Friday: 8:00 AM - 6:00 PM': 'Jumatatu - Ijumaa: 8:00 - 18:00',
            'Emergency (24/7)': 'Dharura (24/7)',
            'Email Support': 'Msaada wa Barua Pepe',
            'Send us an email and we\'ll respond quickly': 'Tuma barua pepe na tutajibu haraka',
            'General Support': 'Msaada wa Jumla',
            'Business Partnerships': 'Ushirikiano wa Biashara',
            'Technical Support': 'Msaada wa Kiufundi',
            'Chat with us on WhatsApp for quick help': 'Tuongee WhatsApp kwa msaada wa haraka',
            'Chat with Us': 'Ongea Nasi',
            'Head Office': 'Ofisi Kuu',
            'Visit our office for face-to-face assistance': 'Tembelea ofisi yetu kwa msaada wa ana kwa ana',
            'Send us a Message': 'Tuma Ujumbe',
            'Full Name *': 'Jina Kamili *',
            'Email Address *': 'Barua Pepe *',
            'Phone Number': 'Namba ya Simu',
            'Subject *': 'Mada *',
            'Choose subject...': 'Chagua mada...',
            'General Inquiry': 'Maswali ya Jumla',
            'Business Partnership': 'Ushirikiano wa Biashara',
            'Billing Questions': 'Maswali ya Malipo',
            'Message *': 'Ujumbe *',
            'Write your message here...': 'Andika ujumbe wako hapa...',
            'Send Message': 'Tuma Ujumbe',
            'Frequently Asked Questions': 'Maswali Yanayoulizwa Mara Kwa Mara',
            'What is EXLIPA and what does it do?': 'Je, EXLIPA ni nini na inafanya nini?',
            'What are the fees for using EXLIPA services?': 'Ni ada gani za kutumia huduma za EXLIPA?',
            'Which mobile money services are supported?': 'Ni huduma gani za pesa za simu zinazotumika?',
            'How long does it take to receive payments?': 'Ni muda gani wa kupokea malipo?',
            'Please fill in all required fields': 'Tafadhali jaza sehemu zote zinazohitajika',
            'Please enter a valid email address': 'Tafadhali ingiza anwani sahihi ya barua pepe',
            'Thank you for your message! We will get back to you within 24 hours.': 'Asante kwa ujumbe wako! Tutakujibu ndani ya masaa 24.',
            'Sorry, there was an error sending your message. Please try again.': 'Samahani, kulikuwa na hitilafu katika kutuma ujumbe wako. Tafadhali jaribu tena.'
        }
    }

    return translations.get(language, {}).get(key, key)

def get_available_templates(tier_name):
    """Get available landing page templates based on tier"""
    templates = {
        'Free': [
            {'id': 'basic', 'name': 'Basic', 'description': 'Simple and clean design'},
            {'id': 'modern', 'name': 'Modern', 'description': 'Contemporary layout'},
            {'id': 'minimal', 'name': 'Minimal', 'description': 'Clean and minimal'}
        ],
        'Business': [
            {'id': 'basic', 'name': 'Basic', 'description': 'Simple and clean design'},
            {'id': 'modern', 'name': 'Modern', 'description': 'Contemporary layout'},
            {'id': 'minimal', 'name': 'Minimal', 'description': 'Clean and minimal'},
            {'id': 'professional', 'name': 'Professional', 'description': 'Business-focused design'},
            {'id': 'gradient', 'name': 'Gradient', 'description': 'Colorful gradient background'},
            {'id': 'corporate', 'name': 'Corporate', 'description': 'Professional corporate style'},
            {'id': 'ecommerce', 'name': 'E-commerce', 'description': 'Online store optimized'},
            {'id': 'service', 'name': 'Service', 'description': 'Service business focused'}
        ],
        'Enterprise': [
            {'id': 'basic', 'name': 'Basic', 'description': 'Simple and clean design'},
            {'id': 'modern', 'name': 'Modern', 'description': 'Contemporary layout'},
            {'id': 'minimal', 'name': 'Minimal', 'description': 'Clean and minimal'},
            {'id': 'professional', 'name': 'Professional', 'description': 'Business-focused design'},
            {'id': 'gradient', 'name': 'Gradient', 'description': 'Colorful gradient background'},
            {'id': 'corporate', 'name': 'Corporate', 'description': 'Professional corporate style'},
            {'id': 'ecommerce', 'name': 'E-commerce', 'description': 'Online store optimized'},
            {'id': 'service', 'name': 'Service', 'description': 'Service business focused'},
            {'id': 'luxury', 'name': 'Luxury', 'description': 'Premium luxury design'},
            {'id': 'tech', 'name': 'Tech', 'description': 'Technology company style'},
            {'id': 'creative', 'name': 'Creative', 'description': 'Creative agency design'},
            {'id': 'custom', 'name': 'Custom', 'description': 'Fully customizable template'}
        ]
    }
    return templates.get(tier_name, templates['Free'])

def validate_mobile_money_transaction(transaction_id, phone_number, operator):
    """Validate mobile money transaction details"""
    validation_result = {
        'is_valid': True,
        'errors': [],
        'warnings': []
    }

    # Validate transaction ID format
    if not transaction_id or len(transaction_id) < 6:
        validation_result['is_valid'] = False
        validation_result['errors'].append('Transaction ID must be at least 6 characters')

    # Validate phone number format
    import re
    phone_pattern = r'^(\+255|0)[67][0-9]{8}$'
    if phone_number and not re.match(phone_pattern, phone_number):
        validation_result['is_valid'] = False
        validation_result['errors'].append('Invalid Tanzanian mobile number format')

    # Operator-specific validations
    if operator == 'M-Pesa' and transaction_id:
        # M-Pesa transaction IDs typically start with certain patterns
        if not any(transaction_id.upper().startswith(prefix) for prefix in ['QH', 'QI', 'QJ', 'QK']):
            validation_result['warnings'].append('M-Pesa transaction IDs usually start with QH, QI, QJ, or QK')

    elif operator == 'Tigo Pesa' and transaction_id:
        # Tigo Pesa transaction IDs have different patterns
        if not any(transaction_id.upper().startswith(prefix) for prefix in ['TP', 'TG', 'TI']):
            validation_result['warnings'].append('Tigo Pesa transaction IDs usually start with TP, TG, or TI')

    elif operator == 'Airtel Money' and transaction_id:
        # Airtel Money transaction IDs have different patterns
        if not any(transaction_id.upper().startswith(prefix) for prefix in ['AM', 'AR', 'AI']):
            validation_result['warnings'].append('Airtel Money transaction IDs usually start with AM, AR, or AI')

    return validation_result

def generate_api_key():
    """Generate a secure API key"""
    import secrets
    return secrets.token_urlsafe(32)

def generate_api_secret():
    """Generate a secure API secret"""
    import secrets
    return secrets.token_urlsafe(64)

def verify_api_credentials(api_key, api_secret):
    """Verify API credentials and return company if valid"""
    if not api_key or not api_secret:
        return None

    company = ClientCompany.query.filter_by(
        api_key=api_key,
        api_secret=api_secret,
        api_enabled=True
    ).first()

    if company:
        # Update last used timestamp
        company.api_last_used = datetime.now()
        db.session.commit()

        # Check if company has API access based on tier
        if company.pricing_tier_id:
            tier = PricingTier.query.get(company.pricing_tier_id)
            if tier and tier.api_access:
                return company

    return None

def get_company_analytics(company_id, days=30):
    """Get comprehensive analytics for a company"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, and_

    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    # Get payment confirmations for the company
    payments = PaymentConfirmation.query.filter(
        and_(
            PaymentConfirmation.client_company_id == company_id,
            PaymentConfirmation.submitted_at >= start_date,
            PaymentConfirmation.submitted_at <= end_date
        )
    ).all()

    # Calculate metrics
    total_transactions = len(payments)
    confirmed_payments = [p for p in payments if p.status == 'Confirmed']
    pending_payments = [p for p in payments if p.status == 'Pending']

    total_revenue = sum(p.amount for p in confirmed_payments)
    total_fees = sum(p.transaction_fee or 0 for p in confirmed_payments)
    net_revenue = total_revenue - total_fees

    # Payment method breakdown
    payment_methods = {}
    for payment in confirmed_payments:
        method = payment.mobile_operator or 'Unknown'
        if method not in payment_methods:
            payment_methods[method] = {'count': 0, 'amount': 0}
        payment_methods[method]['count'] += 1
        payment_methods[method]['amount'] += payment.amount

    # Daily transaction data for charts
    daily_data = {}
    for payment in confirmed_payments:
        date_key = payment.submitted_at.strftime('%Y-%m-%d')
        if date_key not in daily_data:
            daily_data[date_key] = {'count': 0, 'amount': 0}
        daily_data[date_key]['count'] += 1
        daily_data[date_key]['amount'] += payment.amount

    # Average transaction value
    avg_transaction = total_revenue / len(confirmed_payments) if confirmed_payments else 0

    return {
        'period_days': days,
        'total_transactions': total_transactions,
        'confirmed_transactions': len(confirmed_payments),
        'pending_transactions': len(pending_payments),
        'total_revenue': total_revenue,
        'total_fees': total_fees,
        'net_revenue': net_revenue,
        'average_transaction': avg_transaction,
        'payment_methods': payment_methods,
        'daily_data': daily_data,
        'conversion_rate': (len(confirmed_payments) / total_transactions * 100) if total_transactions > 0 else 0
    }

def initialize_default_pricing_tiers():
    """Initialize new freemium pricing tiers optimized for Tanzania market"""
    if PricingTier.query.count() == 0:
        tiers = [
            PricingTier(
                name='Free',
                description='Perfect for small businesses getting started - 100 free transactions monthly',
                setup_fee=0.0,  # No setup fees
                monthly_fee=0.0,  # Completely free
                annual_fee=0.0,  # Free forever
                transaction_fee_percentage=2.5,  # 2.5% after free limit
                fixed_transaction_fee=0,
                max_transactions_per_month=100,  # 100 free transactions
                max_transaction_amount=10000000,  # TSh 10M max
                free_transaction_limit=100,  # 100 free transactions
                custom_branding=False,
                api_access=False,
                priority_support=False,
                analytics_dashboard=True,
                white_label=False,
                multi_location=False
            ),
            PricingTier(
                name='Business',
                description='For growing businesses - unlimited transactions with professional features',
                setup_fee=0.0,  # No setup fees
                monthly_fee=50000,  # TSh 50,000 (affordable for SMEs)
                annual_fee=500000,  # TSh 500,000 (2 months free)
                transaction_fee_percentage=2.0,  # Reduced to 2.0%
                fixed_transaction_fee=0,
                max_transactions_per_month=0,  # Unlimited
                max_transaction_amount=10000000,  # TSh 10M max
                free_transaction_limit=0,  # No free limit (subscription covers it)
                custom_branding=True,
                api_access=True,
                priority_support=True,
                analytics_dashboard=True,
                white_label=False,
                multi_location=True
            ),
            PricingTier(
                name='Enterprise',
                description='For large enterprises - white-label solution with lowest fees',
                setup_fee=0.0,  # No setup fees
                monthly_fee=150000,  # TSh 150,000 (premium but accessible)
                annual_fee=1500000,  # TSh 1,500,000 (2 months free)
                transaction_fee_percentage=1.5,  # Lowest fees at 1.5%
                fixed_transaction_fee=0,
                max_transactions_per_month=0,  # Unlimited
                max_transaction_amount=0,  # Unlimited
                free_transaction_limit=0,  # No free limit (subscription covers it)
                custom_branding=True,
                api_access=True,
                priority_support=True,
                analytics_dashboard=True,
                white_label=True,
                multi_location=True
            )
        ]
        
        for tier in tiers:
            db.session.add(tier)
        db.session.commit()

def create_monthly_bill(client_company):
    """Generate monthly bill for a client company"""
    if not client_company.pricing_tier:
        return None
    
    # Calculate billing period
    now = datetime.now()
    if client_company.billing_cycle == 'Annual':
        billing_period_start = now.replace(day=1, month=1)
        billing_period_end = now.replace(day=31, month=12)
        base_amount = client_company.pricing_tier.annual_fee
    else:
        billing_period_start = now.replace(day=1)
        next_month = billing_period_start.replace(month=billing_period_start.month + 1) if billing_period_start.month < 12 else billing_period_start.replace(year=billing_period_start.year + 1, month=1)
        billing_period_end = next_month - timedelta(days=1)
        base_amount = client_company.pricing_tier.monthly_fee
    
    # Calculate transaction fees for the period
    transaction_fees = db.session.query(db.func.sum(PaymentConfirmation.transaction_fee)).filter(
        PaymentConfirmation.client_company_id == client_company.id,
        PaymentConfirmation.submitted_at >= billing_period_start,
        PaymentConfirmation.submitted_at <= billing_period_end,
        PaymentConfirmation.status == 'Confirmed'
    ).scalar() or 0.0
    
    # Count transactions
    transaction_count = PaymentConfirmation.query.filter(
        PaymentConfirmation.client_company_id == client_company.id,
        PaymentConfirmation.submitted_at >= billing_period_start,
        PaymentConfirmation.submitted_at <= billing_period_end,
        PaymentConfirmation.status == 'Confirmed'
    ).count()
    
    # Calculate total volume
    transaction_volume = db.session.query(db.func.sum(PaymentConfirmation.amount)).filter(
        PaymentConfirmation.client_company_id == client_company.id,
        PaymentConfirmation.submitted_at >= billing_period_start,
        PaymentConfirmation.submitted_at <= billing_period_end,
        PaymentConfirmation.status == 'Confirmed'
    ).scalar() or 0.0
    
    # Calculate tax (18% VAT in Tanzania)
    tax_amount = (base_amount + transaction_fees) * 0.18
    total_amount = base_amount + transaction_fees + tax_amount
    
    # Create bill
    bill = Bill(
        bill_number=generate_bill_number(),
        client_company_id=client_company.id,
        bill_type='Monthly' if client_company.billing_cycle == 'Monthly' else 'Annual',
        billing_period_start=billing_period_start,
        billing_period_end=billing_period_end,
        base_amount=base_amount,
        transaction_fees=transaction_fees,
        tax_amount=tax_amount,
        total_amount=total_amount,
        transaction_count=transaction_count,
        transaction_volume=transaction_volume,
        due_date=now + timedelta(days=30),  # 30 days to pay
        created_by=1  # System generated
    )
    
    db.session.add(bill)
    db.session.commit()
    
    return bill

# Helper to serialize PricingTier for JSON

def pricing_tier_to_dict(tier):
    return {
        'id': tier.id,
        'name': tier.name,
        'monthly_fee': tier.monthly_fee,
        'annual_fee': tier.annual_fee,
        'setup_fee': tier.setup_fee,
        'transaction_fee_percentage': tier.transaction_fee_percentage,
        'max_transactions_per_month': tier.max_transactions_per_month,
        'description': tier.description,
        'features': {
            'custom_branding': tier.custom_branding,
            'api_access': tier.api_access,
            'priority_support': tier.priority_support,
            'analytics_dashboard': tier.analytics_dashboard,
            'white_label': tier.white_label,
            'multi_location': tier.multi_location
        }
    }

# Routes
@app.route('/set_language/<language>')
def set_language(language=None):
    """Set user language preference"""
    if language in app.config['LANGUAGES']:
        session['language'] = language

        # Save to user profile if logged in
        if current_user.is_authenticated:
            current_user.preferred_language = language
            db.session.commit()

    return redirect(request.referrer or url_for('index'))

@app.context_processor
def inject_language():
    """Inject current language and translation function into all templates"""
    return dict(
        current_language=get_current_language(),
        t=get_translation  # Translation function for templates
    )

@app.before_request
def before_request():
    """Handle pre-request processing"""
    # Performance monitoring
    if PERFORMANCE_ENABLED:
        g.start_time = time.time()

    # Language detection
    if 'lang' in request.args:
        lang = request.args.get('lang')
        if lang in ['en', 'sw']:
            session['language'] = lang

@app.after_request
def after_request(response):
    """Handle post-request processing"""
    # Performance tracking
    if PERFORMANCE_ENABLED and hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        perf_monitor.track_request_time(g.start_time, request.endpoint or 'unknown')

        # Add performance headers for debugging
        response.headers['X-Response-Time'] = f"{duration:.3f}s"

    # Security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/debug/user')
@login_required
def debug_user():
    """Debug route to check current user info"""
    return jsonify({
        'user_id': current_user.id,
        'username': current_user.username,
        'role': current_user.role,
        'is_authenticated': current_user.is_authenticated,
        'is_active': current_user.is_active
    })

# API Endpoints
@app.route('/api/usage-status/<int:company_id>')
@login_required
def api_usage_status(company_id):
    """API endpoint to get real-time usage status for a company"""
    # Check if user has access to this company
    if current_user.role == 'user_admin':
        company = ClientCompany.query.filter_by(id=company_id, company_email=current_user.email).first()
    elif current_user.role == 'super_admin':
        company = ClientCompany.query.get(company_id)
    else:
        return jsonify({'error': 'Access denied'}), 403

    if not company:
        return jsonify({'error': 'Company not found'}), 404

    usage_status = get_tier_limits_status(company)
    return jsonify(usage_status)

# External API Endpoints (Business and Enterprise tiers)
def api_auth_required(f):
    """Decorator to require API authentication"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get API credentials from headers
        api_key = request.headers.get('X-API-Key')
        api_secret = request.headers.get('X-API-Secret')

        if not api_key or not api_secret:
            return jsonify({
                'error': 'Missing API credentials',
                'message': 'Include X-API-Key and X-API-Secret headers'
            }), 401

        # Verify credentials
        company = verify_api_credentials(api_key, api_secret)
        if not company:
            return jsonify({
                'error': 'Invalid API credentials',
                'message': 'Check your API key and secret'
            }), 401

        # Add company to request context
        request.api_company = company
        return f(*args, **kwargs)

    return decorated_function

@app.route('/api/v1/payments', methods=['POST'])
@api_auth_required
def api_create_payment():
    """Create a payment confirmation via API"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['customer_name', 'amount', 'mobile_operator', 'transaction_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'error': 'Missing required field',
                    'field': field
                }), 400

        company = request.api_company

        # Calculate transaction fee
        amount = float(data['amount'])
        transaction_fee = calculate_transaction_fee(company, amount)
        net_amount = amount - transaction_fee

        # Create payment confirmation
        payment = PaymentConfirmation(
            client_company_id=company.id,
            customer_name=data['customer_name'],
            customer_email=data.get('customer_email'),
            mobile_money_sender_name=data.get('sender_phone'),
            amount=amount,
            transaction_fee=transaction_fee,
            net_amount=net_amount,
            mobile_operator=data['mobile_operator'],
            transaction_id=data['transaction_id'],
            notes=data.get('notes', ''),
            status='Pending',
            submitted_at=datetime.now()
        )

        db.session.add(payment)
        db.session.commit()

        # Track usage
        track_transaction_usage(company, amount)

        return jsonify({
            'success': True,
            'payment_id': payment.id,
            'reference': f"REF{payment.id:06d}",
            'amount': amount,
            'transaction_fee': transaction_fee,
            'net_amount': net_amount,
            'status': 'pending'
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': 'Failed to create payment',
            'message': str(e)
        }), 500

@app.route('/api/v1/payments/<int:payment_id>', methods=['GET'])
@api_auth_required
def api_get_payment(payment_id):
    """Get payment status via API"""
    company = request.api_company

    payment = PaymentConfirmation.query.filter_by(
        id=payment_id,
        client_company_id=company.id
    ).first()

    if not payment:
        return jsonify({'error': 'Payment not found'}), 404

    return jsonify({
        'payment_id': payment.id,
        'reference': f"REF{payment.id:06d}",
        'customer_name': payment.customer_name,
        'amount': payment.amount,
        'transaction_fee': payment.transaction_fee,
        'net_amount': payment.net_amount,
        'mobile_operator': payment.mobile_operator,
        'transaction_id': payment.transaction_id,
        'status': payment.status.lower(),
        'submitted_at': payment.submitted_at.isoformat() if payment.submitted_at else None,
        'processed_at': payment.processed_at.isoformat() if payment.processed_at else None
    })

@app.route('/api/v1/analytics', methods=['GET'])
@api_auth_required
def api_get_analytics():
    """Get company analytics via API"""
    company = request.api_company

    # Get period from query parameter
    days = request.args.get('days', 30, type=int)
    if days not in [7, 30, 90, 365]:
        days = 30

    analytics = get_company_analytics(company.id, days)

    return jsonify({
        'company_id': company.id,
        'company_name': company.company_name,
        'period_days': analytics['period_days'],
        'metrics': {
            'total_transactions': analytics['total_transactions'],
            'confirmed_transactions': analytics['confirmed_transactions'],
            'pending_transactions': analytics['pending_transactions'],
            'total_revenue': analytics['total_revenue'],
            'total_fees': analytics['total_fees'],
            'net_revenue': analytics['net_revenue'],
            'average_transaction': analytics['average_transaction'],
            'conversion_rate': analytics['conversion_rate']
        },
        'payment_methods': analytics['payment_methods'],
        'daily_data': analytics['daily_data']
    })

# Health Check and Monitoring Endpoints
@app.route('/health')
def health_check():
    """Health check endpoint for load balancers and monitoring"""
    try:
        # Test database connectivity
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        
        # Test cache connectivity if available
        cache_status = 'not_available'
        if ENHANCED_FEATURES and cache:
            try:
                cache.set('health_test', 'ok', timeout=10)
                cache_test = cache.get('health_test')
                cache_status = 'connected' if cache_test == 'ok' else 'disconnected'
            except:
                cache_status = 'error'
        
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'database': 'connected',
            'cache': cache_status,
            'enhanced_features': ENHANCED_FEATURES,
            'version': '1.0.0'
        }
        
        if ENHANCED_FEATURES:
            logger.info("Health check performed", status="healthy")
        else:
            logger.info("Health check performed (basic mode)")
        return jsonify(health_status)
        
    except Exception as e:
        if ENHANCED_FEATURES:
            logger.error("Health check failed", error=str(e))
        else:
            logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }), 503

@app.route('/metrics')
@safe_limit("10 per minute")
def metrics():
    """Application metrics for monitoring"""
    try:
        metrics_data = get_system_metrics()
        if ENHANCED_FEATURES:
            logger.info("Metrics accessed")
        else:
            logger.info("Metrics accessed (basic mode)")
        return jsonify(metrics_data)
    except Exception as e:
        if ENHANCED_FEATURES:
            logger.error("Metrics collection failed", error=str(e))
        else:
            logger.error(f"Metrics collection failed: {e}")
        return jsonify({'error': 'Metrics unavailable'}), 500

def get_system_metrics():
    """Get system metrics (cached if cache available)"""
    if ENHANCED_FEATURES and cache:
        @cache.memoize(timeout=60)  # Cache for 1 minute
        def _cached_metrics():
            return _calculate_metrics()
        return _cached_metrics()
    else:
        return _calculate_metrics()

def _calculate_metrics():
    """Calculate system metrics"""
    return {
        'companies': {
            'total': ClientCompany.query.count(),
            'active': ClientCompany.query.filter_by(subscription_status='Active').count(),
            'suspended': ClientCompany.query.filter_by(subscription_status='Suspended').count()
        },
        'payments': {
            'total': PaymentConfirmation.query.count(),
            'pending': PaymentConfirmation.query.filter_by(status='Pending').count(),
            'confirmed': PaymentConfirmation.query.filter_by(status='Confirmed').count(),
            'today_confirmed': PaymentConfirmation.query.filter(
                PaymentConfirmation.status == 'Confirmed',
                PaymentConfirmation.processed_at >= datetime.now().replace(hour=0, minute=0, second=0)
            ).count()
        },
        'invoices': {
            'total': Invoice.query.count(),
            'unpaid': Invoice.query.filter_by(status='Unpaid').count(),
            'paid': Invoice.query.filter_by(status='Paid').count()
        },
        'revenue': {
            'monthly': calculate_monthly_revenue(),
            'daily': calculate_daily_revenue()
        },
        'timestamp': datetime.utcnow().isoformat()
    }

def calculate_daily_revenue():
    """Calculate daily revenue from confirmed payments"""
    if ENHANCED_FEATURES and cache:
        @cache.memoize(timeout=300)  # Cache for 5 minutes
        def _cached_daily():
            return _calc_daily_revenue()
        return _cached_daily()
    else:
        return _calc_daily_revenue()

def _calc_daily_revenue():
    """Calculate daily revenue"""
    today = datetime.now().replace(hour=0, minute=0, second=0)
    return db.session.query(db.func.sum(PaymentConfirmation.amount)).filter(
        PaymentConfirmation.status == 'Confirmed',
        PaymentConfirmation.processed_at >= today
    ).scalar() or 0.0

def calculate_monthly_revenue():
    """Calculate monthly revenue from paid bills"""
    if ENHANCED_FEATURES and cache:
        @cache.memoize(timeout=300)  # Cache for 5 minutes
        def _cached_monthly():
            return _calc_monthly_revenue()
        return _cached_monthly()
    else:
        return _calc_monthly_revenue()

def _calc_monthly_revenue():
    """Calculate monthly revenue"""
    first_day = datetime.now().replace(day=1, hour=0, minute=0, second=0)
    return db.session.query(db.func.sum(Bill.total_amount)).filter(
        Bill.status == 'Paid',
        Bill.created_at >= first_day
    ).scalar() or 0.0

@app.route('/create-invoice', methods=['GET', 'POST'])
def create_invoice():
    if request.method == 'POST':
        # Create new invoice
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            customer_name=request.form['customer_name'],
            customer_email=request.form.get('customer_email', ''),
            customer_phone=request.form.get('customer_phone', ''),
            amount=float(request.form['amount']),
            service_description=request.form['service_description'],
            due_date=datetime.now() + timedelta(days=int(request.form.get('due_days', 7)))
        )
        
        db.session.add(invoice)
        db.session.commit()
        
        return redirect(url_for('view_transaction', invoice_number=invoice.invoice_number))
    
    return render_template('create_invoice.html')

@app.route('/create-invoice-from-cart', methods=['POST'])
@require_api_key
@safe_limit("20 per minute")
def create_invoice_from_cart():
    """Create invoice from cart data - used by company checkout systems"""
    try:
        # Log API request
        if ENHANCED_FEATURES:
            logger.info("Cart invoice creation requested", 
                       user_agent=request.headers.get('User-Agent'),
                       ip_address=get_remote_address())
        else:
            logger.info("Cart invoice creation requested")
        
        # Get cart data from request
        cart_data = request.get_json()
        
        if not cart_data:
            if ENHANCED_FEATURES:
                logger.warning("Cart invoice creation failed - no data provided")
            else:
                logger.warning("Cart invoice creation failed - no data provided")
            return {'error': 'No cart data provided'}, 400
            
        # Validate required fields
        required_fields = ['customer_name', 'customer_email', 'cart_items', 'total_amount']
        for field in required_fields:
            if field not in cart_data:
                return {'error': f'Missing required field: {field}'}, 400
        
        # Calculate amounts
        subtotal = cart_data.get('subtotal', 0.0)
        tax_amount = cart_data.get('tax_amount', 0.0)
        discount_amount = cart_data.get('discount_amount', 0.0)
        shipping_cost = cart_data.get('shipping_cost', 0.0)
        total_amount = float(cart_data['total_amount'])
        
        # Generate service description from cart items
        service_description = "Order Items: "
        if isinstance(cart_data['cart_items'], list):
            items = []
            for item in cart_data['cart_items']:
                if isinstance(item, dict):
                    name = item.get('name', 'Unknown Item')
                    qty = item.get('quantity', 1)
                    items.append(f"{name} (x{qty})")
            service_description += ", ".join(items)
        else:
            service_description += str(cart_data['cart_items'])
        
        # Create invoice
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            customer_name=cart_data['customer_name'],
            customer_email=cart_data['customer_email'],
            customer_phone=cart_data.get('customer_phone', ''),
            amount=total_amount,
            service_description=service_description,
            due_date=datetime.now() + timedelta(days=int(cart_data.get('due_days', 7))),
            cart_items=json.dumps(cart_data['cart_items']),
            subtotal=subtotal,
            tax_amount=tax_amount,
            discount_amount=discount_amount,
            shipping_cost=shipping_cost
        )
        
        db.session.add(invoice)
        db.session.commit()
        
        # Return invoice details for checkout integration
        return {
            'success': True,
            'invoice_number': invoice.invoice_number,
            'transaction_url': url_for('view_transaction', invoice_number=invoice.invoice_number, _external=True),
            'payment_url': url_for('payment_instructions_with_invoice', invoice_number=invoice.invoice_number, _external=True),
            'amount': invoice.amount,
            'created_at': invoice.created_at.isoformat()
        }
        
    except Exception as e:
        return {'error': f'Failed to create invoice: {str(e)}'}, 500

@app.route('/transaction-id/<invoice_number>')
def view_transaction(invoice_number):
    invoice = Invoice.query.filter_by(invoice_number=invoice_number).first_or_404()
    return render_template('view_invoice.html', invoice=invoice, now=datetime.now())

@app.route('/payment-instructions')
def payment_instructions():
    return render_template('payment_instructions.html')

@app.route('/payment-instructions/<invoice_number>')
def payment_instructions_with_invoice(invoice_number):
    invoice = Invoice.query.filter_by(invoice_number=invoice_number).first_or_404()
    return render_template('payment_instructions.html', invoice=invoice)

@app.route('/confirm-payment', methods=['GET', 'POST'])
@safe_limit("10 per minute")
def confirm_payment():
    if request.method == 'POST':
        try:
            # Rate limiting check
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
            rate_limit_key = f"payment_{client_ip}"

            if not simple_rate_limit(rate_limit_key, max_attempts=10, window_minutes=15):
                flash('Too many payment submissions. Please try again in 15 minutes.', 'danger')
                return render_template('confirm_payment.html')

            # Validate and sanitize inputs
            customer_name = sanitize_input(request.form.get('customer_name', ''), 100)
            customer_email = sanitize_input(request.form.get('customer_email', ''), 120)
            mobile_money_sender_name = sanitize_input(request.form.get('mobile_money_sender_name', ''), 100)
            amount_str = request.form.get('amount', '')
            mobile_operator = sanitize_input(request.form.get('mobile_operator', ''), 50)
            transaction_id = sanitize_input(request.form.get('transaction_id', ''), 100)
            service_description = sanitize_input(request.form.get('service_description', ''), 500)

            # Validation
            if not customer_name:
                flash('Customer name is required', 'danger')
                return render_template('confirm_payment.html')

            if not mobile_money_sender_name:
                flash('Mobile money sender name is required', 'danger')
                return render_template('confirm_payment.html')

            # Validate amount
            amount = validate_payment_amount(amount_str)

            # Validate transaction ID
            transaction_id = validate_transaction_id(transaction_id, mobile_operator)

            # Validate mobile money name
            mobile_money_sender_name = validate_mobile_money_name(mobile_money_sender_name)

            # Log payment confirmation attempt
            if ENHANCED_FEATURES:
                logger.info("Payment confirmation submitted",
                           customer_name=customer_name,
                           amount=amount,
                           operator=mobile_operator,
                           transaction_id=transaction_id,
                           ip_address=client_ip)

            # Calculate transaction fee (default to 2.5% for unassigned customers)
            transaction_fee = amount * 0.025  # Default fee for new customers
            net_amount = amount - transaction_fee

            # Create payment confirmation without invoice matching
            confirmation = PaymentConfirmation(
                invoice_id=None,  # No automatic matching - admin will match manually
                customer_name=customer_name,
                customer_email=customer_email if customer_email else None,
                mobile_money_sender_name=mobile_money_sender_name,
                amount=amount,
                transaction_fee=transaction_fee,
                net_amount=net_amount,
                mobile_operator=mobile_operator,
                transaction_id=transaction_id,
                service_description=service_description
            )

            # Validate the payment confirmation object
            confirmation.validate_amount()
            confirmation.validate_transaction_id_format()
            confirmation.validate_mobile_money_name()

            db.session.add(confirmation)
            db.session.commit()

            if ENHANCED_FEATURES:
                logger.info("Payment confirmation created",
                           confirmation_id=confirmation.id,
                           customer_name=confirmation.customer_name)
            else:
                logger.info(f"Payment confirmation created: {confirmation.id}")

            flash(get_translation('Payment confirmation submitted successfully! We will verify and process your payment shortly.'), 'success')
            return render_template('confirmation_success.html',
                                 reference_id=f"REF{confirmation.id:06d}",
                                 invoice=None,
                                 register_url=None)  # No registration URL until confirmed

        except ValueError as e:
            # Validation errors
            flash(str(e), 'danger')
            if ENHANCED_FEATURES:
                logger.warning("Payment validation failed", error=str(e), ip=client_ip)
            return render_template('confirm_payment.html')

        except Exception as e:
            # Database or other errors
            db.session.rollback()
            flash('An error occurred while processing your payment confirmation. Please try again.', 'danger')
            if ENHANCED_FEATURES:
                logger.error("Payment confirmation error", error=str(e), ip=client_ip)
            return render_template('confirm_payment.html')

    return render_template('confirm_payment.html', invoice=None)

@app.route('/confirm-payment/<invoice_number>')
def confirm_payment_with_invoice(invoice_number):
    invoice = Invoice.query.filter_by(invoice_number=invoice_number).first_or_404()
    return render_template('confirm_payment.html', invoice=invoice)

@app.route('/admin-login', methods=['GET', 'POST'])
def master_admin_login():
    if request.method == 'POST':
        # Rate limiting check
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        rate_limit_key = f"login_master_{client_ip}"

        if not simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15):
            flash('Too many login attempts. Please try again in 15 minutes.', 'danger')
            return render_template('master_admin_login.html')

        username = sanitize_input(request.form.get('username', ''), 80)
        password = request.form.get('password', '')

        if not username or not password:
            flash('Username and password are required', 'danger')
            return render_template('master_admin_login.html')

        user = User.query.filter_by(username=username, is_active=True, role='super_admin').first()

        if user and user.is_account_locked():
            flash('Account is temporarily locked due to failed login attempts', 'danger')
            return render_template('master_admin_login.html')

        if user and check_password_hash(user.password_hash, password):
            # Successful login using new authentication system
            user.unlock_account()  # Reset failed attempts
            db.session.commit()

            # Use new authentication manager
            if AuthenticationManager.login_user_with_role(user):
                # Log successful login
                if ENHANCED_FEATURES:
                    logger.info("Master admin login successful", username=username, ip=client_ip)

                return AuthenticationManager.smart_redirect_by_role(user)
            else:
                flash('Login failed. Please try again.', 'danger')
        else:
            # Failed login
            if user:
                user.increment_failed_login()

            # Log failed login attempt
            if ENHANCED_FEATURES:
                logger.warning("Master admin login failed", username=username, ip=client_ip)

            flash('Invalid username or password, or account is disabled', 'danger')

    return render_template('master_admin_login.html')

@app.route('/login', methods=['GET', 'POST'])
@app.route('/user-admin-login', methods=['GET', 'POST'])
def user_admin_login():
    if request.method == 'POST':
        # Rate limiting check
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        rate_limit_key = f"login_user_{client_ip}"

        if not simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15):
            flash('Too many login attempts. Please try again in 15 minutes.', 'danger')
            return render_template('user_admin_login.html')

        username = sanitize_input(request.form.get('username', ''), 80)
        password = request.form.get('password', '')

        if not username or not password:
            flash('Username and password are required', 'danger')
            return render_template('user_admin_login.html')

        # User admin is the company customer with a paid tier
        # Allow login with either username or email
        user = User.query.filter(
            ((User.username == username) | (User.email == username)),
            User.is_active == True,
            User.role == 'user_admin'
        ).first()

        if user and user.is_account_locked():
            flash('Account is temporarily locked due to failed login attempts', 'danger')
            return render_template('user_admin_login.html')

        if user and check_password_hash(user.password_hash, password):
            # Successful login using new authentication system
            user.unlock_account()  # Reset failed attempts
            db.session.commit()

            # Use new authentication manager
            if AuthenticationManager.login_user_with_role(user):
                # Log successful login
                if ENHANCED_FEATURES:
                    logger.info("User admin login successful", username=username, ip=client_ip)

                return AuthenticationManager.smart_redirect_by_role(user)
            else:
                flash('Login failed. Please try again.', 'danger')
        else:
            # Failed login
            if user:
                user.increment_failed_login()

            # Log failed login attempt
            if ENHANCED_FEATURES:
                logger.warning("User admin login failed", username=username, ip=client_ip)

            flash('Invalid username or password, or account is disabled', 'danger')

    return render_template('user_admin_login.html')

@app.route('/company-login', methods=['GET', 'POST'])
def company_login():
    if request.method == 'POST':
        # Rate limiting check
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        rate_limit_key = f"login_company_{client_ip}"

        if not simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15):
            flash('Too many login attempts. Please try again in 15 minutes.', 'danger')
            return render_template('company_login.html')

        username = sanitize_input(request.form.get('username', ''), 80)
        password = request.form.get('password', '')

        if not username or not password:
            flash('Username and password are required', 'danger')
            return render_template('company_login.html')

        user = User.query.filter_by(username=username, is_active=True, role='company_user').first()

        if user and user.is_account_locked():
            flash('Account is temporarily locked due to failed login attempts', 'danger')
            return render_template('company_login.html')

        if user and check_password_hash(user.password_hash, password):
            # Successful login using new authentication system
            user.unlock_account()  # Reset failed attempts
            db.session.commit()

            # Use new authentication manager
            if AuthenticationManager.login_user_with_role(user):
                # Log successful login
                if ENHANCED_FEATURES:
                    logger.info("Company user login successful", username=username, ip=client_ip)

                return AuthenticationManager.smart_redirect_by_role(user)
            else:
                flash('Login failed. Please try again.', 'danger')
        else:
            # Failed login
            if user:
                user.increment_failed_login()

            # Log failed login attempt
            if ENHANCED_FEATURES:
                logger.warning("Company user login failed", username=username, ip=client_ip)

            flash('Invalid username or password, or account is disabled', 'danger')

    return render_template('company_login.html')

@app.route('/logout')
@login_required
def logout():
    # Use new authentication manager for secure logout
    AuthenticationManager.logout_user_safe()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('index'))

@app.route('/master-admin')
@require_super_admin
@session_timeout_check(timeout_minutes=120)  # 2 hour timeout for super admin
def master_admin_dashboard():
    pending_count = PaymentConfirmation.query.filter_by(status='Pending').count()
    confirmed_count = PaymentConfirmation.query.filter_by(status='Confirmed').count()
    today_confirmed = PaymentConfirmation.query.filter(
        PaymentConfirmation.status == 'Confirmed',
        PaymentConfirmation.processed_at >= datetime.now().replace(hour=0, minute=0, second=0)
    ).count()
    
    # Invoice statistics
    unpaid_invoices = Invoice.query.filter_by(status='Unpaid').count()
    total_invoices = Invoice.query.count()
    
    # Billing statistics
    active_companies = ClientCompany.query.filter_by(subscription_status='Active').count()
    monthly_revenue = db.session.query(db.func.sum(Bill.total_amount)).filter(
        Bill.status == 'Paid',
        Bill.created_at >= datetime.now().replace(day=1)
    ).scalar() or 0.0
    pending_bills = Bill.query.filter_by(status='Pending').count()
    
    return render_template('admin_dashboard.html', 
                         pending_count=pending_count,
                         confirmed_count=confirmed_count,
                         today_confirmed=today_confirmed,
                         unpaid_invoices=unpaid_invoices,
                         total_invoices=total_invoices,
                         active_companies=active_companies,
                         monthly_revenue=monthly_revenue,
                         pending_bills=pending_bills)

@app.route('/user-admin')
@login_required
def user_admin_dashboard():
    # Only allow user_admin role (company customers with paid tiers)
    if current_user.role != 'user_admin':
        return redirect(url_for('user_admin_login'))

    # Get user's associated company (if any)
    user_company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    usage_status = None

    # Get statistics for this user's company or general stats if no company
    if user_company:
        # Get usage status for the company
        usage_status = get_tier_limits_status(user_company)
        # Company-specific statistics (with safe queries)
        try:
            pending_count = PaymentConfirmation.query.filter_by(
                client_company_id=user_company.id,
                status='Pending'
            ).count()
        except:
            pending_count = 0

        try:
            confirmed_count = PaymentConfirmation.query.filter_by(
                client_company_id=user_company.id,
                status='Confirmed'
            ).count()
        except:
            confirmed_count = 0

        try:
            total_invoices = Invoice.query.filter_by(created_by=current_user.id).count()
        except:
            total_invoices = 0

        try:
            team_members = User.query.filter_by(email=user_company.company_email).all()
        except:
            team_members = []
    else:
        # General statistics if no company association
        pending_count = PaymentConfirmation.query.filter_by(status='Pending').count()
        confirmed_count = PaymentConfirmation.query.filter_by(status='Confirmed').count()
        total_invoices = Invoice.query.count()
        team_members = []

    return render_template('user_admin_dashboard.html',
                         pending_count=pending_count,
                         confirmed_count=confirmed_count,
                         total_invoices=total_invoices,
                         user_company=user_company,
                         team_members=team_members,
                         usage_status=usage_status)

@app.route('/admin/payments')
@login_required
def admin_payments():
    # Only allow super_admin (EXLIPA platform control)
    if current_user.role != 'super_admin':
        flash('Access denied. Only EXLIPA administrators can manage payments.', 'danger')
        return smart_redirect_by_role()

    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '')

    query = PaymentConfirmation.query
    
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    
    if search:
        query = query.filter(
            db.or_(
                PaymentConfirmation.customer_name.contains(search),
                PaymentConfirmation.transaction_id.contains(search)
            )
        )
    
    payments = query.order_by(PaymentConfirmation.submitted_at.desc()).all()
    
    return render_template('admin_payments.html', payments=payments, 
                         status_filter=status_filter, search=search)

@app.route('/admin/invoices')
@login_required
def admin_invoices():
    # Only allow super_admin (EXLIPA platform control)
    if current_user.role != 'super_admin':
        flash('Access denied. Only EXLIPA administrators can manage invoices.', 'danger')
        return smart_redirect_by_role()

    status_filter = request.args.get('status', 'all')
    search = request.args.get('search', '')

    query = Invoice.query
    
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    
    if search:
        query = query.filter(
            db.or_(
                Invoice.customer_name.contains(search),
                Invoice.invoice_number.contains(search)
            )
        )
    
    invoices = query.order_by(Invoice.created_at.desc()).all()
    
    return render_template('admin_invoices.html', invoices=invoices, 
                         status_filter=status_filter, search=search, now=datetime.now())

@app.route('/admin/payment/<int:payment_id>')
@login_required
def payment_detail(payment_id):
    payment = PaymentConfirmation.query.get_or_404(payment_id)
    register_url = None
    if payment.status == 'Confirmed':
        token = serializer.dumps(payment.id, salt='register')
        register_url = url_for('register', token=token, _external=True)
    return render_template('payment_detail.html', payment=payment, register_url=register_url)

@app.route('/admin/payment/<int:payment_id>/action', methods=['POST'])
@login_required
def payment_action(payment_id):
    payment = PaymentConfirmation.query.get_or_404(payment_id)
    action = request.form['action']

    if action == 'confirm':
        try:
            # Capture SMS verification data
            sms_sender = sanitize_input(request.form.get('sms_sender', ''), 20)
            sms_amount = request.form.get('sms_amount', '')
            sms_sender_name = sanitize_input(request.form.get('sms_sender_name', ''), 100)
            sms_transaction_ref = sanitize_input(request.form.get('sms_transaction_ref', ''), 100)
            admin_verification_notes = sanitize_input(request.form.get('admin_verification_notes', ''), 1000)

            # Validation
            if not sms_sender:
                flash('SMS sender is required for verification', 'danger')
                return redirect(url_for('payment_detail', payment_id=payment_id))

            if not sms_amount:
                flash('SMS amount is required for verification', 'danger')
                return redirect(url_for('payment_detail', payment_id=payment_id))

            if not sms_sender_name:
                flash('SMS sender name is required for verification', 'danger')
                return redirect(url_for('payment_detail', payment_id=payment_id))

            # Validate SMS amount
            try:
                sms_amount_float = float(sms_amount)
                if abs(sms_amount_float - payment.amount) > 1:  # Allow 1 TZS difference for rounding
                    flash(f'SMS amount ({sms_amount_float:,.0f}) does not match payment amount ({payment.amount:,.0f})', 'danger')
                    return redirect(url_for('payment_detail', payment_id=payment_id))
            except ValueError:
                flash('Invalid SMS amount format', 'danger')
                return redirect(url_for('payment_detail', payment_id=payment_id))

            # Validate sender name matching
            if sms_sender_name.lower().strip() != payment.mobile_money_sender_name.lower().strip():
                flash(f'SMS sender name "{sms_sender_name}" does not match registered name "{payment.mobile_money_sender_name}"', 'danger')
                return redirect(url_for('payment_detail', payment_id=payment_id))

            # Update payment with SMS verification data
            payment.sms_sender = sms_sender
            payment.sms_amount = sms_amount_float
            payment.sms_sender_name = sms_sender_name
            payment.sms_transaction_ref = sms_transaction_ref
            payment.admin_verification_notes = admin_verification_notes

            # Calculate transaction fee if company is associated
            if payment.client_company_id:
                company = ClientCompany.query.get(payment.client_company_id)
                if company:
                    # Calculate transaction fee based on company's tier
                    transaction_fee = calculate_transaction_fee(company, payment.amount)
                    payment.transaction_fee = transaction_fee
                    payment.net_amount = payment.amount - transaction_fee

                    # Track transaction usage for freemium model
                    track_transaction_usage(company, payment.amount)

                    print(f"DEBUG: Transaction fee calculated: TSh {transaction_fee:,.2f} for company {company.company_name}")

            # Confirm the payment
            payment.status = 'Confirmed'
            payment.processed_at = datetime.utcnow()
            payment.processed_by = current_user.id

            # Mark associated invoice as paid if exists
            if payment.invoice:
                payment.invoice.status = 'Paid'

            # AUTOMATIC TIER ASSIGNMENT BASED ON PAYMENT AMOUNT (Updated for new model)
            # Determine pricing tier based on payment amount
            pricing_tier = None
            payment_amount = float(payment.amount)

            # New pricing model: match monthly fees only (no setup fees)
            all_tiers = PricingTier.query.all()
            for tier in all_tiers:
                # Allow for small variations in payment amount (±1000 TSh)
                if abs(payment_amount - tier.monthly_fee) <= 1000:
                    pricing_tier = tier
                    break

            # Store the determined tier in the payment record
            if pricing_tier:
                payment.pricing_tier_id = pricing_tier.id
                print(f"DEBUG: Payment {payment.id} assigned to tier: {pricing_tier.name} (Monthly: TSh {pricing_tier.monthly_fee:,.0f}, Paid: TSh {payment_amount:,.0f})")
            else:
                print(f"DEBUG: Could not determine tier for payment amount: TSh {payment_amount:,.0f}")
                # Default to Free tier for unmatched payments
                free_tier = PricingTier.query.filter_by(name='Free').first()
                if free_tier:
                    payment.pricing_tier_id = free_tier.id
                    print(f"DEBUG: Defaulted to Free tier for payment {payment.id}")

            # Generate registration token and link
            token = serializer.dumps(payment.id, salt='register')
            register_url = url_for('register', token=token, _external=True)

            db.session.commit()

            tier_message = f" (Assigned to {pricing_tier.name} tier)" if pricing_tier else ""
            flash(f'Payment confirmed successfully with SMS verification!{tier_message}', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'Error confirming payment: {str(e)}', 'danger')
            return redirect(url_for('payment_detail', payment_id=payment_id))

        # Determine recipient email
        recipient_email = payment.customer_email
        if not recipient_email and payment.invoice and payment.invoice.customer_email:
            recipient_email = payment.invoice.customer_email

        # Send registration link if email is available
        if recipient_email:
            try:
                msg = Message(
                    subject='🎉 Complete Your Exlipa Registration - Payment Confirmed',
                    recipients=[recipient_email],
                    body=f"""
Dear {payment.customer_name},

Great news! Your payment has been confirmed and verified.

💰 Payment Details:
• Amount: TZS {payment.amount:,.2f}
• Reference: REF{payment.id:06d}
• Transaction ID: {payment.transaction_id}

🚀 Next Step - Complete Your Registration:
Click the link below to set up your Exlipa account (valid for 24 hours):

{register_url}

📞 Need Help?
• Email: <EMAIL>
• Phone: +*********** 789
• Check Status: {url_for('check_payment_status', _external=True)}

If you did not initiate this request, please ignore this email.

Welcome to Exlipa!
The Exlipa Team
                    """
                )
                mail.send(msg)
                flash(f'{get_translation("Registration link sent to")} {recipient_email}.')
            except Exception as e:
                print(f"DEBUG: Email sending failed: {str(e)}")
                print(f"DEBUG: Registration URL: {register_url}")
                flash(get_translation('Payment confirmed! Registration link ready. Email sending disabled for testing.'), 'success')
        else:
            flash(get_translation('No email address found for this payment. Please send the registration link manually.'), 'warning')
        return render_template('payment_detail.html', payment=payment, register_url=register_url)

    elif action == 'reject':
        payment.status = 'Rejected'
        payment.rejection_reason = request.form.get('rejection_reason', '')
        payment.processed_at = datetime.utcnow()
        payment.processed_by = current_user.id
        flash(get_translation('Payment rejected!'))
        
    elif action == 'process':
        payment.status = 'Processed'
        flash('Payment marked as processed!')
        
    elif action == 'add_notes':
        # Handle adding internal notes
        payment.internal_notes = request.form.get('internal_notes', '')
        flash('Internal notes updated!')
    
    # Add internal notes if provided (for other actions)
    if request.form.get('internal_notes') and action != 'add_notes':
        payment.internal_notes = request.form['internal_notes']
    
    db.session.commit()
    return redirect(url_for('payment_detail', payment_id=payment_id))

@app.route('/admin/payment/<int:payment_id>/receipt')
@login_required
def generate_receipt(payment_id):
    payment = PaymentConfirmation.query.get_or_404(payment_id)
    
    # Only generate receipts for confirmed or processed payments
    if payment.status not in ['Confirmed', 'Processed']:
        flash('Receipts can only be generated for confirmed payments!')
        return redirect(url_for('payment_detail', payment_id=payment_id))
    
    # Calculate transaction fee if not already calculated
    if not payment.fee_calculated and payment.client_company:
        payment.transaction_fee = calculate_transaction_fee(payment.amount, payment.client_company)
        payment.fee_calculated = True
        db.session.commit()
    
    # Prepare payment data for receipt generation
    payment_data = {
        'id': payment.id,
        'customer_name': payment.customer_name,
        'amount': payment.amount,
        'mobile_operator': payment.mobile_operator,
        'transaction_id': payment.transaction_id,
        'service_description': payment.service_description,
        'submitted_at': payment.submitted_at,
        'processed_at': payment.processed_at
    }
    
    # Get company information
    company_settings = get_company_settings(payment.client_company_id)
    company_info = {
        'name': company_settings.company_name,
        'address': company_settings.company_address,
        'phone': company_settings.company_phone,
        'email': company_settings.company_email,
        'website': getattr(company_settings, 'company_website', ''),
        'tin': getattr(company_settings, 'company_tin', '')
    }
    
    try:
        # Generate PDF receipt
        pdf_data = generate_receipt_pdf(payment_data, company_info)
        
        # Mark receipt as generated
        payment.receipt_generated = True
        db.session.commit()
        
        # Create response
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        
        # Generate filename
        filename = generate_receipt_filename(payment.id, payment.customer_name)
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        flash(f'Error generating receipt: {str(e)}')
        return redirect(url_for('payment_detail', payment_id=payment_id))

# Company Management Routes
@app.route('/admin/companies')
@login_required
def admin_companies():
    # Only allow super_admin (EXLIPA platform control)
    if current_user.role != 'super_admin':
        flash('Access denied. Only EXLIPA administrators can manage companies.', 'danger')
        return smart_redirect_by_role()

    companies = ClientCompany.query.order_by(ClientCompany.created_at.desc()).all()
    return render_template('admin_companies.html', companies=companies)

@app.route('/admin/companies/new', methods=['GET', 'POST'])
@login_required
def create_company():
    if request.method == 'POST':
        try:
            logo_filename = None
            if 'company_logo' in request.files:
                logo_file = request.files['company_logo']
                if logo_file and allowed_logo_file(logo_file.filename):
                    filename = secure_filename(logo_file.filename)
                    logo_path = os.path.join('static', 'company_logos', filename)
                    logo_file.save(logo_path)
                    logo_filename = f'company_logos/{filename}'
            company = ClientCompany(
                company_name=request.form['company_name'],
                company_address=request.form.get('company_address', ''),
                company_phone=request.form.get('company_phone', ''),
                company_email=request.form.get('company_email', ''),
                company_website=request.form.get('company_website', ''),
                company_tin=request.form.get('company_tin', ''),
                mpesa_till=request.form.get('mpesa_till', ''),
                tigo_paybill=request.form.get('tigo_paybill', ''),
                airtel_merchant=request.form.get('airtel_merchant', ''),
                crdb_merchant=request.form.get('crdb_merchant', ''),
                pricing_tier_id=int(request.form['pricing_tier_id']),
                billing_cycle=request.form.get('billing_cycle', 'Monthly'),
                primary_color=request.form.get('primary_color', '#28a745'),
                secondary_color=request.form.get('secondary_color', '#007bff'),
                currency_code=request.form.get('currency_code', 'TZS'),
                currency_symbol=request.form.get('currency_symbol', 'TZS'),
                created_by=current_user.id,
                logo_filename=logo_filename
            )
            
            # Set next billing date
            if company.billing_cycle == 'Annual':
                company.next_billing_date = datetime.now() + timedelta(days=365)
            else:
                company.next_billing_date = datetime.now() + timedelta(days=30)
            
            db.session.add(company)
            db.session.commit()
            
            # Create initial subscription
            subscription = Subscription(
                client_company_id=company.id,
                pricing_tier_id=company.pricing_tier_id,
                billing_cycle=company.billing_cycle,
                next_billing_date=company.next_billing_date
            )
            db.session.add(subscription)
            
            # Generate setup fee bill if applicable
            if company.pricing_tier.setup_fee > 0:
                setup_bill = Bill(
                    bill_number=generate_bill_number(),
                    client_company_id=company.id,
                    bill_type='Setup',
                    base_amount=company.pricing_tier.setup_fee,
                    setup_fees=company.pricing_tier.setup_fee,
                    tax_amount=company.pricing_tier.setup_fee * 0.18,
                    total_amount=company.pricing_tier.setup_fee * 1.18,
                    due_date=datetime.now() + timedelta(days=7),
                    created_by=current_user.id
                )
                db.session.add(setup_bill)
            
            db.session.commit()
            flash(f'Company "{company.company_name}" created successfully!')
            return redirect(url_for('admin_companies'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating company: {str(e)}')
    pricing_tiers = PricingTier.query.filter_by(is_active=True).all()
    pricing_tiers_dicts = [pricing_tier_to_dict(t) for t in pricing_tiers]
    return render_template('admin_company_form.html', company=None, pricing_tiers=pricing_tiers_dicts)

@app.route('/admin/companies/<int:company_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_company(company_id):
    company = ClientCompany.query.get_or_404(company_id)
    if request.method == 'POST':
        try:
            company.company_name = request.form['company_name']
            company.company_address = request.form.get('company_address', '')
            company.company_phone = request.form.get('company_phone', '')
            company.company_email = request.form.get('company_email', '')
            company.company_website = request.form.get('company_website', '')
            company.company_tin = request.form.get('company_tin', '')
            company.mpesa_till = request.form.get('mpesa_till', '')
            company.tigo_paybill = request.form.get('tigo_paybill', '')
            company.airtel_merchant = request.form.get('airtel_merchant', '')
            company.crdb_merchant = request.form.get('crdb_merchant', '')
            company.primary_color = request.form.get('primary_color', '#28a745')
            company.secondary_color = request.form.get('secondary_color', '#007bff')
            company.currency_code = request.form.get('currency_code', 'TZS')
            company.currency_symbol = request.form.get('currency_symbol', 'TZS')
            
            # Handle pricing tier change
            new_tier_id = int(request.form['pricing_tier_id'])
            if company.pricing_tier_id != new_tier_id:
                company.pricing_tier_id = new_tier_id
                # Update billing cycle if changed
                new_billing_cycle = request.form.get('billing_cycle', 'Monthly')
                if company.billing_cycle != new_billing_cycle:
                    company.billing_cycle = new_billing_cycle
                    # Recalculate next billing date
                    if new_billing_cycle == 'Annual':
                        company.next_billing_date = datetime.now() + timedelta(days=365)
                    else:
                        company.next_billing_date = datetime.now() + timedelta(days=30)
            
            # Handle Dynamic POS request
            dynamic_pos_requested = request.form.get('dynamic_pos_request')
            if dynamic_pos_requested:
                company.dynamic_pos_payment_pending = True
            # Optionally, reset if unchecked (not allowed if already paid)
            else:
                company.dynamic_pos_payment_pending = False
            
            if 'company_logo' in request.files:
                logo_file = request.files['company_logo']
                if logo_file and allowed_logo_file(logo_file.filename):
                    filename = secure_filename(logo_file.filename)
                    logo_path = os.path.join('static', 'company_logos', filename)
                    logo_file.save(logo_path)
                    company.logo_filename = f'company_logos/{filename}'
            
            db.session.commit()
            flash(f'Company "{company.company_name}" updated successfully!')
            return redirect(url_for('admin_companies'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating company: {str(e)}')
    pricing_tiers = PricingTier.query.filter_by(is_active=True).all()
    pricing_tiers_dicts = [pricing_tier_to_dict(t) for t in pricing_tiers]
    return render_template('admin_company_form.html', company=company, pricing_tiers=pricing_tiers_dicts)

@app.route('/admin/companies/<int:company_id>/unlock_pos', methods=['POST'])
@login_required
def unlock_dynamic_pos(company_id):
    company = ClientCompany.query.get_or_404(company_id)
    if company.dynamic_pos_payment_pending and not company.dynamic_pos_enabled:
        company.dynamic_pos_enabled = True
        company.dynamic_pos_payment_pending = False
        db.session.commit()
        flash('Dynamic POS feature has been unlocked for this company!')
    else:
        flash('POS feature is already active or not pending payment.')
    return redirect(url_for('edit_company', company_id=company_id))

@app.route('/admin/pos-requests')
@login_required
def admin_pos_requests():
    # Debug: Check current user role
    if ENHANCED_FEATURES:
        logger.info("POS requests access attempt", user_role=current_user.role, user_id=current_user.id)

    if current_user.role != 'super_admin':
        flash(f'Access denied. Your role: {current_user.role}. Only EXLIPA administrators can manage POS requests.', 'danger')
        return smart_redirect_by_role()

    pending_companies = ClientCompany.query.filter_by(dynamic_pos_payment_pending=True, dynamic_pos_enabled=False).all()
    return render_template('admin_pos_requests.html', pending_companies=pending_companies)

@app.route('/admin/approve-pos/<int:company_id>', methods=['POST'])
@login_required
def approve_pos(company_id):
    if current_user.role != 'super_admin':
        flash('Access denied. Only EXLIPA administrators can approve POS requests.', 'danger')
        return smart_redirect_by_role()
    company = ClientCompany.query.get_or_404(company_id)
    if company.dynamic_pos_payment_pending and not company.dynamic_pos_enabled:
        company.dynamic_pos_enabled = True
        company.dynamic_pos_payment_pending = False
        db.session.commit()
        flash(f'POS unlocked for {company.company_name}.', 'success')
    else:
        flash('POS is already active or not pending payment.', 'info')
    return redirect(url_for('admin_pos_requests'))

@app.route('/admin/payment/<int:payment_id>/resend-link', methods=['POST'])
@login_required
def resend_registration_link(payment_id):
    """Resend registration link for confirmed payments"""
    if current_user.role not in ['admin', 'super_admin', 'master_admin']:
        flash('Access denied.', 'danger')
        return smart_redirect_by_role()

    payment = PaymentConfirmation.query.get_or_404(payment_id)

    if payment.status != 'Confirmed':
        flash('Can only resend registration links for confirmed payments.', 'warning')
        return redirect(url_for('payment_detail', payment_id=payment_id))

    # Generate new registration token
    token = serializer.dumps(payment.id, salt='register')
    register_url = url_for('register', token=token, _external=True)

    # Determine recipient email
    recipient_email = payment.customer_email
    if not recipient_email and payment.invoice and payment.invoice.customer_email:
        recipient_email = payment.invoice.customer_email

    # Send registration link if email is available
    if recipient_email and '@' in recipient_email:
        try:
            msg = Message(
                subject='Your Exlipa Registration Link (Resent)',
                recipients=[recipient_email],
                body=f"""
Dear {payment.customer_name},

Your payment has been confirmed and your registration link has been resent.

Please complete your registration by clicking the link below (valid for 24 hours):

{register_url}

Reference ID: REF{payment.id:06d}
Amount: TZS {payment.amount:,.2f}

If you have any questions, please contact our support team.

Best regards,
Exlipa Team
                """
            )
            mail.send(msg)
            flash(f'Registration link resent to {recipient_email}.', 'success')
        except Exception as e:
            print(f"DEBUG: Email resend failed: {str(e)}")
            flash(f'Registration link ready. Email sending disabled for testing.', 'success')
    else:
        flash('No valid email address found. Please provide the registration link manually.', 'warning')

    return redirect(url_for('payment_detail', payment_id=payment_id))

@app.route('/admin/monitoring')
@login_required
def admin_monitoring():
    """System monitoring dashboard"""
    if current_user.role not in ['admin', 'super_admin', 'master_admin']:
        abort(403)
    
    if ENHANCED_FEATURES:
        logger.info("Monitoring dashboard accessed", user_id=current_user.id)
    else:
        logger.info(f"Monitoring dashboard accessed by user {current_user.id}")
    return render_template('admin_monitoring.html')

# POS System Routes
@app.route('/pos')
@login_required
def pos_dashboard():
    """Main POS dashboard"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash(get_translation('No company associated with your account.'), 'danger')
        return redirect(url_for('logout'))
    
    # Check if POS is enabled
    if not company.dynamic_pos_enabled:
        flash('POS feature is not enabled for your account. Please contact admin.', 'warning')
        return redirect(url_for('company_dashboard'))
    
    # Get recent sales and products
    recent_sales = PosSale.query.filter_by(company_id=company.id).order_by(PosSale.created_at.desc()).limit(10).all()
    products = PosProduct.query.filter_by(company_id=company.id, is_active=True).all()
    
    # Calculate today's sales
    today = datetime.now().replace(hour=0, minute=0, second=0)
    today_sales = db.session.query(db.func.sum(PosSale.total_amount)).filter(
        PosSale.company_id == company.id,
        PosSale.created_at >= today
    ).scalar() or 0.0
    
    return render_template('pos_dashboard.html', 
                         company=company,
                         products=products,
                         recent_sales=recent_sales,
                         today_sales=today_sales)

@app.route('/pos/products')
@login_required
def pos_products():
    """POS product management"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company or not company.dynamic_pos_enabled:
        return redirect(url_for('company_dashboard'))
    
    products = PosProduct.query.filter_by(company_id=company.id).order_by(PosProduct.name).all()
    return render_template('pos_products.html', company=company, products=products)

@app.route('/pos/products/new', methods=['GET', 'POST'])
@login_required
def add_pos_product():
    """Add new POS product"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company or not company.dynamic_pos_enabled:
        return redirect(url_for('company_dashboard'))
    
    if request.method == 'POST':
        try:
            product = PosProduct(
                company_id=company.id,
                name=request.form['name'],
                description=request.form.get('description', ''),
                price=float(request.form['price']),
                category=request.form.get('category', ''),
                sku=request.form.get('sku', ''),
                stock_quantity=int(request.form.get('stock_quantity', 0)),
                track_inventory='track_inventory' in request.form,
                low_stock_alert=int(request.form.get('low_stock_alert', 5))
            )
            
            db.session.add(product)
            db.session.commit()
            flash(f'Product "{product.name}" added successfully!', 'success')
            return redirect(url_for('pos_products'))
            
        except Exception as e:
            flash(f'Error adding product: {str(e)}', 'danger')
    
    return render_template('pos_product_form.html', company=company, product=None)

@app.route('/pos/products/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_pos_product(product_id):
    """Edit POS product"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    product = PosProduct.query.filter_by(id=product_id, company_id=company.id).first_or_404()
    
    if request.method == 'POST':
        try:
            product.name = request.form['name']
            product.description = request.form.get('description', '')
            product.price = float(request.form['price'])
            product.category = request.form.get('category', '')
            product.sku = request.form.get('sku', '')
            product.stock_quantity = int(request.form.get('stock_quantity', 0))
            product.track_inventory = 'track_inventory' in request.form
            product.low_stock_alert = int(request.form.get('low_stock_alert', 5))
            product.is_active = 'is_active' in request.form
            
            db.session.commit()
            flash(f'Product "{product.name}" updated successfully!', 'success')
            return redirect(url_for('pos_products'))
            
        except Exception as e:
            flash(f'Error updating product: {str(e)}', 'danger')
    
    return render_template('pos_product_form.html', company=company, product=product)

@app.route('/pos/sale', methods=['POST'])
@login_required
def process_pos_sale():
    """Process a POS sale"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company or not company.dynamic_pos_enabled:
        return jsonify({'error': 'POS not enabled'}), 403
    
    try:
        data = request.get_json()
        
        # Create sale
        payment_method = data.get('payment_method', 'Cash')
        amount_received = float(data.get('amount_received', 0)) if payment_method == 'Cash' else None
        change_given = float(data.get('change_given', 0)) if payment_method == 'Cash' else None
        
        sale = PosSale(
            company_id=company.id,
            sale_number=generate_sale_number(),
            customer_name=data.get('customer_name', ''),
            customer_phone=data.get('customer_phone', ''),
            customer_email=data.get('customer_email', ''),
            subtotal=float(data['subtotal']),
            tax_amount=float(data.get('tax_amount', 0)),
            discount_amount=float(data.get('discount_amount', 0)),
            total_amount=float(data['total_amount']),
            payment_method=payment_method,
            amount_received=amount_received,
            change_given=change_given,
            created_by=current_user.id
        )
        
        # Validate inventory before processing sale
        inventory_errors = []
        for item in data['items']:
            product = PosProduct.query.get(item['id'])
            if not product or product.company_id != company.id:
                inventory_errors.append(f"Product {item.get('name', 'Unknown')} not found")
                continue

            quantity_requested = int(item['quantity'])

            # Check stock availability for tracked products
            if product.track_inventory:
                if product.stock_quantity < quantity_requested:
                    inventory_errors.append(
                        f"Insufficient stock for {product.name}. "
                        f"Available: {product.stock_quantity}, Requested: {quantity_requested}"
                    )

        # Return errors if any inventory issues
        if inventory_errors:
            return jsonify({
                'error': 'Inventory validation failed',
                'details': inventory_errors
            }), 400

        db.session.add(sale)
        db.session.flush()  # Get sale ID

        # Add sale items and update inventory
        for item in data['items']:
            product = PosProduct.query.get(item['id'])
            if not product or product.company_id != company.id:
                continue

            quantity_sold = int(item['quantity'])

            sale_item = PosSaleItem(
                sale_id=sale.id,
                product_id=product.id,
                product_name=product.name,
                unit_price=float(item['price']),
                quantity=quantity_sold,
                total_price=float(item['total'])
            )

            db.session.add(sale_item)

            # Update inventory if tracked (already validated above)
            if product.track_inventory:
                product.stock_quantity -= quantity_sold

                # Log low stock warning
                if product.stock_quantity <= product.low_stock_alert:
                    if ENHANCED_FEATURES:
                        logger.warning(
                            "Low stock alert",
                            product_name=product.name,
                            current_stock=product.stock_quantity,
                            alert_threshold=product.low_stock_alert
                        )
        
        # Record cash drawer transaction if payment is cash
        if payment_method == 'Cash' and amount_received:
            try:
                # Validate cash drawer session
                current_session = validate_cash_drawer_session(company.id, current_user.id)

                # Validate transaction
                validate_cash_transaction(
                    current_session.id,
                    'Sale',
                    float(data['total_amount']),
                    current_user.id
                )

                cash_transaction = CashDrawerTransaction(
                    session_id=current_session.id,
                    transaction_type='Sale',
                    amount=float(data['total_amount']),
                    description=f'Sale #{sale.sale_number}',
                    reference_id=sale.id,
                    created_by=current_user.id
                )
                db.session.add(cash_transaction)

            except ValueError as e:
                # Log cash drawer error but don't fail the sale
                if ENHANCED_FEATURES:
                    logger.warning("Cash drawer validation failed", error=str(e), sale_id=sale.id)
                # Sale continues without cash drawer record
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'sale_number': sale.sale_number,
            'message': 'Sale processed successfully!',
            'change_given': change_given if change_given else 0
        })
        
    except Exception as e:
        db.session.rollback()
        if ENHANCED_FEATURES:
            logger.error("Error processing sale", error=str(e), error_type=str(type(e)))
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/pos/sales')
@login_required
def pos_sales():
    """POS sales history"""
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company or not company.dynamic_pos_enabled:
        return redirect(url_for('company_dashboard'))
    
    page = request.args.get('page', 1, type=int)
    sales = PosSale.query.filter_by(company_id=company.id).order_by(
        PosSale.created_at.desc()
    ).paginate(page=page, per_page=20, error_out=False)
    
    # Calculate summary stats
    today = datetime.now().replace(hour=0, minute=0, second=0)
    this_month = datetime.now().replace(day=1, hour=0, minute=0, second=0)
    
    stats = {
        'today_sales': db.session.query(db.func.sum(PosSale.total_amount)).filter(
            PosSale.company_id == company.id,
            PosSale.created_at >= today
        ).scalar() or 0.0,
        'month_sales': db.session.query(db.func.sum(PosSale.total_amount)).filter(
            PosSale.company_id == company.id,
            PosSale.created_at >= this_month
        ).scalar() or 0.0,
        'total_sales': db.session.query(db.func.sum(PosSale.total_amount)).filter(
            PosSale.company_id == company.id
        ).scalar() or 0.0
    }
    
    return render_template('pos_sales.html', company=company, sales=sales, stats=stats)

# Pricing Tiers Management
@app.route('/admin/pricing-tiers')
@login_required
def admin_pricing_tiers():
    tiers = PricingTier.query.order_by(PricingTier.monthly_fee.asc()).all()
    return render_template('admin_pricing_tiers.html', tiers=tiers)

@app.route('/admin/pricing-tiers/new', methods=['GET', 'POST'])
@login_required
def create_pricing_tier():
    if request.method == 'POST':
        try:
            tier = PricingTier(
                name=request.form['name'],
                description=request.form.get('description', ''),
                setup_fee=float(request.form.get('setup_fee', 0)),
                monthly_fee=float(request.form.get('monthly_fee', 0)),
                annual_fee=float(request.form.get('annual_fee', 0)),
                transaction_fee_percentage=float(request.form.get('transaction_fee_percentage', 0)),
                fixed_transaction_fee=float(request.form.get('fixed_transaction_fee', 0)),
                max_transactions_per_month=int(request.form.get('max_transactions_per_month', 0)),
                max_transaction_amount=float(request.form.get('max_transaction_amount', 0)),
                custom_branding=bool(request.form.get('custom_branding')),
                api_access=bool(request.form.get('api_access')),
                priority_support=bool(request.form.get('priority_support')),
                analytics_dashboard=bool(request.form.get('analytics_dashboard')),
                white_label=bool(request.form.get('white_label')),
                multi_location=bool(request.form.get('multi_location'))
            )
            
            db.session.add(tier)
            db.session.commit()
            flash(f'Pricing tier "{tier.name}" created successfully!')
            return redirect(url_for('admin_pricing_tiers'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating pricing tier: {str(e)}')
    
    return render_template('admin_pricing_tier_form.html', tier=None)

# Billing Management
@app.route('/admin/billing')
@login_required
def admin_billing():
    # Allow admin, super_admin, and master_admin roles
    if current_user.role not in ['admin', 'super_admin', 'master_admin']:
        flash('Access denied.', 'danger')
        return smart_redirect_by_role()

    status_filter = request.args.get('status', 'all')

    query = Bill.query
    if status_filter != 'all':
        query = query.filter_by(status=status_filter)
    
    bills = query.order_by(Bill.created_at.desc()).limit(100).all()
    
    # Calculate summary statistics
    total_pending = Bill.query.filter_by(status='Pending').count()
    total_paid = Bill.query.filter_by(status='Paid').count()
    total_overdue = Bill.query.filter(
        Bill.status == 'Pending',
        Bill.due_date < datetime.now()
    ).count()
    
    monthly_revenue = db.session.query(db.func.sum(Bill.total_amount)).filter(
        Bill.status == 'Paid',
        Bill.created_at >= datetime.now().replace(day=1)
    ).scalar() or 0.0
    
    return render_template('admin_billing.html', 
                         bills=bills, 
                         status_filter=status_filter,
                         total_pending=total_pending,
                         total_paid=total_paid,
                         total_overdue=total_overdue,
                         monthly_revenue=monthly_revenue)

@app.route('/admin/billing/<int:bill_id>')
@login_required
def bill_detail(bill_id):
    bill = Bill.query.get_or_404(bill_id)
    return render_template('admin_bill_detail.html', bill=bill)

@app.route('/admin/billing/<int:bill_id>/mark-paid', methods=['POST'])
@login_required
def mark_bill_paid(bill_id):
    bill = Bill.query.get_or_404(bill_id)
    
    bill.status = 'Paid'
    bill.paid_date = datetime.now()
    bill.payment_method = request.form.get('payment_method', 'Manual')
    bill.payment_reference = request.form.get('payment_reference', '')
    
    # Update company's setup fee status if this was a setup bill
    if bill.bill_type == 'Setup':
        bill.client_company.setup_fee_paid = True
    
    db.session.commit()
    flash('Bill marked as paid successfully!')
    return redirect(url_for('bill_detail', bill_id=bill_id))

@app.route('/admin/billing/generate-monthly')
@login_required
def generate_monthly_bills():
    """Generate monthly bills for all active companies"""
    companies = ClientCompany.query.filter_by(
        subscription_status='Active'
    ).filter(
        ClientCompany.next_billing_date <= datetime.now()
    ).all()
    
    bills_generated = 0
    for company in companies:
        try:
            bill = create_monthly_bill(company)
            if bill:
                bills_generated += 1
                # Update next billing date
                if company.billing_cycle == 'Annual':
                    company.next_billing_date = datetime.now() + timedelta(days=365)
                else:
                    company.next_billing_date = datetime.now() + timedelta(days=30)
                db.session.commit()
        except Exception as e:
            if ENHANCED_FEATURES:
                logger.error("Error generating bill", company=company.company_name, error=str(e))
            db.session.rollback()
    
    flash(f'Generated {bills_generated} monthly bills successfully!')
    return redirect(url_for('admin_billing'))

# User Management Routes
@app.route('/admin/users')
@login_required
def admin_users():
    if current_user.role not in ['super_admin', 'master_admin']:
        flash('Access denied. Master admin privileges required.')
        return smart_redirect_by_role()
    
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('admin_users.html', users=users)

@app.route('/admin/users/new', methods=['GET', 'POST'])
@login_required
def create_user():
    if current_user.role != 'super_admin':
        flash('Access denied. Super admin privileges required.')
        return smart_redirect_by_role()
    
    if request.method == 'POST':
        try:
            # Check if username already exists
            existing_user = User.query.filter_by(username=request.form['username']).first()
            if existing_user:
                flash('Username already exists. Please choose a different username.')
                return render_template('admin_user_form.html', user=None)
            
            # Check if email already exists
            if request.form.get('email'):
                existing_email = User.query.filter_by(email=request.form['email']).first()
                if existing_email:
                    flash('Email already exists. Please use a different email.')
                    return render_template('admin_user_form.html', user=None)
            
            user = User(
                username=request.form['username'],
                password_hash=generate_password_hash(request.form['password']),
                email=request.form.get('email', ''),
                full_name=request.form.get('full_name', ''),
                role=request.form.get('role', 'admin'),
                created_by=current_user.id
            )
            
            db.session.add(user)
            db.session.commit()
            flash(f'User "{user.username}" created successfully!')
            return redirect(url_for('admin_users'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating user: {str(e)}')
    
    return render_template('admin_user_form.html', user=None)

@app.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'super_admin':
        flash('Access denied. Super admin privileges required.')
        return smart_redirect_by_role()
    
    user = User.query.get_or_404(user_id)
    
    # Prevent editing super admin by regular admin
    if user.role == 'super_admin' and current_user.id != user.id:
        flash('Cannot edit super admin account.')
        return redirect(url_for('admin_users'))
    
    if request.method == 'POST':
        try:
            # Check username uniqueness (excluding current user)
            existing_user = User.query.filter(
                User.username == request.form['username'],
                User.id != user.id
            ).first()
            if existing_user:
                flash('Username already exists. Please choose a different username.')
                return render_template('admin_user_form.html', user=user)
            
            # Check email uniqueness (excluding current user)
            if request.form.get('email'):
                existing_email = User.query.filter(
                    User.email == request.form['email'],
                    User.id != user.id
                ).first()
                if existing_email:
                    flash('Email already exists. Please use a different email.')
                    return render_template('admin_user_form.html', user=user)
            
            user.username = request.form['username']
            user.email = request.form.get('email', '')
            user.full_name = request.form.get('full_name', '')
            
            # Only allow role change if current user is super admin
            if current_user.role == 'super_admin':
                user.role = request.form.get('role', 'admin')
            
            # Update password if provided
            if request.form.get('password'):
                user.password_hash = generate_password_hash(request.form['password'])
            
            db.session.commit()
            flash(f'User "{user.username}" updated successfully!')
            return redirect(url_for('admin_users'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating user: {str(e)}')
    
    return render_template('admin_user_form.html', user=user)

@app.route('/admin/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    if current_user.role != 'super_admin':
        return {'success': False, 'error': 'Access denied'}, 403
    
    user = User.query.get_or_404(user_id)
    
    # Prevent disabling super admin
    if user.role == 'super_admin':
        return {'success': False, 'error': 'Cannot disable super admin account'}, 400
    
    # Prevent users from disabling themselves
    if user.id == current_user.id:
        return {'success': False, 'error': 'Cannot disable your own account'}, 400
    
    try:
        user.is_active = not user.is_active
        db.session.commit()
        
        status = 'enabled' if user.is_active else 'disabled'
        return {'success': True, 'message': f'User {status} successfully'}
        
    except Exception as e:
        db.session.rollback()
        return {'success': False, 'error': str(e)}, 500

@app.route('/admin/profile', methods=['GET', 'POST'])
@login_required
def admin_profile():
    if request.method == 'POST':
        try:
            # Check if email is being changed and is unique
            if request.form.get('email') and request.form['email'] != current_user.email:
                existing_email = User.query.filter(
                    User.email == request.form['email'],
                    User.id != current_user.id
                ).first()
                if existing_email:
                    flash('Email already exists. Please use a different email.')
                    return render_template('admin_profile.html')
            
            current_user.email = request.form.get('email', '')
            current_user.full_name = request.form.get('full_name', '')
            
            # Update password if provided
            if request.form.get('current_password') and request.form.get('new_password'):
                if check_password_hash(current_user.password_hash, request.form['current_password']):
                    current_user.password_hash = generate_password_hash(request.form['new_password'])
                    flash('Password updated successfully!')
                else:
                    flash('Current password is incorrect.')
                    return render_template('admin_profile.html')
            
            db.session.commit()
            flash('Profile updated successfully!')
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating profile: {str(e)}')
    
    return render_template('admin_profile.html')

@app.route('/onboarding', methods=['GET', 'POST'])
@login_required
def onboarding():
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if request.method == 'POST':
        company.primary_color = request.form.get('primary_color', company.primary_color)
        company.secondary_color = request.form.get('secondary_color', company.secondary_color)
        company.mpesa_till = request.form.get('mpesa_till')
        company.tigo_paybill = request.form.get('tigo_paybill')
        company.airtel_merchant = request.form.get('airtel_merchant')
        company.crdb_merchant = request.form.get('crdb_merchant')
        # Handle team invites
        team_emails = request.form.get('team_emails', '')
        invited = []
        if team_emails:
            emails = [e.strip() for e in team_emails.split(',') if e.strip()]
            for email in emails:
                if not User.query.filter_by(email=email).first():
                    temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
                    user = User(
                        username=email,
                        email=email,
                        full_name='',
                        password_hash=generate_password_hash(temp_password),
                        role='company_user',
                        is_active=True,
                        created_at=datetime.utcnow(),
                        created_by=current_user.id
                    )
                    db.session.add(user)
                    invited.append((email, temp_password))
            db.session.commit()
        if invited:
            flash('Team members invited: ' + ', '.join([f'{e} (temp password: {p})' for e, p in invited]), 'info')
        db.session.commit()
        flash('Onboarding complete!','success')
        return redirect(url_for('company_dashboard'))
    return render_template('onboarding.html', company=company)

# Update register route to redirect to onboarding
@app.route('/register', methods=['GET', 'POST'])
def register():
    token = request.args.get('token')
    if not token:
        flash('Registration is only available after payment confirmation. Please pay and confirm your payment first.', 'warning')
        return redirect(url_for('index'))
    try:
        # Validate token (expires in 24 hours)
        payment_id = serializer.loads(token, salt='register', max_age=86400)
        payment = PaymentConfirmation.query.get(payment_id)
        if not payment or payment.status != 'Confirmed':
            flash('Invalid or unconfirmed payment. Please contact support.', 'danger')
            return redirect(url_for('index'))
    except Exception:
        flash('Your registration link is invalid or has expired. Please check your payment status or contact support.', 'danger')
        return redirect(url_for('check_payment_status'))

    if request.method == 'POST':
        full_name = request.form['full_name']
        email = request.form['email']
        password = request.form['password']
        company_name = request.form['company_name']
        company_phone = request.form.get('company_phone')
        company_website = request.form.get('company_website')

        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            # User exists - offer to link payment to existing account
            flash(f'An account with email {email} already exists. Please login to your existing account.', 'info')
            return render_template('register.html',
                                 existing_user=True,
                                 login_url=url_for('company_login'),
                                 email=email)

        # Check if company name already exists
        if ClientCompany.query.filter_by(company_name=company_name).first():
            flash('A company with that name already exists. Please choose a different name.', 'danger')
            return render_template('register.html')

        # ASSIGN PRICING TIER BASED ON PAYMENT
        # Get the pricing tier from the payment confirmation
        pricing_tier_id = None
        if payment.pricing_tier_id:
            pricing_tier_id = payment.pricing_tier_id
            print(f"DEBUG: Assigning tier {payment.pricing_tier_id} to new company based on payment")
        else:
            # Fallback: determine tier from payment amount
            payment_amount = float(payment.amount)
            all_tiers = PricingTier.query.all()
            for tier in all_tiers:
                total_tier_cost = tier.setup_fee + tier.monthly_fee
                if abs(payment_amount - total_tier_cost) <= 1000:
                    pricing_tier_id = tier.id
                    print(f"DEBUG: Determined tier {tier.name} from payment amount TSh {payment_amount:,.0f}")
                    break

        # Create company with assigned pricing tier
        company = ClientCompany(
            company_name=company_name,
            company_phone=company_phone,
            company_website=company_website,
            company_email=email,
            pricing_tier_id=pricing_tier_id,  # Assign the tier based on payment
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.session.add(company)
        db.session.commit()

        # Log the tier assignment
        if pricing_tier_id:
            tier = PricingTier.query.get(pricing_tier_id)
            print(f"DEBUG: Company '{company_name}' created with {tier.name} tier (ID: {pricing_tier_id})")
        else:
            print(f"DEBUG: Company '{company_name}' created without tier assignment")

        # Create user (role: user_admin - company customer with paid tier)
        user = User(
            username=email,
            email=email,
            full_name=full_name,
            password_hash=generate_password_hash(password),
            role='user_admin',  # Company customer role (paid tier)
            is_active=True,
            created_at=datetime.utcnow(),
            created_by=None
        )
        db.session.add(user)
        db.session.commit()

        login_user(user)
        flash('Registration successful! Welcome to Exlipa.', 'success')
        return redirect(url_for('onboarding'))
    return render_template('register.html')

@app.route('/check-payment-status', methods=['GET', 'POST'])
def check_payment_status():
    """Allow customers to check their payment status and get registration links"""
    if request.method == 'POST':
        reference_id = request.form.get('reference_id', '').strip()
        transaction_id = request.form.get('transaction_id', '').strip()
        customer_name = request.form.get('customer_name', '').strip()

        if not reference_id and not transaction_id:
            flash('Please provide either a reference ID or transaction ID.', 'warning')
            return render_template('check_payment_status.html')

        # Search for payment confirmation
        payment = None
        if reference_id:
            # Extract ID from reference (REF000001 -> 1)
            try:
                payment_id = int(reference_id.replace('REF', '').lstrip('0'))
                payment = PaymentConfirmation.query.get(payment_id)
            except ValueError:
                pass

        if not payment and transaction_id:
            payment = PaymentConfirmation.query.filter_by(transaction_id=transaction_id).first()

        if not payment:
            flash('Payment not found. Please check your reference ID or transaction ID.', 'danger')
            return render_template('check_payment_status.html')

        # Verify customer name matches (basic security)
        if customer_name.lower() not in payment.customer_name.lower():
            flash('Customer name does not match our records.', 'danger')
            return render_template('check_payment_status.html')

        # Generate new registration link if payment is confirmed
        register_url = None
        if payment.status == 'Confirmed':
            token = serializer.dumps(payment.id, salt='register')
            register_url = url_for('register', token=token, _external=True)

        return render_template('payment_status_result.html',
                             payment=payment,
                             register_url=register_url,
                             reference_id=f"REF{payment.id:06d}")

    return render_template('check_payment_status.html')

@app.route('/company-dashboard')
@login_required
def company_dashboard():
    # Only allow company users
    if current_user.role != 'company_user':
        return smart_redirect_by_role()
    # Fetch company info by user email (isolation)
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect(url_for('logout'))
    return render_template('company_dashboard.html', company=company)

@app.route('/company-profile', methods=['GET', 'POST'])
@login_required
def company_profile():
    """Company users and user admins can edit their own company profile"""
    if current_user.role not in ['company_user', 'user_admin', 'admin']:
        return smart_redirect_by_role()
    
    # Fetch company info by user email (isolation)
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        if current_user.role == 'user_admin':
            return redirect('/user-admin')
        else:
            return redirect(url_for('logout'))
    
    if request.method == 'POST':
        try:
            # Company users can edit basic profile information only
            company.company_name = request.form.get('company_name', company.company_name)
            company.company_address = request.form.get('company_address', company.company_address)
            company.company_phone = request.form.get('company_phone', company.company_phone)
            company.company_website = request.form.get('company_website', company.company_website)
            
            # Brand colors
            company.primary_color = request.form.get('primary_color', company.primary_color)
            company.secondary_color = request.form.get('secondary_color', company.secondary_color)
            
            # Payment method configurations (existing fields)
            company.mpesa_till = request.form.get('mpesa_till', company.mpesa_till)
            company.tigo_paybill = request.form.get('tigo_paybill', company.tigo_paybill)
            company.airtel_merchant = request.form.get('airtel_merchant', company.airtel_merchant)
            company.crdb_merchant = request.form.get('crdb_merchant', company.crdb_merchant)
            
            db.session.commit()
            flash(get_translation('Company profile updated successfully!'), 'success')
            if current_user.role == 'user_admin':
                return redirect('/user-admin')
            else:
                return redirect(url_for('company_dashboard'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating profile: {str(e)}', 'danger')
    
    return render_template('company_profile.html', company=company)

@app.route('/test-landing')
def test_landing():
    """Simple test route"""
    print("DEBUG: Test landing route accessed")
    return "<h1>Test Landing Page</h1><p>This route is working!</p>"

def get_landing_page_features(pricing_tier):
    """Get available landing page features based on actual PricingTier object"""
    if not pricing_tier:
        # Default to Starter if no tier assigned
        tier_name = 'Starter'
        tier_id = None
        monthly_fee = 75000
        setup_fee = 200000
    else:
        tier_name = pricing_tier.name
        tier_id = pricing_tier.id
        monthly_fee = pricing_tier.monthly_fee
        setup_fee = pricing_tier.setup_fee

    # Base features for all tiers (JSON serializable only)
    base_features = {
        'tier_name': tier_name,
        'tier_id': tier_id,
        'monthly_fee': monthly_fee,
        'setup_fee': setup_fee,
        'templates': ['basic', 'clean', 'simple'],
        'colors': ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'],
        'fonts': ['Inter'],
        'custom_colors': False,
        'layout_controls': False,
        'backgrounds': ['solid'],
        'social_media': False,
        'testimonials': False,
        'seo_basic': False,
        'analytics_basic': False,
        'custom_css': False,
        'animations': False,
        'video_bg': False,
        'ab_testing': False,
        'multilingual': False,
        'custom_domain': False,
        'white_label': False,
        'api_access': False
    }

    # Enhance features based on tier
    if tier_name == 'Business':
        base_features.update({
            'templates': ['basic', 'clean', 'simple', 'modern', 'restaurant', 'retail', 'service', 'professional'],
            'colors': 'unlimited',
            'fonts': ['Inter', 'Poppins', 'Roboto', 'Open Sans', 'Lato', 'Montserrat', 'Playfair Display', 'Merriweather', 'Source Sans Pro', 'Nunito'],
            'custom_colors': True,
            'layout_controls': True,
            'backgrounds': ['solid', 'gradient'],
            'social_media': True,
            'testimonials': True,
            'seo_basic': True,
            'analytics_basic': pricing_tier.analytics_dashboard if pricing_tier else True,
            'custom_branding': pricing_tier.custom_branding if pricing_tier else True,
            'api_access': pricing_tier.api_access if pricing_tier else True,
        })
    elif tier_name == 'Enterprise':
        base_features.update({
            'templates': 'unlimited',
            'colors': 'unlimited',
            'fonts': 'unlimited',
            'custom_colors': True,
            'layout_controls': True,
            'backgrounds': ['solid', 'gradient', 'image', 'video'],
            'social_media': True,
            'testimonials': True,
            'seo_basic': True,
            'seo_advanced': True,
            'analytics_basic': pricing_tier.analytics_dashboard if pricing_tier else True,
            'analytics_advanced': True,
            'custom_css': True,
            'custom_js': True,
            'animations': True,
            'video_bg': True,
            'ab_testing': True,
            'multilingual': True,
            'custom_domain': True,
            'white_label': pricing_tier.white_label if pricing_tier else True,
            'api_access': pricing_tier.api_access if pricing_tier else True,
            'custom_branding': pricing_tier.custom_branding if pricing_tier else True,
            'priority_support': pricing_tier.priority_support if pricing_tier else True,
            'multi_location': pricing_tier.multi_location if pricing_tier else True,
        })

    return base_features

@app.route('/advanced-landing/<int:company_id>', methods=['GET', 'POST'])
@login_required
def advanced_landing(company_id):
    """Advanced landing page builder with tier-based features"""
    print(f"DEBUG: Advanced landing builder accessed - Method: {request.method}, Company ID: {company_id}")

    if current_user.role != 'user_admin':
        flash('Access denied. Only company customers can access landing page builder.', 'danger')
        return redirect(url_for('user_admin_login'))

    company = ClientCompany.query.get_or_404(company_id)

    # Get company's actual pricing tier from database relationship
    pricing_tier = None
    tier_name = 'Starter'  # Default

    try:
        if hasattr(company, 'pricing_tier_id') and company.pricing_tier_id:
            pricing_tier = PricingTier.query.get(company.pricing_tier_id)
            if pricing_tier:
                tier_name = pricing_tier.name
                print(f"DEBUG: Found pricing tier from database: {tier_name}")
            else:
                print(f"DEBUG: Pricing tier ID {company.pricing_tier_id} not found, using Starter")
        else:
            print(f"DEBUG: No pricing tier assigned to company, using Starter")
    except Exception as e:
        print(f"DEBUG: Error getting pricing tier: {e}, using Starter")

    available_features = get_landing_page_features(pricing_tier)
    available_templates = get_available_templates(tier_name)

    print(f"DEBUG: Company: {company.company_name}")
    print(f"DEBUG: Pricing tier: {tier_name}")
    print(f"DEBUG: Available features: {list(available_features.keys())}")
    print(f"DEBUG: Available templates: {len(available_templates)}")

    if request.method == 'POST':
        # Handle advanced customization data
        try:
            # Basic settings
            company.is_public_page_enabled = request.form.get('is_public_page_enabled') == 'on'
            company.landing_page_title = request.form.get('landing_page_title', '')
            company.landing_page_description = request.form.get('landing_page_description', '')
            company.custom_message = request.form.get('custom_message', '')

            # Advanced customization settings
            company.primary_color = request.form.get('primary_color', '#3b82f6')
            company.secondary_color = request.form.get('secondary_color', '#1d4ed8')
            company.font_family = request.form.get('font_family', 'Inter')
            company.template_style = request.form.get('template_style', 'modern')

            db.session.commit()
            flash('Advanced landing page updated successfully!', 'success')
            return redirect(url_for('advanced_landing', company_id=company_id))
        except Exception as e:
            flash(f'Error updating landing page: {str(e)}', 'danger')

    return render_template('tier_based_landing_builder.html',
                         company=company,
                         available_features=available_features,
                         available_templates=available_templates,
                         tier_name=tier_name)

@app.route('/set-company-tier/<int:company_id>/<tier_name>')
@login_required
def set_company_tier(company_id, tier_name):
    """Set company pricing tier for testing - assigns actual PricingTier object"""
    if current_user.role != 'super_admin':
        flash('Access denied. Only EXLIPA administrators can set company tiers.', 'danger')
        return redirect(url_for('master_admin_dashboard'))

    company = ClientCompany.query.get_or_404(company_id)

    if tier_name in ['Free', 'Business', 'Enterprise']:
        # Find the actual PricingTier object
        pricing_tier = PricingTier.query.filter_by(name=tier_name).first()
        if pricing_tier:
            company.pricing_tier_id = pricing_tier.id
            # Reset usage tracking for new tier
            company.monthly_transaction_count = 0
            company.free_transactions_used = 0
            company.last_transaction_reset = datetime.utcnow()
            db.session.commit()
            flash(f'Company tier updated to {tier_name}! (Monthly: TSh {pricing_tier.monthly_fee:,.0f}, Transaction Fee: {pricing_tier.transaction_fee_percentage}%)', 'success')
            print(f"DEBUG: Updated company {company.company_name} to tier {tier_name} (ID: {pricing_tier.id})")
        else:
            flash(f'Pricing tier {tier_name} not found in database.', 'danger')
    else:
        flash('Invalid tier name. Must be Free, Business, or Enterprise.', 'danger')

    return redirect(url_for('advanced_landing', company_id=company_id))

@app.route('/request-upgrade')
@login_required
def request_upgrade():
    """Allow user admins to request tier upgrades"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    # Get user's company
    user_company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not user_company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Get current usage status
    usage_status = get_tier_limits_status(user_company)
    next_tier = usage_status.get('next_tier')

    if not next_tier:
        flash('You are already on the highest tier!', 'info')
        return redirect(url_for('user_admin_dashboard'))

    # Redirect to pricing page with upgrade context
    flash(f'Ready to upgrade to {next_tier}? Choose your plan below.', 'info')
    return redirect(url_for('pricing'))

@app.route('/analytics')
@app.route('/analytics/<int:company_id>')
@login_required
def analytics_dashboard(company_id=None):
    """Analytics dashboard for companies"""
    # Determine which company to show analytics for
    if current_user.role == 'super_admin':
        if company_id:
            company = ClientCompany.query.get_or_404(company_id)
        else:
            # Show platform-wide analytics for super admin
            return render_template('platform_analytics.html')
    elif current_user.role == 'user_admin':
        # User admin can only see their own company analytics
        company = ClientCompany.query.filter_by(company_email=current_user.email).first()
        if not company:
            flash('No company associated with your account.', 'warning')
            return redirect(url_for('user_admin_dashboard'))
    else:
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    # Get analytics period from query parameter (default 30 days)
    days = request.args.get('days', 30, type=int)
    if days not in [7, 30, 90, 365]:
        days = 30

    # Get analytics data
    analytics = get_company_analytics(company.id, days)
    usage_status = get_tier_limits_status(company)

    return render_template('analytics_dashboard.html',
                         company=company,
                         analytics=analytics,
                         usage_status=usage_status,
                         selected_period=days)

@app.route('/api-management')
@login_required
def api_management():
    """API key management for Business and Enterprise tiers"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    # Get user's company
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Check if company has API access
    has_api_access = False
    if company.pricing_tier_id:
        tier = PricingTier.query.get(company.pricing_tier_id)
        has_api_access = tier and tier.api_access

    return render_template('api_management.html',
                         company=company,
                         has_api_access=has_api_access)

@app.route('/api-management/generate-keys', methods=['POST'])
@login_required
def generate_api_keys():
    """Generate new API keys for a company"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Check if company has API access
    if company.pricing_tier_id:
        tier = PricingTier.query.get(company.pricing_tier_id)
        if not tier or not tier.api_access:
            flash('API access is not available for your current tier. Please upgrade to Business or Enterprise.', 'warning')
            return redirect(url_for('api_management'))
    else:
        flash('Please select a pricing tier first.', 'warning')
        return redirect(url_for('api_management'))

    # Generate new API credentials
    company.api_key = generate_api_key()
    company.api_secret = generate_api_secret()
    company.api_enabled = True

    db.session.commit()

    flash('New API credentials generated successfully!', 'success')
    return redirect(url_for('api_management'))

@app.route('/api-management/toggle', methods=['POST'])
@login_required
def toggle_api_access():
    """Enable or disable API access"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Toggle API access
    company.api_enabled = not company.api_enabled
    db.session.commit()

    status = 'enabled' if company.api_enabled else 'disabled'
    flash(f'API access {status} successfully!', 'success')
    return redirect(url_for('api_management'))

@app.route('/mobile-dashboard')
@login_required
def mobile_dashboard():
    """Mobile-optimized dashboard for PWA"""
    # Get user's company if they have one
    user_company = None
    usage_status = None

    if current_user.role == 'user_admin':
        user_company = ClientCompany.query.filter_by(company_email=current_user.email).first()

    if user_company:
        # Get usage status for the company
        usage_status = get_tier_limits_status(user_company)

    return render_template('mobile_dashboard.html',
                         user_company=user_company,
                         usage_status=usage_status)

@app.route('/offline')
def offline():
    """Offline page for PWA"""
    return render_template('offline.html')

@app.route('/onboarding')
@login_required
def onboarding_wizard():
    """User onboarding wizard for new users"""
    return render_template('onboarding_wizard.html')

@app.route('/welcome')
@login_required
def welcome_new_user():
    """Welcome page for newly registered users"""
    # Check if user is new (created within last 24 hours)
    from datetime import datetime, timedelta

    if current_user.created_at and current_user.created_at > datetime.now() - timedelta(hours=24):
        # New user - show onboarding
        return redirect(url_for('onboarding_wizard'))
    else:
        # Existing user - go to dashboard
        if current_user.role == 'user_admin':
            return redirect(url_for('user_admin_dashboard'))
        else:
            return redirect(url_for('master_admin_dashboard'))

@app.route('/admin/performance')
@login_required
def performance_dashboard():
    """Performance monitoring dashboard for admins"""
    if current_user.role != 'super_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    if not PERFORMANCE_ENABLED:
        flash('Performance monitoring not available.', 'warning')
        return redirect(url_for('master_admin_dashboard'))

    stats = perf_monitor.get_performance_stats()

    return render_template('performance_dashboard.html', stats=stats)

@app.route('/webhooks')
@login_required
def webhook_management():
    """Webhook management for user admins"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    if not ADVANCED_FEATURES_ENABLED:
        flash('Advanced features not available.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Get user's company
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    # Check if company has webhook access (Business/Enterprise tiers)
    has_webhook_access = False
    if company.pricing_tier_id:
        tier = PricingTier.query.get(company.pricing_tier_id)
        has_webhook_access = tier and tier.name in ['Business', 'Enterprise']

    webhook_stats = webhook_manager.get_webhook_stats(company.id) if has_webhook_access else {}

    return render_template('webhook_management.html',
                         company=company,
                         has_webhook_access=has_webhook_access,
                         webhook_stats=webhook_stats)

@app.route('/webhooks/create', methods=['POST'])
@login_required
def create_webhook():
    """Create a new webhook"""
    if current_user.role != 'user_admin' or not ADVANCED_FEATURES_ENABLED:
        return jsonify({'error': 'Access denied'}), 403

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        return jsonify({'error': 'Company not found'}), 404

    data = request.get_json()
    url = data.get('url')
    events = data.get('events', [])
    secret = data.get('secret')

    if not url or not events:
        return jsonify({'error': 'URL and events are required'}), 400

    # Convert event strings to enum values
    try:
        event_types = [WebhookEventType(event) for event in events]
    except ValueError as e:
        return jsonify({'error': f'Invalid event type: {e}'}), 400

    webhook_config = webhook_manager.register_webhook(
        company_id=company.id,
        url=url,
        events=event_types,
        secret=secret
    )

    return jsonify({
        'success': True,
        'webhook': {
            'url': webhook_config['url'],
            'events': [e.value for e in webhook_config['events']],
            'created_at': webhook_config['created_at'].isoformat()
        }
    })

@app.route('/bulk-operations')
@login_required
def bulk_operations():
    """Bulk operations interface"""
    if current_user.role != 'user_admin':
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    if not ADVANCED_FEATURES_ENABLED:
        flash('Advanced features not available.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'warning')
        return redirect(url_for('user_admin_dashboard'))

    return render_template('bulk_operations.html', company=company)

@app.route('/simple-landing/<int:company_id>', methods=['GET', 'POST'])
@login_required
def simple_landing(company_id):
    """Simple landing page editor that definitely works"""
    print(f"DEBUG: Simple landing route accessed - Method: {request.method}, Company ID: {company_id}")
    print(f"DEBUG: Current user: {current_user.username} (role: {current_user.role})")

    if current_user.role not in ['company_user', 'admin', 'user_admin']:
        flash('Access denied.', 'danger')
        return redirect(url_for('user_admin_dashboard'))

    company = ClientCompany.query.get_or_404(company_id)
    print(f"DEBUG: Company found: {company.company_name}")

    if request.method == 'POST':
        print(f"DEBUG: POST request received")
        print(f"DEBUG: Form data: {dict(request.form)}")

        try:
            company.is_public_page_enabled = request.form.get('is_public_page_enabled') == 'on'
            company.landing_page_title = request.form.get('landing_page_title', '')
            company.landing_page_description = request.form.get('landing_page_description', '')
            company.custom_message = request.form.get('custom_message', '')

            db.session.commit()
            print("DEBUG: Database updated successfully")
            flash('Landing page updated successfully!', 'success')
            return redirect(url_for('simple_landing', company_id=company_id))
        except Exception as e:
            print(f"DEBUG: Error updating: {e}")
            flash(f'Error updating landing page: {str(e)}', 'danger')

    return render_template('simple_landing.html', company=company)

@app.route('/company-landing', methods=['GET', 'POST'])
@app.route('/company-landing/<int:company_id>', methods=['GET', 'POST'])
@login_required
def company_landing(company_id=None):
    """REDIRECT: Old landing page route - redirects to new tier-based system"""
    print(f"DEBUG: OLD company_landing route accessed - redirecting to advanced_landing")
    print(f"DEBUG: Current user: {current_user.username} (role: {current_user.role})")

    if current_user.role != 'user_admin':
        print(f"DEBUG: Access denied for role: {current_user.role}")
        flash('Access denied. Only company customers can access landing pages.', 'danger')
        return smart_redirect_by_role()

    # Determine company ID for redirect
    if not company_id:
        if current_user.role == 'user_admin':
            # For user admin (company customer), get their company
            company = ClientCompany.query.filter_by(company_email=current_user.email).first()
            company_id = company.id if company else 1
        else:
            # Fallback
            company_id = 1

    # Redirect to new tier-based system
    flash('Redirected to new advanced landing page builder!', 'info')
    return redirect(url_for('advanced_landing', company_id=company_id))

    # This route now only redirects - no form processing needed

# Password reset token serializer
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

@app.route('/reset-request', methods=['GET', 'POST'])
def reset_request():
    if request.method == 'POST':
        email = request.form['email']
        user = User.query.filter_by(email=email, is_active=True).first()
        if user:
            token = serializer.dumps(user.email, salt='password-reset')
            reset_url = url_for('reset_password', token=token, _external=True)
            # TODO: Send reset_url via email in production
            flash(f'Reset link: {reset_url}', 'info')
        else:
            flash('If the email exists, a reset link will be sent.', 'info')
        return redirect(url_for('reset_request'))
    return render_template('reset_request.html')

@app.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    try:
        email = serializer.loads(token, salt='password-reset', max_age=3600)
    except Exception:
        flash('The reset link is invalid or has expired.', 'danger')
        return redirect(url_for('reset_request'))
    user = User.query.filter_by(email=email, is_active=True).first()
    if not user:
        flash('Invalid user.', 'danger')
        return redirect(url_for('reset_request'))
    if request.method == 'POST':
        password = request.form['password']
        confirm = request.form['confirm_password']
        if password != confirm:
            flash('Passwords do not match.', 'danger')
            return render_template('reset_password.html')
        user.password_hash = generate_password_hash(password)
        db.session.commit()
        flash('Password has been reset. You can now log in.', 'success')
        return redirect(url_for('user_admin_login'))
    return render_template('reset_password.html')

@app.route('/team', methods=['GET'])
@login_required
def team_management():
    if current_user.role != 'user_admin':
        flash('Access denied. Only company customers can manage team members.', 'danger')
        return smart_redirect_by_role()
    # Find company by user email
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect(url_for('logout'))
    # All users with same company_email (simple multi-user per company logic)
    team_members = User.query.filter_by(email=company.company_email, role='company_user').all()
    return render_template('team_management.html', team_members=team_members)

@app.route('/team/invite', methods=['POST'])
@login_required
def invite_team_member():
    if current_user.role != 'user_admin':
        flash('Access denied. Only company customers can invite team members.', 'danger')
        return smart_redirect_by_role()
    email = request.form['email'].strip().lower()
    if not email:
        flash('Email is required.', 'danger')
        return redirect(url_for('team_management'))
    if User.query.filter_by(email=email).first():
        flash('User already exists.', 'warning')
        return redirect(url_for('team_management'))
    temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    user = User(
        username=email,
        email=email,
        full_name='',
        password_hash=generate_password_hash(temp_password),
        role='user_admin',  # Company customer role (team member)
        is_active=True,
        created_at=datetime.utcnow(),
        created_by=current_user.id
    )
    db.session.add(user)
    db.session.commit()
    flash(f'Invited {email} (temp password: {temp_password})', 'success')
    return redirect(url_for('team_management'))

@app.route('/team/remove/<int:user_id>', methods=['POST'])
@login_required
def remove_team_member(user_id):
    if current_user.role not in ['company_user', 'admin']:
        return smart_redirect_by_role()
    user = User.query.get_or_404(user_id)
    # Only allow removing users with same company email
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company or user.email != company.company_email or user.id == current_user.id:
        flash('Cannot remove this user.', 'danger')
        return redirect(url_for('team_management'))
    db.session.delete(user)
    db.session.commit()
    flash('User removed.', 'success')
    return redirect(url_for('team_management'))

@app.route('/pricing')
def pricing():
    # Show clean, simplified pricing tiers
    pricing_tiers = PricingTier.query.order_by(PricingTier.monthly_fee).all()
    return render_template('clean_pricing.html', pricing_tiers=pricing_tiers)

@app.route('/pricing/detailed')
def detailed_pricing():
    # Show comprehensive pricing tiers (detailed version)
    pricing_tiers = PricingTier.query.order_by(PricingTier.monthly_fee).all()
    return render_template('comprehensive_pricing.html', pricing_tiers=pricing_tiers)

# Contact Page
@app.route('/contact', methods=['GET', 'POST'])
def contact():
    """Contact page and form submission"""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            subject = request.form.get('subject', '').strip()
            message = request.form.get('message', '').strip()

            # Validate required fields
            if not all([name, email, subject, message]):
                flash(t('Please fill in all required fields'), 'error')
                return render_template('contact.html')

            # Validate email format
            from utils.validators import InputValidator, ValidationError
            try:
                email = InputValidator.validate_email(email)
            except ValidationError:
                flash(t('Please enter a valid email address'), 'error')
                return render_template('contact.html')

            # Log the contact form submission
            logger.info(f"Contact form submitted by {name} ({email}) - Subject: {subject}")

            # In a real application, you would:
            # 1. Save to database
            # 2. Send email notification to support team
            # 3. Send confirmation email to user

            # For now, just show success message
            flash(t('Thank you for your message! We will get back to you within 24 hours.'), 'success')
            return redirect(url_for('contact'))

        except Exception as e:
            logger.error(f"Contact form error: {str(e)}")
            flash(t('Sorry, there was an error sending your message. Please try again.'), 'error')
            return render_template('contact.html')

    return render_template('contact.html')

@app.route('/pricing/original')
def original_pricing():
    # Show original pricing tiers
    pricing_tiers = PricingTier.query.order_by(PricingTier.monthly_fee).all()
    return render_template('pricing.html', pricing_tiers=pricing_tiers)

@app.route('/debug-signup/<int:tier_id>')
def debug_signup(tier_id):
    """Debug version of signup route"""
    try:
        tier = PricingTier.query.get_or_404(tier_id)
        return f"""
        <h1>Debug Signup for Tier {tier_id}</h1>
        <p>Tier Name: {tier.name}</p>
        <p>Setup Fee: {tier.setup_fee}</p>
        <p>Monthly Fee: {tier.monthly_fee}</p>
        <p>Total: {tier.setup_fee + tier.monthly_fee}</p>
        <p><a href="/start-signup/{tier_id}">Try Real Signup</a></p>
        """
    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/start-signup/<int:tier_id>', methods=['GET', 'POST'])
def start_signup(tier_id):
    # Show payment instructions for the selected package
    try:
        tier = PricingTier.query.get_or_404(tier_id)
    except Exception as e:
        flash(f'Error loading pricing tier: {str(e)}', 'danger')
        return redirect(url_for('pricing'))

    # For Free tier, redirect to direct registration
    if tier.name == 'Free' or tier.monthly_fee == 0:
        return redirect(url_for('free_signup'))

    # For GET requests, just render the template
    if request.method == 'GET':
        try:
            return render_template('start_signup.html', tier=tier)
        except Exception as e:
            flash(f'Template error: {str(e)}', 'danger')
            return redirect(url_for('pricing'))

    if request.method == 'POST':
        try:
            # Collect and validate payment details
            customer_name = sanitize_input(request.form.get('customer_name', ''), 100)
            customer_email = sanitize_input(request.form.get('customer_email', ''), 120)
            mobile_money_sender_name = sanitize_input(request.form.get('mobile_money_sender_name', ''), 100)
            mobile_operator = sanitize_input(request.form.get('mobile_operator', ''), 50)
            transaction_id = sanitize_input(request.form.get('transaction_id', ''), 100)

            # Validation
            if not customer_name:
                flash('Customer name is required', 'danger')
                return render_template('start_signup.html', tier=tier)

            if not customer_email:
                flash('Email address is required', 'danger')
                return render_template('start_signup.html', tier=tier)

            if not mobile_money_sender_name:
                flash('Mobile money sender name is required', 'danger')
                return render_template('start_signup.html', tier=tier)

            # Calculate amount and validate
            amount = tier.setup_fee + tier.monthly_fee
            amount = validate_payment_amount(amount)

            # Validate transaction ID
            transaction_id = validate_transaction_id(transaction_id, mobile_operator)

            # Validate mobile money name
            mobile_money_sender_name = validate_mobile_money_name(mobile_money_sender_name)

            service_description = f"Signup for {tier.name} package"

            # Create payment confirmation
            # For signup payments, we need to satisfy the check_payment_association constraint
            # We'll use a special signup invoice or create one if it doesn't exist
            from datetime import datetime

            # Get or create a special "SIGNUP" invoice for all signup payments
            signup_invoice = Invoice.query.filter_by(invoice_number='SIGNUP-GENERAL').first()
            if not signup_invoice:
                signup_invoice = Invoice(
                    invoice_number='SIGNUP-GENERAL',
                    customer_name='Signup Payments',
                    customer_email='<EMAIL>',
                    amount=0.0,  # This will be updated with each payment
                    service_description='General signup invoice for new registrations',
                    status='Paid',  # Mark as paid since it's just a placeholder
                    created_at=datetime.utcnow()
                )
                db.session.add(signup_invoice)
                db.session.flush()  # Get the ID without committing

            # Calculate transaction fee (default to 2.5% for new signups)
            transaction_fee = amount * 0.025  # Default fee for new customers
            net_amount = amount - transaction_fee

            confirmation = PaymentConfirmation(
                invoice_id=signup_invoice.id,  # Use the signup invoice
                customer_name=customer_name,
                customer_email=customer_email,
                mobile_money_sender_name=mobile_money_sender_name,
                amount=amount,
                transaction_fee=transaction_fee,
                net_amount=net_amount,
                mobile_operator=mobile_operator,
                transaction_id=transaction_id,
                service_description=service_description
            )

            # Validate the payment confirmation object
            confirmation.validate_amount()
            confirmation.validate_transaction_id_format()
            confirmation.validate_mobile_money_name()

            db.session.add(confirmation)
            db.session.commit()

            flash(get_translation('Payment confirmation submitted successfully! We will verify and process your payment shortly.'), 'success')
            return render_template('confirmation_success.html',
                                 reference_id=f"REF{confirmation.id:06d}",
                                 register_url=None)  # No registration URL until confirmed

        except ValueError as e:
            # Validation errors
            flash(str(e), 'danger')
            return render_template('start_signup.html', tier=tier)

        except Exception as e:
            # Database or other errors
            db.session.rollback()
            print(f"DEBUG: Signup error: {str(e)}")
            print(f"DEBUG: Error type: {type(e)}")
            import traceback
            traceback.print_exc()
            flash(f'An error occurred while processing your payment confirmation: {str(e)}', 'danger')
            return render_template('start_signup.html', tier=tier)

    return render_template('start_signup.html', tier=tier)

@app.route('/free-signup', methods=['GET', 'POST'])
def free_signup():
    """Direct registration for Free tier - no payment required"""
    # Get Free tier
    free_tier = PricingTier.query.filter_by(name='Free').first()
    if not free_tier:
        flash('Free tier not available. Please contact support.', 'danger')
        return redirect(url_for('pricing'))

    if request.method == 'GET':
        return render_template('free_signup_simple.html', tier=free_tier)

    if request.method == 'POST':
        try:
            # Collect user information
            full_name = sanitize_input(request.form.get('full_name', ''), 100)
            email = sanitize_input(request.form.get('email', ''), 120)
            company_name = sanitize_input(request.form.get('company_name', ''), 100)
            phone = sanitize_input(request.form.get('phone', ''), 20)
            business_type = sanitize_input(request.form.get('business_type', ''), 50)
            password = request.form.get('password', '')
            confirm_password = request.form.get('confirm_password', '')

            # Validation
            if not full_name:
                flash('Full name is required', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            if not email:
                flash('Email address is required', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            if not company_name:
                flash('Company name is required', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            if not password:
                flash('Password is required', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            if len(password) < 6:
                flash('Password must be at least 6 characters long', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            if password != confirm_password:
                flash('Passwords do not match', 'danger')
                return render_template('free_signup_simple.html', tier=free_tier)

            # Check if email already exists
            existing_user = User.query.filter_by(email=email).first()
            if existing_user:
                flash('An account with this email already exists. Please login instead.', 'warning')
                return redirect(url_for('user_admin_login'))

            # Create user account directly (no payment verification needed)
            username = email.split('@')[0]  # Use email prefix as username

            new_user = User(
                username=username,
                email=email,
                full_name=full_name,
                password_hash=generate_password_hash(password),
                role='user_admin',
                is_active=True,
                created_at=datetime.now()
            )

            db.session.add(new_user)
            db.session.flush()  # Get the user ID

            # Create company
            new_company = ClientCompany(
                company_name=company_name,
                company_email=email,
                company_phone=phone,
                pricing_tier_id=free_tier.id,
                created_at=datetime.now(),
                created_by=new_user.id
            )

            db.session.add(new_company)
            db.session.commit()

            flash('Account created successfully! You can now login with your credentials.', 'success')
            return render_template('free_signup_success.html',
                                 email=email,
                                 company_name=company_name,
                                 username=username)

        except Exception as e:
            db.session.rollback()
            print(f"Free signup error: {str(e)}")
            flash(f'An error occurred during registration: {str(e)}', 'danger')
            return render_template('free_signup_simple.html', tier=free_tier)

    # This should never be reached, but just in case
    return render_template('free_signup_simple.html', tier=free_tier)

def send_welcome_email(email, full_name, username, password, company_name):
    """Send welcome email with login credentials"""
    # This is a placeholder - implement actual email sending
    print(f"""
    Welcome Email for {email}:

    Dear {full_name},

    Welcome to EXLIPA! Your Free tier account has been created successfully.

    Company: {company_name}
    Username: {username}
    Password: {password}

    Login at: http://localhost:5000/user-admin-login

    You can start accepting payments immediately with 100 free transactions per month!

    Best regards,
    EXLIPA Team
    """)

@app.route('/request-pos', methods=['GET', 'POST'])
@login_required
def request_pos():
    if current_user.role not in ['company_user', 'admin']:
        return smart_redirect_by_role()
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect(url_for('logout'))
    if company.dynamic_pos_enabled:
        flash('POS is already active for your company.', 'info')
        return redirect(url_for('company_dashboard'))
    if request.method == 'POST':
        # Here you would process payment details, for now just mark as pending
        company.dynamic_pos_payment_pending = True
        db.session.commit()
        flash('Your POS unlock request has been submitted. Please complete payment and wait for admin approval.', 'success')
        return redirect(url_for('company_dashboard'))
    # Show payment instructions and request form
    pos_price = 150000  # Example POS add-on price (TZS)
    return render_template('request_pos.html', company=company, pos_price=pos_price)

@app.route('/my-invoices')
@login_required
def user_invoices():
    """User's own invoices - accessible to user_admin"""
    if current_user.role != 'user_admin':
        return smart_redirect_by_role()

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect('/user-admin')

    # Show invoices created by this user
    invoices = Invoice.query.filter_by(created_by=current_user.id).order_by(Invoice.created_at.desc()).all()
    return render_template('user_invoices.html', company=company, invoices=invoices)

@app.route('/company-invoices')
@login_required
def company_invoices():
    if current_user.role not in ['company_user', 'admin']:
        return smart_redirect_by_role()
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect(url_for('logout'))
    # Show invoices created by this user (since Invoice has no company link)
    invoices = Invoice.query.filter_by(created_by=current_user.id).order_by(Invoice.created_at.desc()).all()
    return render_template('company_invoices.html', company=company, invoices=invoices)

@app.route('/my-payments')
@login_required
def user_payments():
    """User's own payments - accessible to user_admin"""
    if current_user.role != 'user_admin':
        return smart_redirect_by_role()

    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect('/user-admin')

    # Show payments for this user's company
    payments = PaymentConfirmation.query.filter_by(client_company_id=company.id).order_by(PaymentConfirmation.submitted_at.desc()).all()
    return render_template('user_payments.html', company=company, payments=payments)

@app.route('/company-payments')
@login_required
def company_payments():
    if current_user.role not in ['company_user', 'admin']:
        return smart_redirect_by_role()
    company = ClientCompany.query.filter_by(company_email=current_user.email).first()
    if not company:
        flash('No company associated with your account.', 'danger')
        return redirect(url_for('logout'))
    payments = PaymentConfirmation.query.filter_by(client_company_id=company.id).order_by(PaymentConfirmation.submitted_at.desc()).all()
    return render_template('company_payments.html', company=company, payments=payments)

@app.route('/company/<int:company_id>', methods=['GET', 'POST'])
def public_company_landing(company_id):
    company = ClientCompany.query.get_or_404(company_id)
    if not getattr(company, 'is_public_page_enabled', True):
        abort(404)

    if request.method == 'POST':
        # Process payment confirmation
        customer_name = request.form.get('customer_name')
        amount = request.form.get('amount')
        payment_method = request.form.get('payment_method')
        transaction_id = request.form.get('transaction_id')
        sender_phone = request.form.get('sender_phone', '')
        notes = request.form.get('notes', '')

        # Calculate transaction fee based on company's tier
        amount_float = float(amount)
        transaction_fee = calculate_transaction_fee(company, amount_float)
        net_amount = amount_float - transaction_fee

        # Create payment confirmation
        payment_confirmation = PaymentConfirmation(
            client_company_id=company.id,
            customer_name=customer_name,
            customer_email=None,  # Company landing pages don't need email collection
            mobile_money_sender_name=sender_phone,  # Store sender phone number
            amount=amount_float,
            transaction_fee=transaction_fee,
            net_amount=net_amount,
            mobile_operator=payment_method,
            transaction_id=transaction_id,
            notes=notes,
            status='Pending',
            submitted_at=datetime.now()
        )

        try:
            db.session.add(payment_confirmation)
            db.session.commit()

            # Check if upgrade prompt should be shown
            usage_status = get_tier_limits_status(company)
            if usage_status.get('should_show_upgrade'):
                next_tier = usage_status.get('next_tier')
                if next_tier:
                    flash(f'Payment submitted successfully! Consider upgrading to {next_tier} tier for better rates and features.', 'info')
                else:
                    flash('Payment confirmation submitted successfully! We will verify and process your payment shortly.', 'success')
            else:
                flash('Payment confirmation submitted successfully! We will verify and process your payment shortly.', 'success')

            return redirect(url_for('public_company_landing', company_id=company.id))
        except Exception as e:
            db.session.rollback()
            flash('Error submitting payment confirmation. Please try again.', 'danger')
            if ENHANCED_FEATURES:
                logger.error("Error submitting payment confirmation", error=str(e), company_id=company.id)

    return render_template('public_company_landing.html', company=company)



if __name__ == '__main__':
    with app.app_context():
        # Force recreate database if needed
        try:
            # Test if we can query users (will fail if schema is wrong)
            User.query.first()
        except Exception as e:
            if ENHANCED_FEATURES:
                logger.warning("Database schema issue detected, recreating", error=str(e))
            db.drop_all()
            db.create_all()
        
        db.create_all()
        
        # Create default admin user if none exists
        if not User.query.first():
            super_admin = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                email='<EMAIL>',
                full_name='System Administrator',
                role='super_admin'
            )
            db.session.add(super_admin)

            # Create a test user_admin (company customer) for testing
            test_user_admin = User(
                username='useradmin',
                password_hash=generate_password_hash('useradmin123'),
                email='<EMAIL>',
                full_name='Test Company Customer',
                role='user_admin'
            )
            db.session.add(test_user_admin)

            db.session.commit()
            print("Default super admin user created: admin/admin123")
            print("Test user admin created: useradmin/useradmin123")
            print("IMPORTANT: Change the default passwords immediately!")
        
        # Initialize default pricing tiers
        initialize_default_pricing_tiers()
        print("Pricing tiers initialized")
        print("System ready!")
    
    print("Starting Exlipa Payment Gateway...")
    print("Dashboard: http://localhost:5000")
    print("Admin Login: http://localhost:5000/admin-login (admin/admin123)")
    print("Health Check: http://localhost:5000/health")
    print("Metrics: http://localhost:5000/metrics")
    print("Monitoring: http://localhost:5000/admin/monitoring")
    
    if ENHANCED_FEATURES:
        print("✅ Enhanced features enabled (security, caching, monitoring)")
        logger.info("Application starting", port=5000)
    else:
        print("Warning: Running in basic mode - install: pip install flask-limiter flask-caching structlog redis")
        logger.info("Application starting in basic mode")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
