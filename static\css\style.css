/* EXLIPA Payment System - Improved Accessibility & Visibility */

/* EXLIPA - Beautiful Modern Design System */
:root {
    /* Primary Brand Colors - Modern & Attractive */
    --primary-color: #6366f1;        /* Beautiful indigo */
    --primary-dark: #4f46e5;         /* Deep indigo */
    --primary-light: #8b5cf6;        /* Light purple */
    --primary-accent: #06b6d4;       /* Bright cyan */

    /* Secondary Colors - Elegant & Sophisticated */
    --secondary-color: #64748b;      /* Elegant gray */
    --secondary-light: #94a3b8;      /* Light gray */
    --secondary-dark: #475569;       /* Dark gray */

    /* Status Colors - Vibrant & Clear */
    --success-color: #10b981;        /* Vibrant green */
    --success-light: #34d399;        /* Light green */
    --warning-color: #f59e0b;        /* Vibrant amber */
    --warning-light: #fbbf24;        /* Light amber */
    --danger-color: #ef4444;         /* Vibrant red */
    --danger-light: #f87171;         /* Light red */
    --info-color: #3b82f6;           /* Vibrant blue */

    /* Background Colors - Clean & Modern */
    --bg-primary: #ffffff;           /* Pure white */
    --bg-secondary: #f8fafc;         /* Very light gray */
    --bg-tertiary: #f1f5f9;          /* Light gray */
    --bg-dark: #1e293b;              /* Dark slate */
    --bg-surface: #ffffff;           /* Card surfaces */
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Text Colors - Perfect Contrast */
    --text-primary: #1e293b;         /* Dark slate */
    --text-secondary: #475569;       /* Medium gray */
    --text-tertiary: #64748b;        /* Light gray */
    --text-muted: #94a3b8;           /* Very light gray */
    --text-white: #ffffff;           /* Pure white */
    --text-on-primary: #ffffff;      /* White on colored backgrounds */

    /* Border Colors - Subtle & Elegant */
    --border-light: #e2e8f0;         /* Very light border */
    --border-medium: #cbd5e1;        /* Medium border */
    --border-dark: #94a3b8;          /* Dark border */
    --border-accent: #6366f1;        /* Primary border */

    /* Beautiful Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 25px -5px rgba(99, 102, 241, 0.25);

    /* Beautiful Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #64748b 0%, #475569 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

    /* Modern Spacing */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-full: 9999px;

    /* Smooth Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional Typography & Layout */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
    font-weight: 400;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Professional Background Gradients */
.bg-gradient {
    background: var(--gradient-primary) !important;
    color: var(--text-inverse) !important;
}

.bg-gradient-accent {
    background: var(--gradient-accent) !important;
    color: var(--text-inverse) !important;
}

.bg-gradient-surface {
    background: var(--gradient-surface) !important;
    color: var(--text-primary) !important;
}

/* Professional Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Beautiful Modern Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 1.25rem 0;
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
}

.navbar-brand {
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    letter-spacing: -0.02em;
    transition: var(--transition-normal);
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 600;
    padding: 0.875rem 1.5rem !important;
    border-radius: var(--border-radius-full);
    margin: 0 0.25rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
    z-index: -1;
    border-radius: var(--border-radius-full);
}

.navbar-nav .nav-link:hover {
    color: var(--text-white) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-colored);
}

.navbar-nav .nav-link:hover::before {
    left: 0;
}

.navbar-nav .nav-link:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

/* Beautiful Dropdown */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    margin-top: 0.75rem;
    min-width: 200px;
}

.dropdown-item {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: 0.875rem 1.25rem;
    border-radius: var(--border-radius-lg);
    margin: 0.25rem 0;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
    z-index: -1;
}

.dropdown-item:hover {
    color: var(--text-white) !important;
    transform: translateX(8px);
}

.dropdown-item:hover::before {
    left: 0;
}

.dropdown-item.active {
    background: var(--gradient-primary);
    color: var(--text-white) !important;
}

/* Professional Card Design */
.card {
    background-color: var(--bg-surface);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    color: var(--text-primary);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    opacity: 0;
    transition: var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-medium);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--gradient-surface);
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0 !important;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1.25rem 1.75rem;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.75rem;
    color: var(--text-primary);
}

.card-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    letter-spacing: -0.01em;
}

.card-subtitle {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Professional Glass Cards */
.glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-accent);
    opacity: 0;
    transition: var(--transition-normal);
}

.glass-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(8, 145, 178, 0.2);
}

.glass-card:hover::before {
    opacity: 1;
}

/* Beautiful Modern Buttons */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: 1rem 2rem;
    transition: var(--transition-normal);
    border: none;
    font-size: 1rem;
    line-height: 1.4;
    letter-spacing: -0.01em;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.btn:hover {
    transform: translateY(-3px);
    text-decoration: none;
}

.btn:hover::before {
    left: 100%;
}

.btn:focus {
    outline: none;
    transform: translateY(-3px);
}

.btn:active {
    transform: translateY(-1px);
}

/* Primary Button - Beautiful Gradient */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

.btn-primary:focus {
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
    color: var(--text-white);
}

/* Secondary Button - Elegant Gray */
.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(100, 116, 139, 0.4);
}

/* Success Button - Vibrant Green */
.btn-success {
    background: var(--gradient-success);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
}

/* Warning Button - Vibrant Amber */
.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

/* Danger Button - Vibrant Red */
.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4);
}

/* Info Button - Beautiful Blue */
.btn-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: var(--text-white);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: var(--text-white);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

/* Beautiful Outline Buttons */
.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.1);
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-color: transparent;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    box-shadow: 0 4px 15px rgba(100, 116, 139, 0.1);
}

.btn-outline-secondary:hover {
    background: var(--gradient-secondary);
    color: var(--text-white);
    border-color: transparent;
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
}

.btn-outline-success {
    background: transparent;
    color: var(--success-color);
    border: 2px solid var(--success-color);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.btn-outline-success:hover {
    background: var(--gradient-success);
    color: var(--text-white);
    border-color: transparent;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

/* Special Button Styles */
.btn-hero-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    border-radius: var(--border-radius-xl);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
    border: none;
}

.btn-hero-primary:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
    color: var(--text-white);
    transform: translateY(-4px);
    box-shadow: 0 15px 40px rgba(99, 102, 241, 0.5);
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    border-radius: var(--border-radius-xl);
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--text-white);
    transform: translateY(-4px);
    box-shadow: 0 15px 40px rgba(255, 255, 255, 0.1);
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--primary-color);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
}

.btn-ghost:hover {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Button Sizes */
.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius-xl);
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-md);
}

.btn-xs {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
}

/* Professional Outline Buttons */
.btn-outline-primary {
    color: var(--primary-accent);
    border-color: var(--primary-accent);
    background-color: transparent;
    font-weight: 600;
}

.btn-outline-primary:hover {
    background-color: var(--primary-accent);
    border-color: var(--primary-accent);
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(8, 145, 178, 0.2);
}

.btn-outline-secondary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
    font-weight: 600;
}

.btn-outline-secondary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(15, 23, 42, 0.2);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
    background-color: transparent;
    font-weight: 600;
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
}

.btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
    background-color: transparent;
    font-weight: 600;
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background-color: transparent;
    font-weight: 600;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-white);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

/* Professional Ghost Button */
.btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary-accent);
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

.btn-ghost:hover {
    background-color: rgba(8, 145, 178, 0.1);
    color: var(--primary-accent-light);
    transform: translateY(0);
    box-shadow: none;
}

/* Button Sizes */
.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: var(--border-radius-lg);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
}

/* Table styles - High Contrast */
.table {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--border-medium);
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.875rem 0.75rem;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
}

.table-hover tbody tr:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 250, 252, 0.5);
}

/* Badge styles - High Contrast */
.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
    font-weight: 500;
    border-radius: 6px;
}

.badge.bg-primary {
    background-color: var(--primary-color) !important;
    color: var(--text-white) !important;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
    color: var(--text-white) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: var(--text-white) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
    color: var(--text-white) !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
    color: var(--text-white) !important;
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
    color: var(--text-white) !important;
}

/* Form styles - High Contrast */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid var(--border-medium);
    padding: 0.75rem 1rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Alert styles - High Contrast */
.alert {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background-color: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.alert-danger {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-info {
    background-color: #e0f2fe;
    border-color: #b3e5fc;
    color: #0c4a6e;
}

.alert-primary {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Payment instructions specific styles */
.payment-operator-card {
    transition: transform 0.2s;
}

.payment-operator-card:hover {
    transform: scale(1.02);
}

/* Mobile Money operator colors */
.bg-mpesa {
    background-color: #00a651 !important;
}

.bg-tigo {
    background-color: #0066cc !important;
}

.bg-airtel {
    background-color: #e60000 !important;
}

.bg-halo {
    background-color: #ff9900 !important;
}

/* Status colors */
.status-pending {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.status-confirmed {
    background-color: #28a745 !important;
}

.status-rejected {
    background-color: #dc3545 !important;
}

.status-processed {
    background-color: #17a2b8 !important;
}

/* Admin dashboard styles */
.dashboard-card {
    border-left: 4px solid #007bff;
}

.dashboard-card.warning {
    border-left-color: #ffc107;
}

.dashboard-card.success {
    border-left-color: #28a745;
}

.dashboard-card.info {
    border-left-color: #17a2b8;
}

.dashboard-card.primary {
    border-left-color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
}

/* Print styles for receipts */
@media print {
    .navbar, .btn, .card-header {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}
