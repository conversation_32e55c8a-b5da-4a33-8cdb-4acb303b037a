{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}
    <PERSON>po ya Msimamizi - Mfumo wa Malipo
{% else %}
    Admin Billing - Payment System
{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-file-invoice-dollar me-2"></i>
        {% if session.language == 'sw' %}
            Usimamizi wa Malipo
        {% else %}
            Billing Management
        {% endif %}
    </h1>
    <a href="{{ url_for('generate_monthly_bills') }}" class="btn btn-outline-warning">
        <i class="fas fa-calculator me-2"></i>
        {% if session.language == 'sw' %}
            Tengeneza Ankara za Kila Mwezi
        {% else %}
            Generate Monthly Bills
        {% endif %}
    </a>
</div>

<!-- Summary Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <h4>{{ total_pending }}</h4>
                <p class="mb-0">Pending Bills</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4>{{ total_paid }}</h4>
                <p class="mb-0">Paid Bills</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>{{ total_overdue }}</h4>
                <p class="mb-0">Overdue Bills</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4>TZS {{ "{:,}".format(monthly_revenue|int) }}</h4>
                <p class="mb-0">Revenue This Month</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status Filter</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All</option>
                    <option value="Pending" {% if status_filter == 'Pending' %}selected{% endif %}>{{ t('Pending') }}</option>
                    <option value="Paid" {% if status_filter == 'Paid' %}selected{% endif %}>Paid</option>
                    <option value="Overdue" {% if status_filter == 'Overdue' %}selected{% endif %}>Overdue</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{{ url_for('admin_billing') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Bills Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Bills <span class="badge bg-secondary ms-2">{{ bills|length }} records</span></h5>
    </div>
    <div class="card-body p-0">
        {% if bills %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Bill #</th>
                        <th>{{ t('Company') }}</th>
                        <th>Type</th>
                        <th>Period</th>
                        <th>Amount (TZS)</th>
                        <th>Due Date</th>
                        <th>{{ t('Status') }}</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for bill in bills %}
                    <tr>
                        <td><code>{{ bill.bill_number }}</code></td>
                        <td>{{ bill.client_company.company_name if bill.client_company else 'N/A' }}</td>
                        <td>{{ bill.bill_type }}</td>
                        <td>
                            {% if bill.billing_period_start and bill.billing_period_end %}
                                {{ bill.billing_period_start.strftime('%b %d, %Y') }} - {{ bill.billing_period_end.strftime('%b %d, %Y') }}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td>{{ "{:,.0f}"|format(bill.total_amount) }}</td>
                        <td>{{ bill.due_date.strftime('%b %d, %Y') if bill.due_date else 'N/A' }}</td>
                        <td>
                            <span class="badge 
                                {% if bill.status == 'Pending' %}bg-warning text-dark
                                {% elif bill.status == 'Paid' %}bg-success
                                {% elif bill.status == 'Overdue' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {{ bill.status }}
                            </span>
                        </td>
                        <td>
                            <a href="{{ url_for('bill_detail', bill_id=bill.id) }}" class="btn btn-outline-primary btn-sm" title="View Bill">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if bill.status == 'Pending' %}
                            <form method="POST" action="{{ url_for('mark_bill_paid', bill_id=bill.id) }}" style="display:inline-block">
                                <button type="submit" class="btn btn-outline-success btn-sm" onclick="return confirm('Mark this bill as paid?')">
                                    <i class="fas fa-check"></i> Mark Paid
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No bills found</h5>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
