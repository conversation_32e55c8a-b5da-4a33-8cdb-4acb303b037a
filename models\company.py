#!/usr/bin/env python3
"""
Company and subscription models for EXLIPA
"""

from datetime import datetime, timedelta
from .base import db, BaseModel, ValidationMixin
from utils.validators import InputValidator, ValidationError

class PricingTier(BaseModel, ValidationMixin):
    """Pricing tier model"""
    
    __tablename__ = 'pricing_tier'
    
    # Tier details
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='TZS')
    
    # Features
    max_transactions = db.Column(db.Integer)  # NULL for unlimited
    transaction_fee_percentage = db.Column(db.Float, default=0.0)
    has_api_access = db.Column(db.Bo<PERSON>, default=False)
    has_analytics = db.Column(db.Bo<PERSON>, default=True)
    has_pos_system = db.Column(db.Boolean, default=False)
    has_custom_branding = db.Column(db.Boolean, default=False)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    sort_order = db.Column(db.Integer, default=0)
    
    def __repr__(self):
        return f'<PricingTier {self.name}>'
    
    def validate(self):
        """Validate pricing tier data"""
        if not self.name:
            raise ValidationError("Tier name is required")
        
        self.name = InputValidator.sanitize_string(self.name, 50)
        
        if self.price is None or self.price < 0:
            raise ValidationError("Price must be a positive number")
        
        if self.transaction_fee_percentage < 0 or self.transaction_fee_percentage > 100:
            raise ValidationError("Transaction fee percentage must be between 0 and 100")
    
    def get_features_list(self):
        """Get list of features for this tier"""
        features = []
        
        if self.max_transactions:
            features.append(f"Up to {self.max_transactions:,} transactions/month")
        else:
            features.append("Unlimited transactions")
        
        if self.transaction_fee_percentage > 0:
            features.append(f"{self.transaction_fee_percentage}% transaction fee")
        else:
            features.append("No transaction fees")
        
        if self.has_api_access:
            features.append("API Access")
        
        if self.has_analytics:
            features.append("Analytics Dashboard")
        
        if self.has_pos_system:
            features.append("POS System")
        
        if self.has_custom_branding:
            features.append("Custom Branding")
        
        return features
    
    @classmethod
    def get_active_tiers(cls):
        """Get active pricing tiers"""
        return cls.query.filter_by(is_active=True).order_by(cls.sort_order).all()

class ClientCompany(BaseModel, ValidationMixin):
    """Client company model"""
    
    __tablename__ = 'client_company'
    
    # Company details
    company_name = db.Column(db.String(100), nullable=False)
    company_email = db.Column(db.String(120), nullable=False)
    company_phone = db.Column(db.String(20))
    company_address = db.Column(db.Text)
    company_tin = db.Column(db.String(20))  # Tax Identification Number
    
    # Business details
    business_type = db.Column(db.String(50))
    industry = db.Column(db.String(50))
    website = db.Column(db.String(200))
    
    # EXLIPA integration
    api_key = db.Column(db.String(100), unique=True)
    webhook_url = db.Column(db.String(200))
    
    # Status and settings
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    verification_date = db.Column(db.DateTime)
    
    # Ownership
    owner_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Branding
    logo_filename = db.Column(db.String(200))
    brand_color = db.Column(db.String(7))  # Hex color code
    
    # Relationships
    owner = db.relationship('User', backref='owned_companies')
    
    def __repr__(self):
        return f'<ClientCompany {self.company_name}>'
    
    def validate(self):
        """Validate company data"""
        # Validate company name
        if not self.company_name:
            raise ValidationError("Company name is required")
        
        self.company_name = InputValidator.sanitize_string(self.company_name, 100)
        
        # Validate email
        if not self.company_email:
            raise ValidationError("Company email is required")
        
        self.company_email = InputValidator.validate_email(self.company_email)
        
        # Validate phone if provided
        if self.company_phone:
            self.company_phone = InputValidator.validate_phone(self.company_phone)
        
        # Validate TIN if provided
        if self.company_tin:
            self.company_tin = InputValidator.validate_tin(self.company_tin)
        
        # Validate website if provided
        if self.website:
            self.website = InputValidator.sanitize_string(self.website, 200)
    
    def generate_api_key(self):
        """Generate unique API key"""
        import secrets
        import string
        
        # Generate 32-character API key
        alphabet = string.ascii_letters + string.digits
        api_key = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        # Ensure uniqueness
        while ClientCompany.query.filter_by(api_key=api_key).first():
            api_key = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        self.api_key = api_key
        return api_key
    
    def verify_company(self, verified_by_id):
        """Verify the company"""
        self.is_verified = True
        self.verification_date = datetime.utcnow()
        return self.save()
    
    def get_active_subscription(self):
        """Get active subscription for this company"""
        return Subscription.query.filter_by(
            company_id=self.id,
            is_active=True
        ).first()
    
    def get_current_tier(self):
        """Get current pricing tier"""
        subscription = self.get_active_subscription()
        return subscription.pricing_tier if subscription else None
    
    def can_make_transaction(self):
        """Check if company can make transactions"""
        if not self.is_active:
            return False, "Company account is inactive"
        
        subscription = self.get_active_subscription()
        if not subscription:
            return False, "No active subscription"
        
        if subscription.is_expired():
            return False, "Subscription has expired"
        
        # Check transaction limits
        tier = subscription.pricing_tier
        if tier.max_transactions:
            current_month_transactions = self.get_current_month_transaction_count()
            if current_month_transactions >= tier.max_transactions:
                return False, f"Monthly transaction limit of {tier.max_transactions} reached"
        
        return True, "OK"
    
    def get_current_month_transaction_count(self):
        """Get transaction count for current month"""
        from .payment import PaymentConfirmation
        from datetime import datetime
        
        start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        return PaymentConfirmation.query.filter(
            PaymentConfirmation.client_company_id == self.id,
            PaymentConfirmation.status == 'Confirmed',
            PaymentConfirmation.processed_at >= start_of_month
        ).count()
    
    @classmethod
    def get_by_api_key(cls, api_key):
        """Get company by API key"""
        return cls.query.filter_by(api_key=api_key, is_active=True).first()

class Subscription(BaseModel, ValidationMixin):
    """Subscription model linking companies to pricing tiers"""
    
    __tablename__ = 'subscription'
    
    # Associations
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    pricing_tier_id = db.Column(db.Integer, db.ForeignKey('pricing_tier.id'), nullable=False)
    
    # Subscription details
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    
    # Payment details
    amount_paid = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(50))  # Mobile Money, Bank Transfer, etc.
    payment_reference = db.Column(db.String(100))
    
    # Status
    status = db.Column(db.String(20), default='Active')  # Active, Expired, Cancelled, Suspended
    
    # Relationships
    company = db.relationship('ClientCompany', backref='subscriptions')
    pricing_tier = db.relationship('PricingTier', backref='subscriptions')
    
    def __repr__(self):
        return f'<Subscription {self.company.company_name} - {self.pricing_tier.name}>'
    
    def validate(self):
        """Validate subscription data"""
        if not self.company_id:
            raise ValidationError("Company is required")
        
        if not self.pricing_tier_id:
            raise ValidationError("Pricing tier is required")
        
        if not self.amount_paid or self.amount_paid < 0:
            raise ValidationError("Amount paid must be a positive number")
    
    def is_expired(self):
        """Check if subscription is expired"""
        if self.end_date:
            return datetime.utcnow() > self.end_date
        return False
    
    def days_until_expiry(self):
        """Get days until subscription expires"""
        if self.end_date:
            delta = self.end_date - datetime.utcnow()
            return max(0, delta.days)
        return None
    
    def extend_subscription(self, months=1):
        """Extend subscription by specified months"""
        if self.end_date:
            # Extend from current end date
            new_end_date = self.end_date + timedelta(days=30 * months)
        else:
            # Set end date from now
            new_end_date = datetime.utcnow() + timedelta(days=30 * months)
        
        self.end_date = new_end_date
        self.status = 'Active'
        return self.save()
    
    def cancel_subscription(self, reason=None):
        """Cancel the subscription"""
        self.is_active = False
        self.status = 'Cancelled'
        return self.save()
    
    @classmethod
    def create_subscription(cls, company_id, pricing_tier_id, amount_paid, payment_method, payment_reference, duration_months=1):
        """Create a new subscription"""
        subscription = cls(
            company_id=company_id,
            pricing_tier_id=pricing_tier_id,
            amount_paid=amount_paid,
            payment_method=payment_method,
            payment_reference=payment_reference,
            end_date=datetime.utcnow() + timedelta(days=30 * duration_months)
        )
        
        subscription.validate_and_save()
        return subscription
    
    @classmethod
    def get_expiring_soon(cls, days=7):
        """Get subscriptions expiring within specified days"""
        cutoff_date = datetime.utcnow() + timedelta(days=days)
        
        return cls.query.filter(
            cls.is_active == True,
            cls.end_date <= cutoff_date,
            cls.end_date > datetime.utcnow()
        ).all()
