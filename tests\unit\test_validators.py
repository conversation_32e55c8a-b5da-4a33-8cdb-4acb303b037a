#!/usr/bin/env python3
"""
Unit tests for input validators
"""

import pytest
from utils.validators import InputValidator, ValidationError

class TestInputValidator:
    """Test InputValidator class"""
    
    def test_sanitize_string_valid(self):
        """Test string sanitization with valid input"""
        # Normal string
        result = InputValidator.sanitize_string("Hello World", 50)
        assert result == "Hello World"
        
        # String with whitespace
        result = InputValidator.sanitize_string("  Hello World  ", 50)
        assert result == "Hello World"
        
        # String with HTML (should be escaped)
        result = InputValidator.sanitize_string("<script>alert('xss')</script>", 100)
        assert "&lt;script&gt;" in result
        assert "&lt;/script&gt;" in result
    
    def test_sanitize_string_invalid(self):
        """Test string sanitization with invalid input"""
        # Non-string input
        with pytest.raises(ValidationError, match="Input must be a string"):
            InputValidator.sanitize_string(123, 50)
        
        # String too long
        with pytest.raises(ValidationError, match="Input too long"):
            InputValidator.sanitize_string("a" * 100, 50)
    
    def test_validate_email_valid(self):
        """Test email validation with valid emails"""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            result = InputValidator.validate_email(email)
            assert result == email.lower()
    
    def test_validate_email_invalid(self):
        """Test email validation with invalid emails"""
        invalid_emails = [
            "",
            "invalid-email",
            "@domain.com",
            "user@",
            "user@domain",
            "user <EMAIL>",
            "<EMAIL>"
        ]
        
        for email in invalid_emails:
            with pytest.raises(ValidationError):
                InputValidator.validate_email(email)
    
    def test_validate_phone_valid(self):
        """Test phone validation with valid Tanzanian numbers"""
        valid_phones = [
            "+255712345678",
            "+255622345678",
            "0712345678",
            "0622345678"
        ]
        
        for phone in valid_phones:
            result = InputValidator.validate_phone(phone)
            assert result == phone
    
    def test_validate_phone_invalid(self):
        """Test phone validation with invalid numbers"""
        invalid_phones = [
            "+254712345678",  # Kenya number
            "712345678",      # Missing prefix
            "+255812345678",  # Invalid operator code
            "+25571234567",   # Too short
            "+2557*********", # Too long
            "invalid-phone"
        ]
        
        for phone in invalid_phones:
            with pytest.raises(ValidationError):
                InputValidator.validate_phone(phone, required=True)
    
    def test_validate_phone_optional(self):
        """Test optional phone validation"""
        # Empty phone should return None when not required
        result = InputValidator.validate_phone("", required=False)
        assert result is None
        
        # Empty phone should raise error when required
        with pytest.raises(ValidationError):
            InputValidator.validate_phone("", required=True)
    
    def test_validate_username_valid(self):
        """Test username validation with valid usernames"""
        valid_usernames = [
            "user123",
            "test_user",
            "admin",
            "company_admin_2023"
        ]
        
        for username in valid_usernames:
            result = InputValidator.validate_username(username)
            assert result == username
    
    def test_validate_username_invalid(self):
        """Test username validation with invalid usernames"""
        invalid_usernames = [
            "",
            "ab",           # Too short
            "a" * 51,       # Too long
            "user-name",    # Invalid character
            "user name",    # Space not allowed
            "user@name",    # Special character not allowed
            "123",          # Only numbers
        ]
        
        for username in invalid_usernames:
            with pytest.raises(ValidationError):
                InputValidator.validate_username(username)
    
    def test_validate_password_valid(self):
        """Test password validation with valid passwords"""
        valid_passwords = [
            "Password123",
            "MySecure123",
            "Admin2023!",
            "TestPass1"
        ]
        
        for password in valid_passwords:
            result = InputValidator.validate_password(password)
            assert result == password
    
    def test_validate_password_invalid(self):
        """Test password validation with invalid passwords"""
        invalid_passwords = [
            "",
            "short",        # Too short
            "password",     # No uppercase
            "PASSWORD",     # No lowercase
            "Password",     # No digit
            "password123",  # No uppercase
            "PASSWORD123"   # No lowercase
        ]
        
        for password in invalid_passwords:
            with pytest.raises(ValidationError):
                InputValidator.validate_password(password)
    
    def test_validate_amount_valid(self):
        """Test amount validation with valid amounts"""
        valid_amounts = [
            "5000.00",
            "1000",
            1500.50,
            "10000",
            5000000
        ]
        
        for amount in valid_amounts:
            result = InputValidator.validate_amount(amount)
            assert isinstance(result, float)
            assert result >= 1000
            assert result <= 10000000
    
    def test_validate_amount_invalid(self):
        """Test amount validation with invalid amounts"""
        invalid_amounts = [
            "500",          # Below minimum
            "15000000",     # Above maximum
            "invalid",      # Not a number
            "",             # Empty
            -1000,          # Negative
            0               # Zero
        ]
        
        for amount in invalid_amounts:
            with pytest.raises(ValidationError):
                InputValidator.validate_amount(amount)
    
    def test_validate_transaction_id_valid(self):
        """Test transaction ID validation with valid IDs"""
        valid_ids = [
            "AB12345678",
            "MPESA123456",
            "TG87654321",
            "AIRTEL12345"
        ]
        
        for transaction_id in valid_ids:
            result = InputValidator.validate_transaction_id(transaction_id)
            assert result == transaction_id.upper()
    
    def test_validate_transaction_id_invalid(self):
        """Test transaction ID validation with invalid IDs"""
        invalid_ids = [
            "",
            "123",          # Too short
            "ab123",        # Too short
            "invalid-id",   # Invalid characters
            "a" * 51        # Too long
        ]
        
        for transaction_id in invalid_ids:
            with pytest.raises(ValidationError):
                InputValidator.validate_transaction_id(transaction_id)
    
    def test_validate_mobile_operator_valid(self):
        """Test mobile operator validation with valid operators"""
        valid_operators = [
            "M-Pesa",
            "Tigo Pesa",
            "Airtel Money",
            "CRDB Lipa",
            "Halo Pesa"
        ]
        
        for operator in valid_operators:
            result = InputValidator.validate_mobile_operator(operator)
            assert result == operator
    
    def test_validate_mobile_operator_invalid(self):
        """Test mobile operator validation with invalid operators"""
        invalid_operators = [
            "",
            "Invalid Operator",
            "Vodacom",
            "Orange Money"
        ]
        
        for operator in invalid_operators:
            with pytest.raises(ValidationError):
                InputValidator.validate_mobile_operator(operator)
    
    def test_validate_role_valid(self):
        """Test role validation with valid roles"""
        valid_roles = [
            "super_admin",
            "user_admin",
            "company_user"
        ]
        
        for role in valid_roles:
            result = InputValidator.validate_role(role)
            assert result == role
    
    def test_validate_role_invalid(self):
        """Test role validation with invalid roles"""
        invalid_roles = [
            "",
            "admin",
            "user",
            "invalid_role",
            "super-admin"
        ]
        
        for role in invalid_roles:
            with pytest.raises(ValidationError):
                InputValidator.validate_role(role)
    
    def test_validate_tin_valid(self):
        """Test TIN validation with valid TINs"""
        valid_tins = [
            "***********",
            "***********"
        ]
        
        for tin in valid_tins:
            result = InputValidator.validate_tin(tin)
            assert result == tin
    
    def test_validate_tin_invalid(self):
        """Test TIN validation with invalid TINs"""
        invalid_tins = [
            "*********",    # No dashes
            "123-45-6789",  # Wrong format
            "abc-def-ghi",  # Not numbers
            "123-456-78"    # Too short
        ]
        
        for tin in invalid_tins:
            with pytest.raises(ValidationError):
                InputValidator.validate_tin(tin, required=True)
    
    def test_validate_tin_optional(self):
        """Test optional TIN validation"""
        # Empty TIN should return None when not required
        result = InputValidator.validate_tin("", required=False)
        assert result is None
        
        # Empty TIN should raise error when required
        with pytest.raises(ValidationError):
            InputValidator.validate_tin("", required=True)
    
    def test_validate_payment_status_valid(self):
        """Test payment status validation with valid statuses"""
        valid_statuses = [
            "Pending",
            "Confirmed",
            "Rejected",
            "Processed"
        ]
        
        for status in valid_statuses:
            result = InputValidator.validate_payment_status(status)
            assert result == status
    
    def test_validate_payment_status_invalid(self):
        """Test payment status validation with invalid statuses"""
        invalid_statuses = [
            "",
            "Invalid",
            "pending",      # Wrong case
            "Complete",
            "Failed"
        ]
        
        for status in invalid_statuses:
            with pytest.raises(ValidationError):
                InputValidator.validate_payment_status(status)
    
    def test_validate_json_data_valid(self):
        """Test JSON data validation with valid data"""
        valid_data = {
            "name": "Test",
            "email": "<EMAIL>",
            "amount": 5000
        }
        required_fields = ["name", "email"]
        
        result = InputValidator.validate_json_data(valid_data, required_fields)
        assert result == valid_data
    
    def test_validate_json_data_invalid(self):
        """Test JSON data validation with invalid data"""
        # Not a dictionary
        with pytest.raises(ValidationError, match="Invalid JSON data"):
            InputValidator.validate_json_data("not a dict", [])
        
        # Missing required fields
        incomplete_data = {"name": "Test"}
        required_fields = ["name", "email", "amount"]
        
        with pytest.raises(ValidationError, match="Missing required fields"):
            InputValidator.validate_json_data(incomplete_data, required_fields)
    
    def test_validate_api_key_valid(self):
        """Test API key validation with valid keys"""
        valid_keys = [
            "abcdef*********0abcdef*********0",
            "ABCDEF*********0abcdef*********0",
            "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
        ]
        
        for key in valid_keys:
            result = InputValidator.validate_api_key(key)
            assert result == key
    
    def test_validate_api_key_invalid(self):
        """Test API key validation with invalid keys"""
        invalid_keys = [
            "",
            "short",        # Too short
            "key with spaces",
            "key-with-dashes"
        ]
        
        for key in invalid_keys:
            with pytest.raises(ValidationError):
                InputValidator.validate_api_key(key)

class TestValidatorHelpers:
    """Test validator helper functions"""
    
    def test_safe_int(self):
        """Test safe integer conversion"""
        from utils.validators import safe_int
        
        assert safe_int("123") == 123
        assert safe_int(123) == 123
        assert safe_int("invalid") == 0
        assert safe_int("invalid", default=5) == 5
        assert safe_int(None) == 0
    
    def test_safe_float(self):
        """Test safe float conversion"""
        from utils.validators import safe_float
        
        assert safe_float("123.45") == 123.45
        assert safe_float(123.45) == 123.45
        assert safe_float("invalid") == 0.0
        assert safe_float("invalid", default=5.5) == 5.5
        assert safe_float(None) == 0.0
    
    def test_safe_bool(self):
        """Test safe boolean conversion"""
        from utils.validators import safe_bool
        
        assert safe_bool(True) == True
        assert safe_bool(False) == False
        assert safe_bool("true") == True
        assert safe_bool("1") == True
        assert safe_bool("yes") == True
        assert safe_bool("on") == True
        assert safe_bool("false") == False
        assert safe_bool("0") == False
        assert safe_bool(1) == True
        assert safe_bool(0) == False
        assert safe_bool("invalid") == False
        assert safe_bool("invalid", default=True) == True
