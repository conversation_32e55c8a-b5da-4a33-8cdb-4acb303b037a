#!/usr/bin/env python
"""
Database migration script to add cash drawer management tables
"""

from app import app, db
from app import Cash<PERSON>rawerSession, CashDrawerTransaction, PosSale

def migrate_database():
    """Run database migrations for cash drawer management"""
    with app.app_context():
        try:
            print("Starting cash drawer database migration...")
            
            # Create new tables
            db.create_all()
            
            # Add new columns to existing PosSale table
            # Note: These columns were added to the model, but we need to ensure they exist
            print("Adding new columns to PosSale table...")
            
            # Check if columns exist and add them if they don't
            from sqlalchemy import text
            
            # Check for amount_received column
            try:
                result = db.session.execute(text("SELECT amount_received FROM pos_sale LIMIT 1"))
                print("amount_received column already exists")
            except:
                print("Adding amount_received column...")
                db.session.execute(text("ALTER TABLE pos_sale ADD COLUMN amount_received FLOAT"))
            
            # Check for change_given column
            try:
                result = db.session.execute(text("SELECT change_given FROM pos_sale LIMIT 1"))
                print("change_given column already exists")
            except:
                print("Adding change_given column...")
                db.session.execute(text("ALTER TABLE pos_sale ADD COLUMN change_given FLOAT"))
            
            db.session.commit()
            print("✅ Cash drawer database migration completed successfully!")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {e}")
            raise

if __name__ == "__main__":
    migrate_database()
