#!/usr/bin/env python3
"""
Test PaymentConfirmation creation
"""

from app import app, db, PaymentConfirmation

def test_payment_confirmation():
    """Test creating a PaymentConfirmation object"""
    with app.app_context():
        print("=== TESTING PAYMENT CONFIRMATION CREATION ===")
        
        try:
            # Test data similar to what would come from the form
            test_data = {
                'invoice_id': None,
                'customer_name': '<PERSON>',
                'customer_email': '<EMAIL>',
                'mobile_money_sender_name': '<PERSON>',
                'amount': 700000.0,
                'mobile_operator': 'M-Pesa',
                'transaction_id': 'ND12345678',
                'service_description': 'Signup for Business package'
            }
            
            print("Creating PaymentConfirmation with test data...")
            print(f"Data: {test_data}")
            
            # Create the object
            confirmation = PaymentConfirmation(**test_data)
            print("✅ PaymentConfirmation object created successfully")
            
            # Test validation methods
            print("Testing validation methods...")
            
            try:
                confirmation.validate_amount()
                print("✅ validate_amount() passed")
            except Exception as e:
                print(f"❌ validate_amount() failed: {e}")
            
            try:
                confirmation.validate_transaction_id_format()
                print("✅ validate_transaction_id_format() passed")
            except Exception as e:
                print(f"❌ validate_transaction_id_format() failed: {e}")
            
            try:
                confirmation.validate_mobile_money_name()
                print("✅ validate_mobile_money_name() passed")
            except Exception as e:
                print(f"❌ validate_mobile_money_name() failed: {e}")
            
            # Test database operations
            print("Testing database operations...")
            try:
                db.session.add(confirmation)
                db.session.commit()
                print("✅ Database commit successful")
                print(f"Created confirmation with ID: {confirmation.id}")
                
                # Clean up
                db.session.delete(confirmation)
                db.session.commit()
                print("✅ Cleanup successful")
                
            except Exception as e:
                print(f"❌ Database operation failed: {e}")
                db.session.rollback()
        
        except Exception as e:
            print(f"❌ PaymentConfirmation creation failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_payment_confirmation()
