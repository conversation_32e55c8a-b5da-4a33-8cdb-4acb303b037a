{% extends "base.html" %}

{% block title %}Manage Users - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users me-2"></i>Manage Admin Users</h1>
    <a href="{{ url_for('create_user') }}" class="btn btn-success">
        <i class="fas fa-user-plus me-2"></i>Add New User
    </a>
</div>

<!-- Statistics Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ users|length }}</h4>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h4>{{ users|selectattr('is_active', 'equalto', True)|list|length }}</h4>
                <p class="mb-0">{{ t('Active') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="fas fa-user-times fa-2x mb-2"></i>
                <h4>{{ users|selectattr('is_active', 'equalto', False)|list|length }}</h4>
                <p class="mb-0">Disabled</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-crown fa-2x mb-2"></i>
                <h4>{{ users|selectattr('role', 'equalto', 'super_admin')|list|length }}</h4>
                <p class="mb-0">Super Admins</p>
            </div>
        </div>
    </div>
</div>

<!-- Security Notice -->
<div class="alert alert-warning mb-4">
    <i class="fas fa-shield-alt me-2"></i>
    <strong>Security Notice:</strong> Only super admins can manage user accounts. Regular admins cannot access this section.
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Admin Users
        </h5>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>User</th>
                        <th>{{ t('Contact') }}</th>
                        <th>Role</th>
                        <th>{{ t('Status') }}</th>
                        <th>Last Login</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr class="{{ 'table-warning' if not user.is_active else '' }}">
                        <td>
                            <div>
                                <strong>{{ user.username }}</strong>
                                {% if user.full_name %}
                                    <br><small class="text-muted">{{ user.full_name }}</small>
                                {% endif %}
                                {% if user.id == current_user.id %}
                                    <span class="badge bg-info ms-2">You</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if user.email %}
                                <i class="fas fa-envelope me-1"></i>{{ user.email }}
                            {% else %}
                                <span class="text-muted">No email</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge 
                                {% if user.role == 'super_admin' %}bg-danger
                                {% else %}bg-primary{% endif %}">
                                {% if user.role == 'super_admin' %}
                                    <i class="fas fa-crown me-1"></i>Super Admin
                                {% else %}
                                    <i class="fas fa-user me-1"></i>Admin
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="badge {{ 'bg-success' if user.is_active else 'bg-secondary' }}">
                                {{ 'Active' if user.is_active else 'Disabled' }}
                            </span>
                        </td>
                        <td>
                            {% if user.last_login %}
                                <span title="{{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}">
                                    {{ user.last_login.strftime('%b %d, %Y') }}
                                </span>
                                <br>
                                <small class="text-muted">
                                    {{ user.last_login.strftime('%H:%M') }}
                                </small>
                            {% else %}
                                <span class="text-muted">Never</span>
                            {% endif %}
                        </td>
                        <td>
                            <span title="{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}">
                                {{ user.created_at.strftime('%b %d, %Y') }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <a href="{{ url_for('edit_user', user_id=user.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                
                                {% if user.role != 'super_admin' and user.id != current_user.id %}
                                <button class="btn btn-outline-{{ 'warning' if user.is_active else 'success' }} btn-sm" 
                                        onclick="toggleUserStatus({{ user.id }})">
                                    {% if user.is_active %}
                                        <i class="fas fa-user-times"></i> Disable
                                    {% else %}
                                        <i class="fas fa-user-check"></i> Enable
                                    {% endif %}
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Users Found</h4>
            <p class="text-muted">Start by creating your first admin user</p>
            <a href="{{ url_for('create_user') }}" class="btn btn-success">
                <i class="fas fa-user-plus me-2"></i>Add First User
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Information Cards -->
<div class="row g-4 mt-4">
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>User Roles
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong><i class="fas fa-crown text-danger me-2"></i>Super Admin:</strong>
                    <ul class="mt-1 mb-0">
                        <li>Full system access</li>
                        <li>Manage users and companies</li>
                        <li>Configure pricing tiers</li>
                        <li>Generate bills and reports</li>
                    </ul>
                </div>
                <div>
                    <strong><i class="fas fa-user text-primary me-2"></i>Admin:</strong>
                    <ul class="mt-1 mb-0">
                        <li>Manage payments and invoices</li>
                        <li>View company information</li>
                        <li>Generate receipts</li>
                        <li>Limited system access</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Security Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Strong Passwords:</strong> Require minimum 8 characters with mixed case, numbers, and symbols</li>
                    <li><strong>Regular Review:</strong> Audit user accounts monthly</li>
                    <li><strong>Immediate Removal:</strong> Disable accounts for departing staff</li>
                    <li><strong>Limited Super Admins:</strong> Keep super admin accounts to minimum</li>
                    <li><strong>Login Monitoring:</strong> Monitor last login times regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId) {
    if (confirm('Are you sure you want to change this user\'s status?')) {
        fetch(`/admin/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}
</script>
{% endblock %}
