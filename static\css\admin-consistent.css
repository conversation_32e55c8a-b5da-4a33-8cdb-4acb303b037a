/* EXLIPA Admin Consistent Styling */
/* Ensures all admin pages have beautiful, consistent design */

/* Admin Page Headers */
.admin-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.admin-header h1 {
    color: #1e293b;
    font-weight: 800;
    font-size: 2.25rem;
    margin-bottom: 0.5rem;
}

.admin-header .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

/* Beautiful Admin Statistics Cards */
.admin-stat-card {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-radius: 16px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
}

.admin-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.admin-stat-card.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.admin-stat-card.success:hover {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.admin-stat-card.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
}

.admin-stat-card.warning:hover {
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.admin-stat-card.danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
}

.admin-stat-card.danger:hover {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.admin-stat-card.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.admin-stat-card.info:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.admin-stat-card.cyan {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.2);
}

.admin-stat-card.cyan:hover {
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.admin-stat-card i {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.admin-stat-card h3,
.admin-stat-card h4 {
    color: white;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.admin-stat-card p {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin: 0;
    font-size: 1rem;
}

/* Admin Tables */
.admin-table-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.admin-table-card .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
}

.admin-table-card .card-header h5 {
    color: #1e293b;
    font-weight: 700;
    margin: 0;
    font-size: 1.25rem;
}

.admin-table-card .table {
    margin: 0;
    background: white;
}

.admin-table-card .table th {
    background: #f8fafc;
    color: #374151;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.25rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-table-card .table td {
    color: #1e293b;
    border-bottom: 1px solid #f3f4f6;
    padding: 1rem 1.25rem;
    vertical-align: middle;
}

.admin-table-card .table tbody tr:hover {
    background: #f8fafc;
}

/* Admin Buttons */
.admin-btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.admin-btn-primary:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3);
}

.admin-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.admin-btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3);
}

.admin-btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.admin-btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(245, 158, 11, 0.3);
}

.admin-btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.admin-btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.3);
}

/* Admin Filter Cards */
.admin-filter-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.admin-filter-card .card-body {
    padding: 2rem;
}

/* Admin Form Controls */
.admin-form-control {
    border: 1px solid #d1d5db;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    background: white;
    color: #1e293b;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.admin-form-control:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: white;
    color: #1e293b;
}

/* Admin Badges */
.admin-badge-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.admin-badge-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.admin-badge-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.admin-badge-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Admin Page Background */
.admin-page {
    background: #f8fafc;
    min-height: 100vh;
    padding: 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-stat-card {
        margin-bottom: 1rem;
    }
    
    .admin-header h1 {
        font-size: 1.75rem;
    }
    
    .admin-table-card {
        border-radius: 12px;
    }
}
