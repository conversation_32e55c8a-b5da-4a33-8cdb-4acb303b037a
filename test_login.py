#!/usr/bin/env python3
"""
Test login functionality directly
"""

from app import app, db, User
from werkzeug.security import check_password_hash

def test_login():
    """Test login functionality step by step"""
    with app.app_context():
        print("=== TESTING LOGIN FUNCTIONALITY ===")
        
        # Test data
        test_cases = [
            ('admin', 'admin123', 'master_admin'),
            ('useradmin', 'useradmin123', 'user_admin'),
            ('juma', 'juma123', 'company_user'),
            ('test', 'test123', 'admin')
        ]
        
        for username, password, expected_role in test_cases:
            print(f"\n🧪 Testing: {username} / {password}")
            
            # Step 1: Find user
            user = User.query.filter_by(username=username).first()
            print(f"   User found: {user is not None}")
            
            if user:
                print(f"   Role: {user.role} (expected: {expected_role})")
                print(f"   Active: {user.is_active}")
                print(f"   Locked: {user.is_account_locked()}")
                
                # Step 2: Check password
                password_valid = check_password_hash(user.password_hash, password)
                print(f"   Password valid: {password_valid}")
                
                # Step 3: Check role-specific query
                if expected_role == 'master_admin':
                    role_user = User.query.filter_by(username=username, is_active=True, role='master_admin').first()
                elif expected_role == 'user_admin':
                    role_user = User.query.filter_by(username=username, is_active=True, role='user_admin').first()
                elif expected_role == 'company_user':
                    role_user = User.query.filter_by(username=username, is_active=True, role='company_user').first()
                else:
                    role_user = User.query.filter_by(username=username, is_active=True, role='admin').first()
                
                print(f"   Role-specific query result: {role_user is not None}")
                
                # Step 4: Overall login check
                login_should_work = (user and user.is_active and not user.is_account_locked() and password_valid and role_user)
                print(f"   ✅ Login should work: {login_should_work}")
            else:
                print(f"   ❌ User not found!")

if __name__ == '__main__':
    test_login()
