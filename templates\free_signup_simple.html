<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Signup - EXLIPA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Inter', sans-serif;
        }
        
        .signup-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            border-radius: 10px;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 1rem 2rem;
            border-radius: 25px;
        }
        
        .free-badge {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 700;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="signup-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4 fw-bold">🚀 EXLIPA Free Signup</h1>
                <div class="free-badge d-inline-block mb-3">
                    100% FREE - No Payment Required!
                </div>
                <p class="lead">Create your free account and start accepting payments immediately</p>
            </div>
            
            <!-- Free Plan Benefits -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>✅ What You Get:</h5>
                    <ul class="list-unstyled">
                        <li>📱 100 free transactions monthly</li>
                        <li>💳 M-Pesa, Tigo Pesa, Airtel Money</li>
                        <li>📊 Basic dashboard & reports</li>
                        <li>🔧 24/7 support</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <strong>🎉 No Setup Fees!</strong><br>
                        Start using immediately after registration
                    </div>
                </div>
            </div>
            
            <!-- Registration Form -->
            <form method="POST" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="full_name" class="form-label fw-bold">
                            <i class="fas fa-user me-2"></i>Full Name *
                        </label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required
                               placeholder="Your full name">
                    </div>
                    
                    <div class="col-md-6">
                        <label for="email" class="form-label fw-bold">
                            <i class="fas fa-envelope me-2"></i>Email Address *
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="col-md-6">
                        <label for="company_name" class="form-label fw-bold">
                            <i class="fas fa-building me-2"></i>Company Name *
                        </label>
                        <input type="text" class="form-control" id="company_name" name="company_name" required
                               placeholder="Your business name">
                    </div>
                    
                    <div class="col-md-6">
                        <label for="phone" class="form-label fw-bold">
                            <i class="fas fa-phone me-2"></i>Phone Number
                        </label>
                        <input type="tel" class="form-control" id="phone" name="phone"
                               placeholder="+255...">
                    </div>

                    <div class="col-md-6">
                        <label for="password" class="form-label fw-bold">
                            <i class="fas fa-lock me-2"></i>Password *
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required
                               placeholder="Create a secure password" minlength="6">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>

                    <div class="col-md-6">
                        <label for="confirm_password" class="form-label fw-bold">
                            <i class="fas fa-lock me-2"></i>Confirm Password *
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required
                               placeholder="Confirm your password" minlength="6">
                    </div>
                    
                    <div class="col-12">
                        <label for="business_type" class="form-label fw-bold">
                            <i class="fas fa-store me-2"></i>Business Type
                        </label>
                        <select class="form-control" id="business_type" name="business_type">
                            <option value="">Select business type</option>
                            <option value="retail">Retail</option>
                            <option value="restaurant">Restaurant</option>
                            <option value="services">Services</option>
                            <option value="ecommerce">E-commerce</option>
                            <option value="education">Education</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-warning">Terms of Service</a> and <a href="#" class="text-warning">Privacy Policy</a>
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            Create My Free Account
                        </button>
                    </div>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <p>Already have an account? <a href="/user-admin-login" class="text-warning">Login here</a></p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        function validatePasswords() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const confirmField = document.getElementById('confirm_password');

            if (password !== confirmPassword) {
                confirmField.setCustomValidity('Passwords do not match');
                return false;
            } else {
                confirmField.setCustomValidity('');
                return true;
            }
        }

        // Add event listeners for password validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');

            passwordField.addEventListener('input', validatePasswords);
            confirmPasswordField.addEventListener('input', validatePasswords);
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false || !validatePasswords()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
