{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Check Malipo Hali - EXLIPA
{%- else -%}
    Check Payment Status - EXLIPA
{%- endif -%}
{%- endblock -%}

{% block content %}
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
}

body {
    background: var(--primary-gradient);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.status-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.status-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    padding: 2rem;
    transition: all 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.status-header {
    text-align: center;
    margin-bottom: 2rem;
}

.status-title {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.status-subtitle {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
}

.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    width: 100%;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.form-control:hover {
    border-color: #cbd5e1;
}

.submit-button {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.submit-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.help-text {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.5rem;
}

.info-box {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.back-link:hover {
    color: #764ba2;
    transform: translateX(-2px);
}

@media (max-width: 576px) {
    .status-container { padding: 1rem 0.5rem; }
    .status-card { padding: 1.5rem; }
    .status-title { font-size: 1.5rem; }
}
</style>

<div class="status-container">
    <!-- Back Link -->
    <a href="{{ url_for('index') }}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        <span>Back to Home</span>
    </a>

    <!-- Status Check Card -->
    <div class="status-card">
        <div class="status-header">
            <h1 class="status-title">
                <i class="fas fa-search me-2"></i>
                Check Payment Status
            </h1>
            <p class="status-subtitle">Track your payment and get your registration link</p>
        </div>

        <!-- Info Box -->
        <div class="info-box">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Need help?</strong> Use your reference ID (REF000001) or transaction ID from your payment confirmation SMS.
        </div>

        <!-- Status Check Form -->
        <form method="POST">
            <div class="form-group">
                <label for="reference_id" class="form-label">
                    <i class="fas fa-receipt me-2"></i>Reference ID (Optional)
                </label>
                <input type="text" class="form-control" id="reference_id" name="reference_id" 
                       placeholder="REF000001" value="{{ request.form.get('reference_id', '') }}">
                <div class="help-text">
                    The reference ID you received after submitting your payment confirmation
                </div>
            </div>

            <div class="form-group">
                <label for="transaction_id" class="form-label">
                    <i class="fas fa-mobile-alt me-2"></i>Transaction ID (Optional)
                </label>
                <input type="text" class="form-control" id="transaction_id" name="transaction_id" 
                       placeholder="ABC123XYZ" value="{{ request.form.get('transaction_id', '') }}">
                <div class="help-text">
                    The transaction ID from your mobile money payment SMS
                </div>
            </div>

            <div class="form-group">
                <label for="customer_name" class="form-label">
                    <i class="fas fa-user me-2"></i>Your Name
                </label>
                <input type="text" class="form-control" id="customer_name" name="customer_name" 
                       placeholder="Enter your full name" value="{{ request.form.get('customer_name', '') }}" required>
                <div class="help-text">
                    The name you used when submitting your payment confirmation
                </div>
            </div>

            <button type="submit" class="submit-button">
                <i class="fas fa-search me-2"></i>
                <span>{{ t('Check Payment Status') }}</span>
            </button>
        </form>

        <!-- Additional Help -->
        <div class="text-center mt-3">
            <small class="text-muted">
                <i class="fas fa-question-circle me-1"></i>
                Having trouble? Contact support at <strong><EMAIL></strong>
            </small>
        </div>
    </div>
</div>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const refId = document.getElementById('reference_id');
    const transId = document.getElementById('transaction_id');
    
    form.addEventListener('submit', function(e) {
        if (!refId.value.trim() && !transId.value.trim()) {
            e.preventDefault();
            alert('Please provide either a Reference ID or Transaction ID.');
            return false;
        }
    });
    
    // Auto-format reference ID
    refId.addEventListener('input', function(e) {
        let value = e.target.value.toUpperCase();
        if (value && !value.startsWith('REF')) {
            value = 'REF' + value.replace(/[^0-9]/g, '');
        }
        e.target.value = value;
    });
});
</script>

{% endblock %}
