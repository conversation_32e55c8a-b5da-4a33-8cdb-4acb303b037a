#!/usr/bin/env python3
"""
Migrate existing database to add new landing page customization columns
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """Create a backup of the existing database"""
    db_path = 'payment_system.db'
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f'payment_system_backup_{timestamp}.db'
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    return None

def check_existing_columns():
    """Check what columns already exist in client_company table"""
    try:
        conn = sqlite3.connect('payment_system.db')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_company'")
        if not cursor.fetchone():
            print("❌ client_company table does not exist")
            return None
            
        # Get existing columns
        cursor.execute("PRAGMA table_info(client_company)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"📋 Existing columns ({len(columns)}): {', '.join(columns)}")
        
        conn.close()
        return columns
        
    except sqlite3.Error as e:
        print(f"❌ Error checking columns: {e}")
        return None

def add_missing_columns():
    """Add missing columns to the client_company table"""
    try:
        conn = sqlite3.connect('payment_system.db')
        cursor = conn.cursor()
        
        # Get current columns
        cursor.execute("PRAGMA table_info(client_company)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        # Define new columns to add
        new_columns = [
            ("pricing_tier", "VARCHAR(20)", "'Starter'"),
            ("primary_color", "VARCHAR(7)", "'#3b82f6'"),
            ("secondary_color", "VARCHAR(7)", "'#1d4ed8'"),
            ("font_family", "VARCHAR(50)", "'Inter'"),
            ("template_style", "VARCHAR(50)", "'basic'")
        ]
        
        added_count = 0
        for column_name, column_type, default_value in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE client_company ADD COLUMN {column_name} {column_type} DEFAULT {default_value}"
                    cursor.execute(sql)
                    print(f"✅ Added column: {column_name} ({column_type})")
                    added_count += 1
                except sqlite3.Error as e:
                    print(f"❌ Error adding {column_name}: {e}")
                    return False
            else:
                print(f"⏭️  Column {column_name} already exists")
        
        if added_count > 0:
            conn.commit()
            print(f"✅ Successfully added {added_count} new columns")
        else:
            print("ℹ️  No new columns needed")
            
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def verify_migration():
    """Verify that the migration was successful"""
    try:
        conn = sqlite3.connect('payment_system.db')
        cursor = conn.cursor()
        
        # Test query with new columns
        cursor.execute("""
            SELECT id, company_name, pricing_tier, primary_color, font_family 
            FROM client_company 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            print("🧪 Migration verification:")
            print(f"   Company ID: {result[0]}")
            print(f"   Company Name: {result[1]}")
            print(f"   Pricing Tier: {result[2]}")
            print(f"   Primary Color: {result[3]}")
            print(f"   Font Family: {result[4]}")
            print("✅ Migration verification successful!")
        else:
            print("ℹ️  No companies found (empty table)")
            
        # Check final column count
        cursor.execute("PRAGMA table_info(client_company)")
        final_columns = [row[1] for row in cursor.fetchall()]
        print(f"📊 Final column count: {len(final_columns)}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 EXLIPA Database Migration")
    print("=" * 50)
    
    # Check if database exists
    if not os.path.exists('payment_system.db'):
        print("❌ Database file 'payment_system.db' not found!")
        print("Please run the application first to create the database.")
        return False
    
    # Backup database
    backup_path = backup_database()
    if backup_path:
        print(f"💾 Backup created: {backup_path}")
    
    # Check existing columns
    existing_columns = check_existing_columns()
    if existing_columns is None:
        print("❌ Could not check existing columns")
        return False
    
    # Add missing columns
    print("\n🔄 Adding missing columns...")
    if add_missing_columns():
        print("\n🧪 Verifying migration...")
        if verify_migration():
            print("\n🎉 Migration completed successfully!")
            print("You can now restart the server and use the tier-based landing page features.")
            return True
        else:
            print("\n❌ Migration verification failed!")
            return False
    else:
        print("\n❌ Migration failed!")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n💡 If migration failed, you can:")
        print("1. Restore from backup")
        print("2. Check the error messages above")
        print("3. Try running the migration again")
