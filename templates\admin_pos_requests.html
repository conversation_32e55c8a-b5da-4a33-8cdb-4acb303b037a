{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Inasubiri POS Unlock Requests - Msimamizi
{%- else -%}
    Pending POS Unlock Requests - Admin
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-cash-register me-2 text-warning"></i>Pending POS Unlock Requests</h2>
    {% if pending_companies %}
    <div class="table-responsive">
        <table class="table table-bordered align-middle">
            <thead class="table-light">
                <tr>
                    <th>{{ t('Company Name') }}</th>
                    <th>{{ t('Email') }}</th>
                    <th>{{ t('Phone') }}</th>
                    <th>Requested On</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                {% for company in pending_companies %}
                <tr>
                    <td>{{ company.company_name }}</td>
                    <td>{{ company.company_email }}</td>
                    <td>{{ company.company_phone or 'N/A' }}</td>
                    <td>{{ company.updated_at.strftime('%Y-%m-%d %H:%M') if company.updated_at else 'N/A' }}</td>
                    <td>
                        <form action="{{ url_for('approve_pos', company_id=company.id) }}" method="post" style="display:inline;">
                            <button type="submit" class="btn btn-success btn-sm"><i class="fas fa-unlock me-1"></i>Approve</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">No pending POS unlock requests at this time.</div>
    {% endif %}
    <div class="mt-3">
        <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-link">&larr; Back to Dashboard</a>
    </div>
</div>
{% endblock %}
