{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Advanced Landing Page Builder - EXLIPA
{%- else -%}
    Advanced Landing Page Builder - EXLIPA
{%- endif -%}
{%- endblock -%}

{% block content %}
<style>
/* Modern Builder Interface */
.builder-container {
    display: flex;
    height: 100vh;
    background: #f8fafc;
}

.builder-sidebar {
    width: 350px;
    background: white;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.builder-canvas {
    flex: 1;
    background: #f1f5f9;
    overflow-y: auto;
    position: relative;
}

.preview-frame {
    max-width: 1200px;
    margin: 20px auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.sidebar-section {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.sidebar-section h5 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-picker-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.color-option {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
    border-color: #3b82f6;
}

.color-option.active {
    border-color: #1d4ed8;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.template-card {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.template-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.template-card.active {
    border-color: #1d4ed8;
    background: #eff6ff;
}

.component-item {
    padding: 12px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: grab;
    transition: all 0.3s ease;
    background: white;
}

.component-item:hover {
    border-color: #3b82f6;
    transform: translateX(5px);
}

.style-control {
    margin-bottom: 15px;
}

.style-control label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 5px;
}

.range-slider {
    width: 100%;
    margin-bottom: 10px;
}

.preview-device-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.device-btn {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.device-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

/* Live Preview Styles */
.landing-preview {
    transition: all 0.3s ease;
}

.hero-section {
    padding: 80px 20px;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color, #3b82f6), var(--secondary-color, #1d4ed8));
    color: white;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.cta-button {
    background: white;
    color: var(--primary-color, #3b82f6);
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.section {
    padding: 60px 20px;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.payment-card {
    background: white;
    padding: 30px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .builder-container {
        flex-direction: column;
    }
    
    .builder-sidebar {
        width: 100%;
        height: 300px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
}
</style>

<div class="builder-container">
    <!-- Sidebar Controls -->
    <div class="builder-sidebar">
        <!-- Template Selection -->
        <div class="sidebar-section">
            <h5><i class="fas fa-palette"></i> Choose Template</h5>
            <div class="template-grid">
                <div class="template-card active" data-template="modern">
                    <i class="fas fa-laptop fa-2x mb-2 text-primary"></i>
                    <div class="fw-bold">Modern Business</div>
                    <small class="text-muted">Clean & Professional</small>
                </div>
                <div class="template-card" data-template="restaurant">
                    <i class="fas fa-utensils fa-2x mb-2 text-warning"></i>
                    <div class="fw-bold">Restaurant</div>
                    <small class="text-muted">Food & Dining</small>
                </div>
                <div class="template-card" data-template="retail">
                    <i class="fas fa-shopping-bag fa-2x mb-2 text-success"></i>
                    <div class="fw-bold">Retail Shop</div>
                    <small class="text-muted">Product Sales</small>
                </div>
                <div class="template-card" data-template="service">
                    <i class="fas fa-handshake fa-2x mb-2 text-info"></i>
                    <div class="fw-bold">Service Provider</div>
                    <small class="text-muted">Professional Services</small>
                </div>
            </div>
        </div>

        <!-- Share & Publish Section -->
        <div class="sidebar-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; margin: 10px; border: none;">
            <h5><i class="fas fa-share-alt"></i> Share Your Landing Page</h5>

            <!-- Landing Page URL -->
            <div class="mb-3">
                <label class="form-label fw-bold">Your Landing Page URL:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="landingPageUrl"
                           value="{{ url_for('public_company_landing', company_id=company.id, _external=True) }}"
                           readonly style="background: rgba(255,255,255,0.9); color: #333;">
                    <button class="btn btn-light" type="button" onclick="copyLandingUrl()" title="Copy URL">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <small class="text-light opacity-75">Share this link with your customers</small>
            </div>

            <!-- Quick Share Buttons -->
            <div class="d-flex gap-2 mb-3">
                <button class="btn btn-light btn-sm flex-fill" onclick="shareWhatsApp()" title="Share on WhatsApp">
                    <i class="fab fa-whatsapp text-success"></i> WhatsApp
                </button>
                <button class="btn btn-light btn-sm flex-fill" onclick="shareEmail()" title="Share via Email">
                    <i class="fas fa-envelope text-primary"></i> Email
                </button>
                <button class="btn btn-light btn-sm flex-fill" onclick="shareSMS()" title="Share via SMS">
                    <i class="fas fa-sms text-warning"></i> SMS
                </button>
            </div>

            <!-- QR Code -->
            <div class="text-center mb-3">
                <div class="bg-white p-3 rounded d-inline-block">
                    <div id="qrcode" style="width: 120px; height: 120px;"></div>
                </div>
                <div class="mt-2">
                    <small class="text-light opacity-75">QR Code for easy sharing</small>
                </div>
            </div>

            <!-- Preview & Publish -->
            <div class="d-flex gap-2">
                <button class="btn btn-light flex-fill" onclick="previewLandingPage()" title="Preview in new tab">
                    <i class="fas fa-eye me-1"></i>Preview
                </button>
                <button class="btn btn-success flex-fill" onclick="publishLandingPage()" title="Save and publish">
                    <i class="fas fa-rocket me-1"></i>Publish
                </button>
            </div>
        </div>

        <!-- Color Customization -->
        <div class="sidebar-section">
            <h5><i class="fas fa-paint-brush"></i> Brand Colors</h5>
            <label>Primary Color</label>
            <div class="color-picker-grid">
                <div class="color-option active" style="background: #3b82f6" data-color="#3b82f6"></div>
                <div class="color-option" style="background: #ef4444" data-color="#ef4444"></div>
                <div class="color-option" style="background: #10b981" data-color="#10b981"></div>
                <div class="color-option" style="background: #f59e0b" data-color="#f59e0b"></div>
                <div class="color-option" style="background: #8b5cf6" data-color="#8b5cf6"></div>
                <div class="color-option" style="background: #06b6d4" data-color="#06b6d4"></div>
                <div class="color-option" style="background: #84cc16" data-color="#84cc16"></div>
                <div class="color-option" style="background: #f97316" data-color="#f97316"></div>
            </div>
            <input type="color" class="form-control" id="customColor" value="#3b82f6">
        </div>

        <!-- Typography -->
        <div class="sidebar-section">
            <h5><i class="fas fa-font"></i> Typography</h5>
            <div class="style-control">
                <label>Font Family</label>
                <select class="form-select" id="fontFamily">
                    <option value="Inter">Inter (Modern)</option>
                    <option value="Poppins">Poppins (Friendly)</option>
                    <option value="Roboto">Roboto (Clean)</option>
                    <option value="Playfair Display">Playfair (Elegant)</option>
                </select>
            </div>
            <div class="style-control">
                <label>Heading Size</label>
                <input type="range" class="range-slider" min="24" max="72" value="48" id="headingSize">
                <span id="headingSizeValue">48px</span>
            </div>
        </div>

        <!-- Layout Options -->
        <div class="sidebar-section">
            <h5><i class="fas fa-th-large"></i> Layout</h5>
            <div class="style-control">
                <label>Section Spacing</label>
                <input type="range" class="range-slider" min="40" max="120" value="80" id="sectionSpacing">
                <span id="spacingValue">80px</span>
            </div>
            <div class="style-control">
                <label>Border Radius</label>
                <input type="range" class="range-slider" min="0" max="30" value="16" id="borderRadius">
                <span id="radiusValue">16px</span>
            </div>
        </div>

        <!-- Save Button -->
        <div class="sidebar-section">
            <button class="btn btn-success w-100 btn-lg">
                <i class="fas fa-save me-2"></i>Save Landing Page
            </button>
        </div>
    </div>

    <!-- Preview Canvas -->
    <div class="builder-canvas">
        <div class="preview-device-toggle">
            <button class="device-btn active" data-device="desktop">
                <i class="fas fa-desktop"></i> Desktop
            </button>
            <button class="device-btn" data-device="tablet">
                <i class="fas fa-tablet-alt"></i> Tablet
            </button>
            <button class="device-btn" data-device="mobile">
                <i class="fas fa-mobile-alt"></i> Mobile
            </button>
        </div>

        <div class="preview-frame" id="previewFrame">
            <!-- Live Preview Content -->
            <div class="landing-preview">
                <!-- Hero Section -->
                <div class="hero-section" id="heroSection">
                    <div class="container">
                        <h1 class="hero-title" id="heroTitle">{{ company.company_name }}</h1>
                        <p class="hero-subtitle" id="heroSubtitle">{{ company.landing_page_description or 'Professional payment solutions for your business' }}</p>
                        <a href="#payment" class="cta-button">{{ t('Make Payment') }}</a>
                    </div>
                </div>

                <!-- About Section -->
                <div class="section" style="background: #f8fafc;">
                    <div class="container text-center">
                        <h2>About Our Services</h2>
                        <p class="lead">{{ company.custom_message or 'We provide excellent services with secure payment options.' }}</p>
                    </div>
                </div>

                <!-- Payment Methods Section -->
                <div class="section" id="payment">
                    <div class="container">
                        <h2 class="text-center mb-5">{{ t('Payment Methods') }}</h2>
                        <div class="payment-methods">
                            {% if company.mpesa_till %}
                            <div class="payment-card">
                                <i class="fas fa-mobile-alt fa-3x text-success mb-3"></i>
                                <h5>{{ t('M-Pesa') }}</h5>
                                <p class="fw-bold">{{ company.mpesa_till }}</p>
                            </div>
                            {% endif %}
                            {% if company.tigo_paybill %}
                            <div class="payment-card">
                                <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                                <h5>{{ t('Tigo Pesa') }}</h5>
                                <p class="fw-bold">{{ company.tigo_paybill }}</p>
                            </div>
                            {% endif %}
                            {% if company.airtel_merchant %}
                            <div class="payment-card">
                                <i class="fas fa-phone fa-3x text-danger mb-3"></i>
                                <h5>{{ t('Airtel Money') }}</h5>
                                <p class="fw-bold">{{ company.airtel_merchant }}</p>
                            </div>
                            {% endif %}
                            {% if company.crdb_merchant %}
                            <div class="payment-card">
                                <i class="fas fa-university fa-3x text-info mb-3"></i>
                                <h5>CRDB Bank</h5>
                                <p class="fw-bold">{{ company.crdb_merchant }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Contact Section -->
                <div class="section" style="background: #1f2937; color: white;">
                    <div class="container text-center">
                        <h2>Contact Information</h2>
                        <p><i class="fas fa-envelope me-2"></i>{{ company.company_email }}</p>
                        {% if company.company_phone %}
                        <p><i class="fas fa-phone me-2"></i>{{ company.company_phone }}</p>
                        {% endif %}
                        {% if company.company_address %}
                        <p><i class="fas fa-map-marker-alt me-2"></i>{{ company.company_address }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Advanced Landing Page Builder JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Advanced Landing Page Builder loaded');
    
    // Color picker functionality
    const colorOptions = document.querySelectorAll('.color-option');
    const customColorInput = document.getElementById('customColor');
    const heroSection = document.getElementById('heroSection');
    
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            colorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            
            const color = this.dataset.color;
            updatePrimaryColor(color);
        });
    });
    
    customColorInput.addEventListener('change', function() {
        updatePrimaryColor(this.value);
    });
    
    function updatePrimaryColor(color) {
        document.documentElement.style.setProperty('--primary-color', color);
        heroSection.style.background = `linear-gradient(135deg, ${color}, ${adjustBrightness(color, -20)})`;
    }
    
    // Typography controls
    const fontFamily = document.getElementById('fontFamily');
    const headingSize = document.getElementById('headingSize');
    const headingSizeValue = document.getElementById('headingSizeValue');
    
    fontFamily.addEventListener('change', function() {
        document.querySelector('.landing-preview').style.fontFamily = this.value;
    });
    
    headingSize.addEventListener('input', function() {
        headingSizeValue.textContent = this.value + 'px';
        document.getElementById('heroTitle').style.fontSize = this.value + 'px';
    });
    
    // Layout controls
    const sectionSpacing = document.getElementById('sectionSpacing');
    const spacingValue = document.getElementById('spacingValue');
    const borderRadius = document.getElementById('borderRadius');
    const radiusValue = document.getElementById('radiusValue');
    
    sectionSpacing.addEventListener('input', function() {
        spacingValue.textContent = this.value + 'px';
        document.querySelectorAll('.section').forEach(section => {
            section.style.padding = this.value + 'px 20px';
        });
    });
    
    borderRadius.addEventListener('input', function() {
        radiusValue.textContent = this.value + 'px';
        document.querySelectorAll('.payment-card').forEach(card => {
            card.style.borderRadius = this.value + 'px';
        });
    });
    
    // Device preview toggle
    const deviceButtons = document.querySelectorAll('.device-btn');
    const previewFrame = document.getElementById('previewFrame');
    
    deviceButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            deviceButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const device = this.dataset.device;
            switch(device) {
                case 'desktop':
                    previewFrame.style.maxWidth = '1200px';
                    break;
                case 'tablet':
                    previewFrame.style.maxWidth = '768px';
                    break;
                case 'mobile':
                    previewFrame.style.maxWidth = '375px';
                    break;
            }
        });
    });
    
    // Template selection
    const templateCards = document.querySelectorAll('.template-card');
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            templateCards.forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            
            const template = this.dataset.template;
            applyTemplate(template);
        });
    });
    
    function applyTemplate(template) {
        const heroSection = document.getElementById('heroSection');
        
        switch(template) {
            case 'modern':
                updatePrimaryColor('#3b82f6');
                break;
            case 'restaurant':
                updatePrimaryColor('#f59e0b');
                break;
            case 'retail':
                updatePrimaryColor('#10b981');
                break;
            case 'service':
                updatePrimaryColor('#8b5cf6');
                break;
        }
    }
    
    function adjustBrightness(color, amount) {
        const usePound = color[0] === '#';
        const col = usePound ? color.slice(1) : color;
        const num = parseInt(col, 16);
        let r = (num >> 16) + amount;
        let g = (num >> 8 & 0x00FF) + amount;
        let b = (num & 0x0000FF) + amount;
        r = r > 255 ? 255 : r < 0 ? 0 : r;
        g = g > 255 ? 255 : g < 0 ? 0 : g;
        b = b > 255 ? 255 : b < 0 ? 0 : b;
        return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }

    // Generate QR Code on page load
    generateQRCode();
});

// Sharing and Publishing Functions
function copyLandingUrl() {
    const urlInput = document.getElementById('landingPageUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showToast('✅ URL copied to clipboard!', 'success');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(urlInput.value).then(() => {
            showToast('✅ URL copied to clipboard!', 'success');
        }).catch(() => {
            showToast('❌ Failed to copy URL', 'error');
        });
    }
}

function shareWhatsApp() {
    const url = document.getElementById('landingPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const message = `Check out ${companyName}'s payment page: ${url}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareEmail() {
    const url = document.getElementById('landingPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const subject = `Payment Page - ${companyName}`;
    const body = `Hi,\n\nYou can make payments to ${companyName} using this secure payment page:\n\n${url}\n\nThank you!`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = emailUrl;
}

function shareSMS() {
    const url = document.getElementById('landingPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const message = `Pay ${companyName} securely: ${url}`;
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`;
    window.location.href = smsUrl;
}

function previewLandingPage() {
    const url = document.getElementById('landingPageUrl').value;
    window.open(url, '_blank');
}

function publishLandingPage() {
    // Save current customizations
    showToast('🚀 Landing page published successfully!', 'success');
    // You can add actual save functionality here
}

function generateQRCode() {
    const url = document.getElementById('landingPageUrl').value;
    const qrContainer = document.getElementById('qrcode');

    // Clear existing QR code
    qrContainer.innerHTML = '';

    // Generate QR code using a simple method
    const qrSize = 120;
    const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}x${qrSize}&data=${encodeURIComponent(url)}`;

    const qrImg = document.createElement('img');
    qrImg.src = qrUrl;
    qrImg.style.width = '100%';
    qrImg.style.height = '100%';
    qrImg.alt = 'QR Code for Landing Page';

    qrContainer.appendChild(qrImg);
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="me-2">${message}</span>
            <button type="button" class="btn-close btn-close-white ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// Add CSS for toast animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
