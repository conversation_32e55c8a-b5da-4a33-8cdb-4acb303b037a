#!/usr/bin/env python3
"""
Quick test to verify contact route is working
"""

from app import app

def test_routes():
    with app.test_client() as client:
        # Test contact page
        response = client.get('/contact')
        print(f'Contact page status: {response.status_code}')
        
        # Test homepage
        try:
            response2 = client.get('/')
            print(f'Homepage status: {response2.status_code}')
        except Exception as e:
            print(f'Homepage error: {str(e)}')
        
        # Test if contact route is in url_map
        contact_routes = [rule for rule in app.url_map.iter_rules() if 'contact' in rule.endpoint]
        print(f'Contact routes found: {len(contact_routes)}')
        for route in contact_routes:
            print(f'  - {route.endpoint}: {route.rule}')

if __name__ == '__main__':
    test_routes()
