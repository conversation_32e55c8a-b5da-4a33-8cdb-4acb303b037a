{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    System Monitoring - Msimamizi Dashibodi
{%- else -%}
    System Monitoring - Admin Dashboard
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3"><i class="fas fa-chart-line me-2"></i>System Monitoring</h1>
                <button class="btn btn-primary" onclick="refreshMetrics()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- System Health Status -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
                    <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>System Health</h5>
                </div>
                <div class="card-body">
                    <div id="health-status">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div id="quick-stats">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Metrics -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-building me-2"></i>Companies</h6>
                </div>
                <div class="card-body">
                    <div id="companies-metrics">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>{{ t('Payments') }}</h6>
                </div>
                <div class="card-body">
                    <div id="payments-metrics">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-invoice me-2"></i>{{ t('Invoices') }}</h6>
                </div>
                <div class="card-body">
                    <div id="invoices-metrics">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Revenue Metrics</h6>
                </div>
                <div class="card-body">
                    <div id="revenue-metrics">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Recent System Activity</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        System logs are available through your logging infrastructure. Check your log aggregation service for detailed activity.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setInterval(refreshMetrics, 30000);

// Load metrics on page load
document.addEventListener('DOMContentLoaded', function() {
    refreshMetrics();
});

function refreshMetrics() {
    // Fetch health status
    fetch('/health')
        .then(response => response.json())
        .then(data => {
            const statusHtml = `
                <div class="row text-center">
                    <div class="col-6">
                        <div class="badge bg-${data.status === 'healthy' ? 'success' : 'danger'} fs-6 p-2 w-100">
                            ${data.status.toUpperCase()}
                        </div>
                        <small class="d-block mt-1">Overall Status</small>
                    </div>
                    <div class="col-6">
                        <div class="badge bg-${data.database === 'connected' ? 'success' : 'danger'} fs-6 p-2 w-100">
                            ${data.database.toUpperCase()}
                        </div>
                        <small class="d-block mt-1">Database</small>
                    </div>
                </div>
                <hr>
                <small class="text-muted">Last updated: ${new Date(data.timestamp).toLocaleString()}</small>
            `;
            document.getElementById('health-status').innerHTML = statusHtml;
        })
        .catch(error => {
            document.getElementById('health-status').innerHTML = 
                '<div class="alert alert-danger">Failed to load health status</div>';
        });

    // Fetch detailed metrics
    fetch('/metrics')
        .then(response => response.json())
        .then(data => {
            // Quick stats
            const quickStatsHtml = `
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">${data.companies.active}</h4>
                        <small>Active Companies</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">${data.payments.today_confirmed}</h4>
                        <small>Today's Payments</small>
                    </div>
                </div>
            `;
            document.getElementById('quick-stats').innerHTML = quickStatsHtml;

            // Companies metrics
            const companiesHtml = `
                <div class="mb-2">
                    <span class="badge bg-primary">${data.companies.total}</span> Total Companies
                </div>
                <div class="mb-2">
                    <span class="badge bg-success">${data.companies.active}</span> Active
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning">${data.companies.suspended || 0}</span> Suspended
                </div>
            `;
            document.getElementById('companies-metrics').innerHTML = companiesHtml;

            // Payments metrics
            const paymentsHtml = `
                <div class="mb-2">
                    <span class="badge bg-primary">${data.payments.total}</span> Total Payments
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning">${data.payments.pending}</span> Pending
                </div>
                <div class="mb-2">
                    <span class="badge bg-success">${data.payments.confirmed}</span> Confirmed
                </div>
            `;
            document.getElementById('payments-metrics').innerHTML = paymentsHtml;

            // Invoices metrics
            const invoicesHtml = `
                <div class="mb-2">
                    <span class="badge bg-primary">${data.invoices.total}</span> Total Invoices
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning">${data.invoices.unpaid}</span> Unpaid
                </div>
                <div class="mb-2">
                    <span class="badge bg-success">${data.invoices.paid}</span> Paid
                </div>
            `;
            document.getElementById('invoices-metrics').innerHTML = invoicesHtml;

            // Revenue metrics
            const revenueHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-success">TZS ${formatNumber(data.revenue.daily)}</h5>
                        <small class="text-muted">Daily Revenue</small>
                    </div>
                    <div class="col-md-6">
                        <h5 class="text-primary">TZS ${formatNumber(data.revenue.monthly)}</h5>
                        <small class="text-muted">Monthly Revenue</small>
                    </div>
                </div>
            `;
            document.getElementById('revenue-metrics').innerHTML = revenueHtml;
        })
        .catch(error => {
            document.getElementById('quick-stats').innerHTML = 
                '<div class="alert alert-danger">Failed to load metrics</div>';
        });
}

function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}
</script>
{% endblock %}
