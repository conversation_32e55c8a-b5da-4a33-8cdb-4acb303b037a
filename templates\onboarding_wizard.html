{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}<PERSON><PERSON><PERSON><PERSON> wa Kuanza - EXLIPA{% else %}Getting Started - EXLIPA{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Progress Bar -->
            <div class="glass-card p-3 mb-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0 gradient-text">
                        {% if session.language == 'sw' %}<PERSON><PERSON><PERSON><PERSON> wa Kuanza{% else %}Getting Started{% endif %}
                    </h6>
                    <span class="badge bg-primary" id="progressText">1/4</span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-gradient" id="progressBar" style="width: 25%"></div>
                </div>
            </div>

            <!-- Wizard Container -->
            <div class="glass-card p-4">
                <!-- Step 1: Welcome -->
                <div class="wizard-step active" id="step1">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-rocket fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold gradient-text mb-3">
                            {% if session.language == 'sw' %}
                                Karibu EXLIPA! 🎉
                            {% else %}
                                Welcome to EXLIPA! 🎉
                            {% endif %}
                        </h2>
                        <p class="lead text-muted">
                            {% if session.language == 'sw' %}
                                Hebu tukuongozee katika mchakato wa kuanza. Itachukua dakika chache tu!
                            {% else %}
                                Let's get you started with a quick setup. This will only take a few minutes!
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded hover-lift">
                                <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                <h6>
                                    {% if session.language == 'sw' %}Malipo ya Simu{% else %}Mobile Payments{% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if session.language == 'sw' %}M-Pesa, Tigo Pesa, Airtel Money{% else %}M-Pesa, Tigo Pesa, Airtel Money{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded hover-lift">
                                <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                <h6>
                                    {% if session.language == 'sw' %}Uchambuzi{% else %}Analytics{% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if session.language == 'sw' %}Ripoti za biashara{% else %}Business insights{% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded hover-lift">
                                <i class="fas fa-code fa-2x text-warning mb-2"></i>
                                <h6>
                                    {% if session.language == 'sw' %}API{% else %}API Access{% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if session.language == 'sw' %}Unganisha mifumo yako{% else %}Integrate your systems{% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Company Information -->
                <div class="wizard-step" id="step2">
                    <div class="text-center mb-4">
                        <i class="fas fa-building fa-3x text-primary mb-3"></i>
                        <h3 class="fw-bold gradient-text">
                            {% if session.language == 'sw' %}Taarifa za Kampuni{% else %}Company Information{% endif %}
                        </h3>
                        <p class="text-muted">
                            {% if session.language == 'sw' %}
                                Tuambie kuhusu kampuni yako ili tuweze kukusaidia vizuri zaidi
                            {% else %}
                                Tell us about your company so we can serve you better
                            {% endif %}
                        </p>
                    </div>

                    <form id="companyForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">
                                    {% if session.language == 'sw' %}Jina la Kampuni{% else %}Company Name{% endif %}
                                </label>
                                <input type="text" class="form-control" id="companyName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">
                                    {% if session.language == 'sw' %}Aina ya Biashara{% else %}Business Type{% endif %}
                                </label>
                                <select class="form-control" id="businessType" required>
                                    <option value="">
                                        {% if session.language == 'sw' %}Chagua aina{% else %}Select type{% endif %}
                                    </option>
                                    <option value="retail">
                                        {% if session.language == 'sw' %}Rejareja{% else %}Retail{% endif %}
                                    </option>
                                    <option value="restaurant">
                                        {% if session.language == 'sw' %}Mgahawa{% else %}Restaurant{% endif %}
                                    </option>
                                    <option value="services">
                                        {% if session.language == 'sw' %}Huduma{% else %}Services{% endif %}
                                    </option>
                                    <option value="ecommerce">
                                        {% if session.language == 'sw' %}Biashara ya mtandaoni{% else %}E-commerce{% endif %}
                                    </option>
                                    <option value="other">
                                        {% if session.language == 'sw' %}Nyingine{% else %}Other{% endif %}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">
                                    {% if session.language == 'sw' %}Simu ya Kampuni{% else %}Company Phone{% endif %}
                                </label>
                                <input type="tel" class="form-control" id="companyPhone" placeholder="+255...">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">
                                    {% if session.language == 'sw' %}Barua pepe ya Kampuni{% else %}Company Email{% endif %}
                                </label>
                                <input type="email" class="form-control" id="companyEmail" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">
                                    {% if session.language == 'sw' %}Anwani ya Kampuni{% else %}Company Address{% endif %}
                                </label>
                                <textarea class="form-control" id="companyAddress" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Step 3: Choose Plan -->
                <div class="wizard-step" id="step3">
                    <div class="text-center mb-4">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h3 class="fw-bold gradient-text">
                            {% if session.language == 'sw' %}Chagua Mpango Wako{% else %}Choose Your Plan{% endif %}
                        </h3>
                        <p class="text-muted">
                            {% if session.language == 'sw' %}
                                Chagua mpango unaofaa mahitaji yako ya biashara
                            {% else %}
                                Select the plan that fits your business needs
                            {% endif %}
                        </p>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card h-100 plan-card" data-plan="free">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-success">
                                        {% if session.language == 'sw' %}Bure{% else %}Free{% endif %}
                                    </h5>
                                    <div class="h2 text-success">TSh 0</div>
                                    <p class="text-muted small">
                                        {% if session.language == 'sw' %}kwa mwezi{% else %}per month{% endif %}
                                    </p>
                                    <ul class="list-unstyled small">
                                        <li>✅ 100 {% if session.language == 'sw' %}miamala ya bure{% else %}free transactions{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}Malipo ya simu{% else %}Mobile payments{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}Ripoti za msingi{% else %}Basic reports{% endif %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100 plan-card border-primary" data-plan="business">
                                <div class="card-body text-center">
                                    <span class="badge bg-primary mb-2">
                                        {% if session.language == 'sw' %}MAARUFU{% else %}POPULAR{% endif %}
                                    </span>
                                    <h5 class="card-title text-primary">
                                        {% if session.language == 'sw' %}Biashara{% else %}Business{% endif %}
                                    </h5>
                                    <div class="h2 text-primary">TSh 50,000</div>
                                    <p class="text-muted small">
                                        {% if session.language == 'sw' %}kwa mwezi{% else %}per month{% endif %}
                                    </p>
                                    <ul class="list-unstyled small">
                                        <li>✅ {% if session.language == 'sw' %}Miamala 10,000{% else %}10,000 transactions{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}API ya kiufundi{% else %}Developer API{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}Uchambuzi wa kina{% else %}Advanced analytics{% endif %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100 plan-card" data-plan="enterprise">
                                <div class="card-body text-center">
                                    <h5 class="card-title text-warning">
                                        {% if session.language == 'sw' %}Makampuni{% else %}Enterprise{% endif %}
                                    </h5>
                                    <div class="h2 text-warning">TSh 150,000</div>
                                    <p class="text-muted small">
                                        {% if session.language == 'sw' %}kwa mwezi{% else %}per month{% endif %}
                                    </p>
                                    <ul class="list-unstyled small">
                                        <li>✅ {% if session.language == 'sw' %}Miamala isiyo na kikomo{% else %}Unlimited transactions{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}Msaada wa kipaumbele{% else %}Priority support{% endif %}</li>
                                        <li>✅ {% if session.language == 'sw' %}Vipengele vyote{% else %}All features{% endif %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Complete -->
                <div class="wizard-step" id="step4">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle fa-4x text-success"></i>
                        </div>
                        <h2 class="fw-bold gradient-text-success mb-3">
                            {% if session.language == 'sw' %}Hongera! 🎉{% else %}Congratulations! 🎉{% endif %}
                        </h2>
                        <p class="lead text-muted mb-4">
                            {% if session.language == 'sw' %}
                                Umekamilisha mchakato wa kuanza. Sasa unaweza kuanza kutumia EXLIPA!
                            {% else %}
                                You've completed the setup process. You're now ready to use EXLIPA!
                            {% endif %}
                        </p>
                        
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <a href="{{ url_for('user_admin_dashboard') }}" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    {% if session.language == 'sw' %}Nenda Dashibodi{% else %}Go to Dashboard{% endif %}
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('advanced_landing', company_id=1) }}" class="btn btn-outline-primary btn-lg w-100">
                                    <i class="fas fa-globe me-2"></i>
                                    {% if session.language == 'sw' %}Unda Ukurasa{% else %}Create Landing Page{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary" id="prevBtn" onclick="changeStep(-1)" disabled>
                        <i class="fas fa-arrow-left me-2"></i>
                        {% if session.language == 'sw' %}Nyuma{% else %}Previous{% endif %}
                    </button>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                        {% if session.language == 'sw' %}Mbele{% else %}Next{% endif %}
                        <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.wizard-step {
    display: none;
}

.wizard-step.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.plan-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.plan-card.selected {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.progress-bar {
    transition: width 0.5s ease;
}
</style>

<script>
let currentStep = 1;
const totalSteps = 4;
let selectedPlan = null;

function updateProgress() {
    const progress = (currentStep / totalSteps) * 100;
    document.getElementById('progressBar').style.width = progress + '%';
    document.getElementById('progressText').textContent = currentStep + '/' + totalSteps;
}

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.wizard-step').forEach(el => {
        el.classList.remove('active');
    });
    
    // Show current step
    document.getElementById('step' + step).classList.add('active');
    
    // Update navigation buttons
    document.getElementById('prevBtn').disabled = step === 1;
    
    const nextBtn = document.getElementById('nextBtn');
    if (step === totalSteps) {
        nextBtn.style.display = 'none';
    } else {
        nextBtn.style.display = 'block';
        nextBtn.textContent = step === totalSteps - 1 ? 
            '{% if session.language == "sw" %}Maliza{% else %}Finish{% endif %}' : 
            '{% if session.language == "sw" %}Mbele{% else %}Next{% endif %}';
    }
    
    updateProgress();
}

function changeStep(direction) {
    if (direction === 1 && !validateCurrentStep()) {
        return;
    }
    
    currentStep += direction;
    if (currentStep < 1) currentStep = 1;
    if (currentStep > totalSteps) currentStep = totalSteps;
    
    showStep(currentStep);
}

function validateCurrentStep() {
    if (currentStep === 2) {
        const companyName = document.getElementById('companyName').value;
        const businessType = document.getElementById('businessType').value;
        const companyEmail = document.getElementById('companyEmail').value;
        
        if (!companyName || !businessType || !companyEmail) {
            alert('{% if session.language == "sw" %}Tafadhali jaza sehemu zote muhimu{% else %}Please fill in all required fields{% endif %}');
            return false;
        }
    }
    
    if (currentStep === 3 && !selectedPlan) {
        alert('{% if session.language == "sw" %}Tafadhali chagua mpango{% else %}Please select a plan{% endif %}');
        return false;
    }
    
    return true;
}

// Plan selection
document.querySelectorAll('.plan-card').forEach(card => {
    card.addEventListener('click', function() {
        document.querySelectorAll('.plan-card').forEach(c => c.classList.remove('selected'));
        this.classList.add('selected');
        selectedPlan = this.dataset.plan;
    });
});

// Initialize
showStep(1);
</script>
{% endblock %}
