{% extends "base.html" %}

{% block title %}POS Products - {{ company.company_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-boxes me-2 text-primary"></i>
                {% if session.language == 'sw' %}
                    Usimamizi wa Bidhaa
                {% else %}
                    Product Management
                {% endif %}
            </h2>
            <p class="text-muted mb-0">{{ company.company_name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ url_for('add_pos_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                {% if session.language == 'sw' %}
                    Ongeza Bidhaa
                {% else %}
                    Add Product
                {% endif %}
            </a>
            <a href="/pos" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                {% if session.language == 'sw' %}
                    <PERSON><PERSON> kwenye POS
                {% else %}
                    Back to POS
                {% endif %}
            </a>
        </div>
    </div>

    {% if products %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if session.language == 'sw' %}
                        Bidhaa ({{ products|length }})
                    {% else %}
                        Products ({{ products|length }})
                    {% endif %}
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>{%- if session.language == "sw" -%}Bidhaa{%- else -%}Product{%- endif -%}</th>
                                <th>{%- if session.language == "sw" -%}Jamii{%- else -%}Category{%- endif -%}</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Bei{%- else -%}Price{%- endif -%}') }}</th>
                                <th>Stock</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Hali{%- else -%}Status{%- endif -%}') }}</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Vitendo{%- else -%}Actions{%- endif -%}') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <div>
                                        <h6 class="mb-1">{{ product.name }}</h6>
                                        {% if product.description %}
                                            <small class="text-muted">{{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                        {% if product.sku %}
                                            <br><small class="text-muted">SKU: {{ product.sku }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if product.category %}
                                        <span class="badge bg-secondary">{{ product.category }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>TZS {{ "{:,.0f}".format(product.price) }}</strong>
                                </td>
                                <td>
                                    {% if product.track_inventory %}
                                        <span class="{% if product.stock_quantity <= product.low_stock_alert %}text-danger{% elif product.stock_quantity <= product.low_stock_alert * 2 %}text-warning{% else %}text-success{% endif %}">
                                            {{ product.stock_quantity }}
                                        </span>
                                        {% if product.stock_quantity <= product.low_stock_alert %}
                                            <i class="fas fa-exclamation-triangle text-danger ms-1" title="Low stock"></i>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Not tracked</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">{{ t('{%- if session.language == "sw" -%}Hai{%- else -%}Active{%- endif -%}') }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ t('{%- if session.language == "sw" -%}Haifanyi Kazi{%- else -%}Inactive{%- endif -%}') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('edit_pos_product', product_id=product.id) }}" 
                                           class="btn btn-outline-primary" title="{{ t('{%- if session.language == "sw" -%}Hariri{%- else -%}Edit{%- endif -%}') }}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteProduct({{ product.id }}, '{{ product.name }}')" title="{{ t('{%- if session.language == "sw" -%}Futa{%- else -%}Delete{%- endif -%}') }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                        <h5>{{ products|length }}</h5>
                        <p class="text-muted mb-0">Total Products</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5>{{ products|selectattr('is_active')|list|length }}</h5>
                        <p class="text-muted mb-0">Active Products</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h5>
                            {% set low_stock_count = 0 %}
                            {% for product in products %}
                                {% if product.track_inventory and product.stock_quantity <= product.low_stock_alert %}
                                    {% set low_stock_count = low_stock_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ low_stock_count }}
                        </h5>
                        <p class="text-muted mb-0">Low Stock</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-dollar-sign fa-2x text-info mb-2"></i>
                        <h5>TZS {{ "{:,.0f}".format(products|map(attribute='price')|sum) }}</h5>
                        <p class="text-muted mb-0">Total Value</p>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                <h4 class="text-muted mb-3">No Products Yet</h4>
                <p class="text-muted mb-4">Start by adding your first product to begin selling</p>
                <a href="{{ url_for('add_pos_product') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Add Your First Product
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- {%- if session.language == "sw" -%}Futa{%- else -%}Delete{%- endif -%} Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-danger me-2"></i>Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the product <strong id="delete-product-name"></strong>?</p>
                <p class="text-muted mb-0">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('{%- if session.language == "sw" -%}Ghairi{%- else -%}Cancel{%- endif -%}') }}</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">Delete Product</button>
            </div>
        </div>
    </div>
</div>

<script>
let productToDelete = null;

function deleteProduct(productId, productName) {
    productToDelete = productId;
    document.getElementById('delete-product-name').textContent = productName;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirm-delete').addEventListener('click', function() {
    if (productToDelete) {
        // In a real implementation, you'd make an AJAX call to delete the product
        // For now, we'll just show an alert
        alert('Delete functionality would be implemented here');
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
        productToDelete = null;
    }
});
</script>
{% endblock %}
