# 👨‍💼 EXLIPA Payment Gateway - Admin User Guide

## 📋 **Overview**

This comprehensive guide covers all administrative functions in the EXLIPA Payment Gateway system, including SMS verification workflows, user management, and business operations.

---

## 🔐 **Admin Login & Access**

### **Master Admin Access**
- **URL**: `https://yourdomain.com/admin-login`
- **Default Credentials**: admin / admin123 (⚠️ Change immediately)
- **Capabilities**: Full system administration, payment verification, user management

### **User Admin Access**
- **URL**: `https://yourdomain.com/login`
- **Capabilities**: Company management, POS system, team management

### **Company User Access**
- **URL**: `https://yourdomain.com/company-login`
- **Capabilities**: Basic operations, POS sales, customer management

---

## 💳 **SMS Payment Verification Workflow**

### **Step 1: Payment Submission Review**
1. **Access Payment Dashboard**:
   - Navigate to `Admin Dashboard` → `Payment Confirmations`
   - View pending payments requiring verification

2. **Payment Details Review**:
   - **Customer Information**: Name, email, mobile money registered name
   - **Payment Details**: Amount, mobile operator, transaction ID
   - **Service Description**: What the customer is paying for

### **Step 2: SMS Verification Process**
1. **Check Your Mobile Money SMS**:
   - Look for SMS from mobile money provider (MPESA, TIGOPESA, etc.)
   - Note the sender name, amount, and transaction reference

2. **Verify Payment Details**:
   - **Amount Match**: SMS amount must match customer's claimed amount
   - **Name Match**: SMS sender name must match customer's mobile money registered name
   - **Transaction ID**: Verify transaction reference matches

3. **Complete Verification Form**:
   ```
   SMS Sender: [Select: MPESA/TIGOPESA/AIRTELMONEY/CRDB]
   SMS Amount: [Enter exact amount from SMS]
   SMS Sender Name: [Enter exact name from SMS]
   SMS Transaction Ref: [Enter transaction reference]
   Verification Notes: [Add any relevant notes]
   ```

### **Step 3: Approval or Rejection**
**For Approval**:
- Click `Confirm Payment` after verifying all details match
- Registration link automatically sent to customer's email
- Payment status changes to "Confirmed"

**For Rejection**:
- Click `Reject Payment` if details don't match
- Add clear rejection reason (e.g., "Amount mismatch", "Name doesn't match")
- Customer can resubmit with correct information

### **Common Verification Scenarios**

#### **✅ Successful Verification Example**:
```
Customer Submission:
- Name: John Doe
- Mobile Money Name: John Doe
- Amount: TZS 50,000
- Operator: M-Pesa
- Transaction ID: ND12345678

SMS Received:
- From: MPESA
- Message: "You have received TZS 50,000 from John Doe. Ref: ND12345678"

Action: APPROVE ✅
```

#### **❌ Failed Verification Example**:
```
Customer Submission:
- Name: Mary Smith
- Mobile Money Name: Mary Smith
- Amount: TZS 25,000
- Operator: Tigo Pesa
- Transaction ID: TP87654321

SMS Received:
- From: TIGOPESA
- Message: "You have received TZS 20,000 from Mary Smith. Ref: TP87654321"

Action: REJECT ❌ (Amount mismatch)
Reason: "SMS shows TZS 20,000 but customer claimed TZS 25,000"
```

---

## 👥 **User Management**

### **Creating New Users**
1. **Navigate to User Management**:
   - `Admin Dashboard` → `User Management` → `Add New User`

2. **User Information**:
   ```
   Username: [Unique username]
   Email: [Valid email address]
   Full Name: [User's full name]
   Role: [master_admin/user_admin/company_user]
   Password: [Secure password]
   ```

3. **Role Permissions**:
   - **Master Admin**: Full system access, payment verification
   - **User Admin**: Company management, POS access
   - **Company User**: Basic operations, POS sales

### **Managing Existing Users**
- **View Users**: See all users with their roles and status
- **Edit Users**: Update user information and permissions
- **Deactivate Users**: Disable access without deleting data
- **Reset Passwords**: Generate new passwords for users
- **Unlock Accounts**: Manually unlock accounts after failed login attempts

---

## 🏢 **Company Management**

### **Company Profile Setup**
1. **Basic Information**:
   ```
   Company Name: [Official business name]
   Address: [Physical business address]
   Phone: [Business phone number]
   Email: [Business email address]
   TIN: [Tax identification number]
   Website: [Company website URL]
   ```

2. **Payment Method Configuration**:
   ```
   M-Pesa Till: [Your Vodacom till number]
   Tigo Pesa Paybill: [Your Tigo merchant code]
   Airtel Money: [Your Airtel merchant number]
   CRDB Lipa: [Your CRDB merchant details]
   ```

3. **Branding Settings**:
   ```
   Logo: [Upload company logo]
   Primary Color: [Brand color hex code]
   Secondary Color: [Accent color hex code]
   Landing Page Title: [Custom page title]
   Landing Page Description: [Welcome message]
   ```

### **Landing Page Management**
1. **Customize Landing Page**:
   - Upload company logo and set brand colors
   - Write customer-friendly payment instructions
   - Configure payment methods and till numbers

2. **Preview and Test**:
   - Use preview function to see customer view
   - Test payment form with dummy data
   - Verify mobile money instructions are clear

---

## 🛒 **POS System Management**

### **Product Management**
1. **Add New Products**:
   ```
   Product Name: [Descriptive product name]
   Price: [Selling price in TZS]
   Stock Quantity: [Current inventory count]
   Track Inventory: [Yes/No]
   Low Stock Alert: [Minimum quantity threshold]
   Category: [Product category]
   ```

2. **Inventory Management**:
   - **Stock Adjustments**: Manually adjust inventory levels
   - **Low Stock Alerts**: Monitor products below threshold
   - **Inventory Reports**: Track stock movements and sales

### **Sales Processing**
1. **Process Sale**:
   - Select products and quantities
   - Choose payment method (Cash/Mobile Money)
   - Enter customer information if needed
   - Generate receipt

2. **Cash Drawer Management**:
   - **Open Session**: Start with opening balance count
   - **Record Transactions**: All cash sales automatically recorded
   - **Close Session**: End with closing balance and reconciliation

---

## 📊 **Reporting & Analytics**

### **Payment Reports**
1. **Payment Summary**:
   - Total payments processed
   - Confirmation success rate
   - Average verification time
   - Revenue by payment method

2. **Daily Operations**:
   - Pending verifications
   - Completed registrations
   - Failed payment attempts
   - Customer support tickets

### **Business Analytics**
1. **Sales Performance**:
   - Daily/weekly/monthly sales trends
   - Top-selling products
   - Customer purchase patterns
   - Revenue growth analysis

2. **System Health**:
   - User activity logs
   - System performance metrics
   - Security incident reports
   - Backup status and logs

---

## 🆘 **Troubleshooting Common Issues**

### **Payment Verification Issues**

#### **Problem**: SMS not received from mobile money provider
**Solutions**:
1. Check mobile money account balance and status
2. Verify till/paybill numbers are correct and active
3. Contact mobile money provider for account issues
4. Use alternative verification methods if available

#### **Problem**: Customer name doesn't match SMS
**Solutions**:
1. Ask customer to verify their mobile money registered name
2. Check for spelling variations or abbreviations
3. Contact customer to confirm correct mobile money account
4. Reject payment with clear explanation for resubmission

#### **Problem**: Amount discrepancy between customer and SMS
**Solutions**:
1. Double-check SMS amount carefully
2. Consider mobile money transaction fees
3. Ask customer to provide SMS screenshot if needed
4. Reject payment with specific amount details

### **System Access Issues**

#### **Problem**: User account locked
**Solutions**:
1. Wait 15 minutes for automatic unlock
2. Use admin panel to manually unlock account
3. Reset password if user forgot credentials
4. Check for typing errors in username/password

#### **Problem**: Registration link not working
**Solutions**:
1. Check if link has expired (24-hour validity)
2. Verify email delivery to customer
3. Use admin resend functionality
4. Generate new registration link if needed

### **POS System Issues**

#### **Problem**: Inventory showing incorrect levels
**Solutions**:
1. Review recent sales for stock deductions
2. Check for manual inventory adjustments
3. Verify product tracking settings
4. Perform physical count and adjust if needed

#### **Problem**: Cash drawer discrepancies
**Solutions**:
1. Review all cash transactions for the session
2. Check for manual cash adjustments
3. Verify opening balance was correct
4. Document variance and investigate source

---

## 📞 **Customer Support Procedures**

### **Handling Customer Inquiries**
1. **Payment Status Questions**:
   - Direct customers to payment status checker
   - Provide reference number for tracking
   - Explain verification timeline (2-4 hours)

2. **Registration Issues**:
   - Verify payment confirmation status
   - Check email delivery and spam folders
   - Resend registration link if needed
   - Provide manual registration assistance

3. **Technical Problems**:
   - Document issue details and screenshots
   - Escalate to technical team if needed
   - Provide temporary workarounds
   - Follow up on resolution

### **Escalation Process**
1. **Level 1**: Customer support (2-hour response)
2. **Level 2**: Technical team (4-hour response)
3. **Level 3**: Development team (8-hour response)
4. **Emergency**: Immediate response for payment failures

---

## 🔒 **Security Best Practices**

### **Account Security**
- Change default passwords immediately
- Use strong passwords with mixed characters
- Enable two-factor authentication if available
- Log out when leaving workstation unattended

### **Payment Verification Security**
- Always verify SMS details carefully
- Never approve payments without proper verification
- Document all verification decisions
- Report suspicious payment attempts

### **Data Protection**
- Keep customer information confidential
- Use secure communication channels
- Regular backup verification
- Report security incidents immediately

---

## 📚 **Quick Reference**

### **Important URLs**
- **Master Admin**: `/admin-login`
- **Payment Dashboard**: `/admin/payments`
- **User Management**: `/admin/users`
- **Company Settings**: `/company-settings`
- **POS System**: `/pos`

### **Emergency Contacts**
- **Technical Support**: <EMAIL>
- **Customer Support**: <EMAIL>
- **Emergency Line**: +*********** 789

### **Key Shortcuts**
- **Pending Payments**: Dashboard → Payment Confirmations → Filter: Pending
- **User Unlock**: User Management → Select User → Unlock Account
- **Resend Link**: Payment Details → Resend Registration Link
- **Cash Drawer**: POS → Cash Drawer → Open/Close Session

---

**© 2024 Exlipa Payment Solutions Ltd. All rights reserved.**
