<!DOCTYPE html>
<html lang="{% if session.language == 'sw' %}sw{% else %}en{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if session.language == 'sw' %}Hakuna Mtandao - EXLIPA{% else %}Offline - EXLIPA{% endif %}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 400px;
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .offline-message {
            font-size: 1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .offline-features {
            margin-top: 2rem;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .feature-icon {
            margin-right: 0.5rem;
            width: 20px;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-offline {
            background: rgba(220, 53, 69, 0.9);
            color: white;
        }

        .status-online {
            background: rgba(40, 167, 69, 0.9);
            color: white;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .retry-btn {
                background: rgba(0, 0, 0, 0.3);
                border-color: rgba(255, 255, 255, 0.2);
            }
        }
    </style>
</head>
<body>
    <div class="connection-status status-offline" id="connectionStatus">
        📡 {% if session.language == 'sw' %}Hakuna Mtandao{% else %}Offline{% endif %}
    </div>

    <div class="offline-container">
        <div class="offline-icon pulse">📱</div>
        
        <h1 class="offline-title">
            {% if session.language == 'sw' %}
                Hakuna Muunganisho wa Mtandao
            {% else %}
                No Internet Connection
            {% endif %}
        </h1>
        
        <p class="offline-message">
            {% if session.language == 'sw' %}
                Samahani, hakuna muunganisho wa mtandao kwa sasa. Jaribu tena baada ya kupata mtandao.
            {% else %}
                Sorry, you're currently offline. Please check your internet connection and try again.
            {% endif %}
        </p>

        <button class="retry-btn" onclick="retryConnection()">
            {% if session.language == 'sw' %}🔄 Jaribu Tena{% else %}🔄 Try Again{% endif %}
        </button>

        <div class="offline-features">
            <h3 style="margin-bottom: 1rem; font-size: 1.1rem;">
                {% if session.language == 'sw' %}
                    Unapoungana tena:
                {% else %}
                    When you're back online:
                {% endif %}
            </h3>
            
            <div class="feature-item">
                <span class="feature-icon">💾</span>
                {% if session.language == 'sw' %}
                    Data yako itahifadhiwa kiotomatiki
                {% else %}
                    Your data will be automatically synced
                {% endif %}
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                {% if session.language == 'sw' %}
                    Ripoti za hivi karibuni zitaonyeshwa
                {% else %}
                    Recent reports will be updated
                {% endif %}
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">🔔</span>
                {% if session.language == 'sw' %}
                    Arifa mpya zitapokelewa
                {% else %}
                    New notifications will be received
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🌐 {% if session.language == "sw" %}Mtandao Upo{% else %}Online{% endif %}';
                statusElement.className = 'connection-status status-online';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = '/mobile-dashboard';
                }, 1000);
            } else {
                statusElement.textContent = '📡 {% if session.language == "sw" %}Hakuna Mtandao{% else %}Offline{% endif %}';
                statusElement.className = 'connection-status status-offline';
            }
        }

        // Retry connection
        function retryConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                window.location.reload();
            } else {
                // Show feedback
                const btn = document.querySelector('.retry-btn');
                const originalText = btn.textContent;
                btn.textContent = '{% if session.language == "sw" %}Bado Hakuna Mtandao...{% else %}Still Offline...{% endif %}';
                btn.style.background = 'rgba(220, 53, 69, 0.3)';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'rgba(255, 255, 255, 0.2)';
                }, 2000);
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Service Worker messaging
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'CACHE_UPDATED') {
                    // Show update available notification
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        left: 20px;
                        right: 20px;
                        background: rgba(40, 167, 69, 0.9);
                        color: white;
                        padding: 1rem;
                        border-radius: 10px;
                        text-align: center;
                        z-index: 1000;
                    `;
                    notification.textContent = '{% if session.language == "sw" %}Sasisho la programu linapatikana{% else %}App update available{% endif %}';
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 5000);
                }
            });
        }

        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
