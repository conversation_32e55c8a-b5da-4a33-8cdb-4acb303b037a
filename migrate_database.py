#!/usr/bin/env python3
"""
Database migration script to update existing database to new schema
"""

import os
import sqlite3
from datetime import datetime
import shutil

def backup_database():
    """Create a backup of the existing database"""
    db_path = 'payment_system.db'
    if os.path.exists(db_path):
        backup_path = f'payment_system_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2(db_path, backup_path)
        print(f"[OK] Database backed up to: {backup_path}")
        return backup_path
    return None

def check_existing_data():
    """Check what data exists in the current database"""
    db_path = 'payment_system.db'
    if not os.path.exists(db_path):
        print("[INFO] No existing database found. Will create fresh database.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check existing tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"[DATA] Existing tables: {[table[0] for table in tables]}")
        
        # Check data counts
        for table in ['user', 'invoice', 'payment_confirmation']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   - {table}: {count} records")
            except sqlite3.OperationalError:
                print(f"   - {table}: table doesn't exist")
        
        conn.close()
        return True
    except Exception as e:
        print(f"[ERROR] Error checking database: {e}")
        return False

def migrate_database():
    """Migrate database to new schema"""
    db_path = 'payment_system.db'
    
    if not os.path.exists(db_path):
        print("[INFO] No existing database. New database will be created automatically.")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("[MIGRATE] Starting database migration...")
        
        # Check if new columns already exist
        cursor.execute("PRAGMA table_info(payment_confirmation)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add missing columns to payment_confirmation
        if 'client_company_id' not in columns:
            print("   Adding client_company_id column...")
            cursor.execute("ALTER TABLE payment_confirmation ADD COLUMN client_company_id INTEGER")
        
        if 'transaction_fee' not in columns:
            print("   Adding transaction_fee column...")
            cursor.execute("ALTER TABLE payment_confirmation ADD COLUMN transaction_fee REAL DEFAULT 0.0")
        
        if 'fee_calculated' not in columns:
            print("   Adding fee_calculated column...")
            cursor.execute("ALTER TABLE payment_confirmation ADD COLUMN fee_calculated BOOLEAN DEFAULT 0")
        
        # Check invoice table
        cursor.execute("PRAGMA table_info(invoice)")
        invoice_columns = [column[1] for column in cursor.fetchall()]
        
        # Add missing columns to invoice
        cart_columns = ['cart_items', 'subtotal', 'tax_amount', 'discount_amount', 'shipping_cost']
        for col in cart_columns:
            if col not in invoice_columns:
                print(f"   Adding {col} column to invoice...")
                if col == 'cart_items':
                    cursor.execute(f"ALTER TABLE invoice ADD COLUMN {col} TEXT")
                else:
                    cursor.execute(f"ALTER TABLE invoice ADD COLUMN {col} REAL DEFAULT 0.0")
        
        # Check user table
        cursor.execute("PRAGMA table_info(user)")
        user_columns = [column[1] for column in cursor.fetchall()]
        
        # Add missing columns to user
        user_new_columns = {
            'email': 'TEXT',
            'full_name': 'TEXT',
            'role': 'TEXT DEFAULT "admin"',
            'is_active': 'BOOLEAN DEFAULT 1',
            'last_login': 'DATETIME',
            'created_by': 'INTEGER'
        }
        
        for col, col_type in user_new_columns.items():
            if col not in user_columns:
                print(f"   Adding {col} column to user...")
                cursor.execute(f"ALTER TABLE user ADD COLUMN {col} {col_type}")
        
        conn.commit()
        conn.close()
        
        print("[OK] Database migration completed successfully!")
        return True
        
    except Exception as e:
        print("[ERROR] Migration failed: {e}")
        return False

def create_new_database():
    """Create fresh database with new schema"""
    try:
        # Import app and create tables
        from app import app, db
        
        with app.app_context():
            print("[CREATE] Creating new database with updated schema...")
            db.create_all()
            print("[OK] New database created successfully!")
            return True
    except Exception as e:
        print(f"[ERROR] Failed to create new database: {e}")
        return False

def main():
    print("=== EXLIPA DATABASE MIGRATION ===")
    print()
    
    # Check current state
    has_existing_db = check_existing_data()
    
    if has_existing_db:
        print("\n[OPTIONS] Migration Options:")
        print("1. Migrate existing database (recommended - preserves data)")
        print("2. Create fresh database (WARNING: will lose existing data)")
        print("3. Cancel migration")
        
        choice = input("\nEnter your choice (1/2/3): ").strip()
        
        if choice == '1':
            # Backup first
            backup_path = backup_database()
            
            # Try migration
            if migrate_database():
                print("\n[SUCCESS] Migration completed! Your data has been preserved.")
                print("[INFO] Test the application and if everything works, you can delete the backup file.")
            else:
                print("\n[ERROR] Migration failed!")
                if backup_path:
                    print(f"[BACKUP] Your original database is backed up at: {backup_path}")
                    
        elif choice == '2':
            # Backup first
            backup_path = backup_database()
            
            # Remove old database
            if os.path.exists('payment_system.db'):
                os.remove('payment_system.db')
                print("[REMOVE] Old database removed")
            
            # Create new database
            if create_new_database():
                print("\n[SUCCESS] Fresh database created!")
                if backup_path:
                    print(f"[BACKUP] Your old database is backed up at: {backup_path}")
                    print("[WARNING] You'll need to recreate your admin user and any test data.")
            
        elif choice == '3':
            print("[CANCEL] Migration cancelled")
            return
        else:
            print("[ERROR] Invalid choice")
            return
    else:
        # No existing database, create new one
        create_new_database()
    
    print("\n[READY] Database is now ready for the new billing system!")
    print("[RUN] You can now run: python app.py")

if __name__ == '__main__':
    main()
