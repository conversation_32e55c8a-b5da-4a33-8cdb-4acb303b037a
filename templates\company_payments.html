{% extends "base.html" %}
{%- block title -%}
{%- if session.language == 'sw' -%}
    My Malipo - Exlipa
{%- else -%}
    My Payments - Exlipa
{%- endif -%}
{%- endblock -%}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-credit-card me-2 text-success"></i>
        {% if session.language == 'sw' %}
            Malipo Yangu
        {% else %}
            My Payments
        {% endif %}
    </h2>
    {% if payments %}
    <div class="table-responsive">
        <table class="table table-bordered align-middle">
            <thead class="table-light">
                <tr>
                    <th>{{ t('Date') }}</th>
                    <th>{{ t('Amount') }}</th>
                    <th>
                        {% if session.language == 'sw' %}
                            Mtoa Huduma
                        {% else %}
                            Operator
                        {% endif %}
                    </th>
                    <th>{{ t('Transaction ID') }}</th>
                    <th>{{ t('Status') }}</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in payments %}
                <tr>
                    <td>{{ payment.submitted_at.strftime('%Y-%m-%d') if payment.submitted_at else 'N/A' }}</td>
                    <td>TZS {{ "{:,}".format(payment.amount|int) }}</td>
                    <td>{{ payment.mobile_operator or '-' }}</td>
                    <td>{{ payment.transaction_id or '-' }}</td>
                    <td>
                        <span class="badge {% if payment.status == 'Confirmed' %}bg-success{% elif payment.status == 'Pending' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">{{ payment.status }}</span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        {% if session.language == 'sw' %}
            Hakuna malipo yaliyopatikana.
        {% else %}
            No payments found.
        {% endif %}
    </div>
    {% endif %}
    <div class="mt-3">
        <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">
            &larr;
            {% if session.language == 'sw' %}
                Rudi kwenye Dashibodi
            {% else %}
                Back to Dashboard
            {% endif %}
        </a>
    </div>
</div>
{% endblock %}
