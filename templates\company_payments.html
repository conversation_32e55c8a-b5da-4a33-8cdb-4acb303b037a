{% extends "base.html" %}
{% block title %}My Payments - Exlipa{% endblock %}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-credit-card me-2 text-success"></i>My Payments</h2>
    {% if payments %}
    <div class="table-responsive">
        <table class="table table-bordered align-middle">
            <thead class="table-light">
                <tr>
                    <th>{{ t('Date') }}</th>
                    <th>{{ t('Amount') }}</th>
                    <th>Operator</th>
                    <th>{{ t('Transaction ID') }}</th>
                    <th>{{ t('Status') }}</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in payments %}
                <tr>
                    <td>{{ payment.submitted_at.strftime('%Y-%m-%d') if payment.submitted_at else 'N/A' }}</td>
                    <td>TZS {{ "{:,}".format(payment.amount|int) }}</td>
                    <td>{{ payment.mobile_operator or '-' }}</td>
                    <td>{{ payment.transaction_id or '-' }}</td>
                    <td>
                        <span class="badge {% if payment.status == 'Confirmed' %}bg-success{% elif payment.status == 'Pending' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">{{ payment.status }}</span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">No payments found.</div>
    {% endif %}
    <div class="mt-3">
        <a href="{{ url_for('company_dashboard') }}" class="btn btn-link">&larr; Back to Dashboard</a>
    </div>
</div>
{% endblock %}
