// EXLIPA Smart Help System
class SmartHelp {
    constructor() {
        this.isActive = false;
        this.currentStep = 0;
        this.tours = {};
        this.language = document.documentElement.lang || 'en';
        this.init();
    }

    init() {
        this.createHelpButton();
        this.setupTours();
        this.bindEvents();
    }

    createHelpButton() {
        const helpButton = document.createElement('div');
        helpButton.id = 'smart-help-button';
        helpButton.innerHTML = `
            <button class="btn btn-primary rounded-circle position-fixed" 
                    style="bottom: 20px; right: 20px; width: 60px; height: 60px; z-index: 1050; box-shadow: 0 4px 20px rgba(0,123,255,0.4);">
                <i class="fas fa-question fa-lg"></i>
            </button>
        `;
        document.body.appendChild(helpButton);

        helpButton.addEventListener('click', () => this.showHelpMenu());
    }

    showHelpMenu() {
        const menu = document.createElement('div');
        menu.id = 'help-menu';
        menu.className = 'position-fixed';
        menu.style.cssText = `
            bottom: 90px; 
            right: 20px; 
            z-index: 1051; 
            background: rgba(255,255,255,0.95); 
            backdrop-filter: blur(20px);
            border-radius: 15px; 
            padding: 1rem; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            min-width: 250px;
        `;

        const menuItems = this.language === 'sw' ? [
            { icon: 'fas fa-route', text: 'Mwongozo wa Haraka', action: 'quickTour' },
            { icon: 'fas fa-book', text: 'Mafunzo ya Msingi', action: 'basicTutorial' },
            { icon: 'fas fa-video', text: 'Video za Mafunzo', action: 'videoTutorials' },
            { icon: 'fas fa-headset', text: 'Wasiliana Nasi', action: 'contactSupport' }
        ] : [
            { icon: 'fas fa-route', text: 'Quick Tour', action: 'quickTour' },
            { icon: 'fas fa-book', text: 'Basic Tutorial', action: 'basicTutorial' },
            { icon: 'fas fa-video', text: 'Video Tutorials', action: 'videoTutorials' },
            { icon: 'fas fa-headset', text: 'Contact Support', action: 'contactSupport' }
        ];

        menu.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 fw-bold">${this.language === 'sw' ? 'Msaada' : 'Help'}</h6>
                <button class="btn btn-sm btn-outline-secondary rounded-circle" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            ${menuItems.map(item => `
                <div class="d-flex align-items-center p-2 rounded hover-bg-light cursor-pointer" onclick="smartHelp.${item.action}()">
                    <i class="${item.icon} me-3 text-primary"></i>
                    <span>${item.text}</span>
                </div>
            `).join('')}
        `;

        // Remove existing menu
        const existingMenu = document.getElementById('help-menu');
        if (existingMenu) existingMenu.remove();

        document.body.appendChild(menu);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (document.getElementById('help-menu')) {
                menu.remove();
            }
        }, 10000);
    }

    setupTours() {
        // Dashboard tour
        this.tours.dashboard = {
            steps: [
                {
                    element: '.navbar-brand',
                    title: this.language === 'sw' ? 'Karibu EXLIPA' : 'Welcome to EXLIPA',
                    content: this.language === 'sw' ? 
                        'Hii ni jukwaa lako la malipo. Hebu tukuongoze!' : 
                        'This is your payment platform. Let us show you around!'
                },
                {
                    element: '[href*="admin_payments"]',
                    title: this.language === 'sw' ? 'Malipo' : 'Payments',
                    content: this.language === 'sw' ? 
                        'Hapa unaweza kuona na kusimamia malipo yako yote' : 
                        'Here you can view and manage all your payments'
                },
                {
                    element: '[href*="analytics"]',
                    title: this.language === 'sw' ? 'Uchambuzi' : 'Analytics',
                    content: this.language === 'sw' ? 
                        'Ona ripoti za biashara yako na takwimu muhimu' : 
                        'View your business reports and key metrics'
                },
                {
                    element: '[href*="advanced_landing"]',
                    title: this.language === 'sw' ? 'Ukurasa wa Malipo' : 'Landing Page',
                    content: this.language === 'sw' ? 
                        'Unda ukurasa wa malipo kwa wateja wako' : 
                        'Create payment pages for your customers'
                }
            ]
        };

        // Payment tutorial
        this.tours.payments = {
            steps: [
                {
                    element: '.btn-primary',
                    title: this.language === 'sw' ? 'Ongeza Malipo' : 'Add Payment',
                    content: this.language === 'sw' ? 
                        'Bofya hapa kuongeza malipo mapya' : 
                        'Click here to add new payments'
                }
            ]
        };
    }

    quickTour() {
        this.startTour('dashboard');
        document.getElementById('help-menu')?.remove();
    }

    startTour(tourName) {
        const tour = this.tours[tourName];
        if (!tour) return;

        this.isActive = true;
        this.currentStep = 0;
        this.showStep(tour.steps[0]);
    }

    showStep(step) {
        // Remove existing overlay
        this.removeOverlay();

        const element = document.querySelector(step.element);
        if (!element) {
            this.nextStep();
            return;
        }

        // Create overlay
        const overlay = document.createElement('div');
        overlay.id = 'help-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1060;
            pointer-events: none;
        `;

        // Highlight element
        const rect = element.getBoundingClientRect();
        const highlight = document.createElement('div');
        highlight.style.cssText = `
            position: fixed;
            top: ${rect.top - 5}px;
            left: ${rect.left - 5}px;
            width: ${rect.width + 10}px;
            height: ${rect.height + 10}px;
            border: 3px solid #007bff;
            border-radius: 8px;
            z-index: 1061;
            pointer-events: none;
            box-shadow: 0 0 0 9999px rgba(0,0,0,0.5);
        `;

        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed;
            top: ${rect.bottom + 15}px;
            left: ${rect.left}px;
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            z-index: 1062;
            max-width: 300px;
            pointer-events: auto;
        `;

        tooltip.innerHTML = `
            <h6 class="fw-bold mb-2">${step.title}</h6>
            <p class="mb-3">${step.content}</p>
            <div class="d-flex justify-content-between">
                <button class="btn btn-outline-secondary btn-sm" onclick="smartHelp.endTour()">
                    ${this.language === 'sw' ? 'Maliza' : 'End Tour'}
                </button>
                <button class="btn btn-primary btn-sm" onclick="smartHelp.nextStep()">
                    ${this.language === 'sw' ? 'Mbele' : 'Next'}
                </button>
            </div>
        `;

        document.body.appendChild(overlay);
        document.body.appendChild(highlight);
        document.body.appendChild(tooltip);

        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    nextStep() {
        const currentTour = this.getCurrentTour();
        if (!currentTour) return;

        this.currentStep++;
        if (this.currentStep >= currentTour.steps.length) {
            this.endTour();
            return;
        }

        this.showStep(currentTour.steps[this.currentStep]);
    }

    endTour() {
        this.isActive = false;
        this.currentStep = 0;
        this.removeOverlay();
    }

    removeOverlay() {
        document.getElementById('help-overlay')?.remove();
        document.querySelectorAll('[style*="box-shadow: 0 0 0 9999px"]').forEach(el => el.remove());
        document.querySelectorAll('[style*="z-index: 1062"]').forEach(el => el.remove());
    }

    getCurrentTour() {
        // Determine current tour based on page
        const path = window.location.pathname;
        if (path.includes('dashboard')) return this.tours.dashboard;
        if (path.includes('payments')) return this.tours.payments;
        return this.tours.dashboard;
    }

    basicTutorial() {
        document.getElementById('help-menu')?.remove();
        this.showTutorialModal();
    }

    showTutorialModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade show';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            ${this.language === 'sw' ? 'Mafunzo ya Msingi' : 'Basic Tutorial'}
                        </h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-credit-card fa-2x text-primary mb-3"></i>
                                        <h6>${this.language === 'sw' ? 'Pokea Malipo' : 'Accept Payments'}</h6>
                                        <p class="small text-muted">
                                            ${this.language === 'sw' ? 
                                                'Jifunze jinsi ya kupokea malipo kutoka kwa wateja' : 
                                                'Learn how to accept payments from customers'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar fa-2x text-success mb-3"></i>
                                        <h6>${this.language === 'sw' ? 'Ona Ripoti' : 'View Reports'}</h6>
                                        <p class="small text-muted">
                                            ${this.language === 'sw' ? 
                                                'Fuatilia biashara yako kwa ripoti za kina' : 
                                                'Track your business with detailed reports'}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    videoTutorials() {
        document.getElementById('help-menu')?.remove();
        window.open('https://youtube.com/exlipa-tutorials', '_blank');
    }

    contactSupport() {
        document.getElementById('help-menu')?.remove();
        const supportModal = document.createElement('div');
        supportModal.className = 'modal fade show';
        supportModal.style.display = 'block';
        supportModal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            ${this.language === 'sw' ? 'Wasiliana Nasi' : 'Contact Support'}
                        </h5>
                        <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                        <h6>${this.language === 'sw' ? 'Tupo Hapa Kukusaidia!' : 'We\'re Here to Help!'}</h6>
                        <p class="mb-4">
                            ${this.language === 'sw' ? 
                                'Wasiliana nasi kwa njia yoyote ya haraka' : 
                                'Contact us through any of these quick methods'}
                        </p>
                        <div class="d-grid gap-2">
                            <a href="tel:+255123456789" class="btn btn-primary">
                                <i class="fas fa-phone me-2"></i>
                                ${this.language === 'sw' ? 'Piga Simu' : 'Call Us'}
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i>
                                ${this.language === 'sw' ? 'Barua Pepe' : 'Email Us'}
                            </a>
                            <a href="https://wa.me/255123456789" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(supportModal);
    }

    bindEvents() {
        // Auto-start tour for new users
        if (window.location.search.includes('new_user=true')) {
            setTimeout(() => this.quickTour(), 2000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelpMenu();
            }
            if (this.isActive && e.key === 'Escape') {
                this.endTour();
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.smartHelp = new SmartHelp();
});

// Add CSS for hover effects
const style = document.createElement('style');
style.textContent = `
    .hover-bg-light:hover {
        background-color: rgba(0,0,0,0.05) !important;
    }
    .cursor-pointer {
        cursor: pointer;
    }
    #smart-help-button button:hover {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }
`;
document.head.appendChild(style);
