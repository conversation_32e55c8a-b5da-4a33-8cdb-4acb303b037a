#!/usr/bin/env python3
"""
Quick test to verify the Flask application is properly configured
"""

def test_imports():
    """Test if all modules can be imported correctly"""
    try:
        from flask import Flask
        print("+ Flask imported successfully")
        
        from flask_sqlalchemy import SQLAlchemy
        print("+ Flask-SQLAlchemy imported successfully")
        
        from flask_login import LoginManager
        print("+ Flask-Login imported successfully")
        
        from reportlab.lib.pagesizes import letter
        print("+ ReportLab imported successfully")
        
        from dotenv import load_dotenv
        print("+ python-dotenv imported successfully")
        
        return True
    except ImportError as e:
        print(f"- Import error: {e}")
        return False

def test_database():
    """Test database creation"""
    try:
        import sqlite3
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        
        # Test creating a simple table
        cursor.execute('CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)')
        cursor.execute('INSERT INTO test (name) VALUES (?)', ('Test Record',))
        cursor.execute('SELECT * FROM test')
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            print("+ Database operations working")
            return True
        else:
            print("- Database test failed")
            return False
            
    except Exception as e:
        print(f"- Database error: {e}")
        return False

def test_environment():
    """Test environment configuration"""
    try:
        from dotenv import load_dotenv
        import os
        
        load_dotenv()
        
        company_name = os.environ.get('COMPANY_NAME', 'Not Found')
        company_email = os.environ.get('COMPANY_EMAIL', 'Not Found')
        
        print(f"+ Company Name: {company_name}")
        print(f"+ Company Email: {company_email}")
        
        if company_name != 'Not Found':
            return True
        else:
            print("- Environment variables not loaded")
            return False
            
    except Exception as e:
        print(f"- Environment error: {e}")
        return False

def test_pdf_generation():
    """Test PDF generation capability"""
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        import io
        
        # Create a simple PDF in memory
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        
        content = [Paragraph("Test PDF Generation", styles['Title'])]
        doc.build(content)
        
        pdf_size = len(buffer.getvalue())
        buffer.close()
        
        if pdf_size > 0:
            print(f"+ PDF generation working (generated {pdf_size} bytes)")
            return True
        else:
            print("- PDF generation failed")
            return False
            
    except Exception as e:
        print(f"- PDF generation error: {e}")
        return False

def main():
    print("="*60)
    print("PAYMENT GATEWAY SYSTEM - DEPENDENCY TEST")
    print("="*60)
    
    all_tests_passed = True
    
    print("\n1. Testing Python Module Imports:")
    print("-" * 35)
    if not test_imports():
        all_tests_passed = False
    
    print("\n2. Testing Database Operations:")
    print("-" * 35)
    if not test_database():
        all_tests_passed = False
    
    print("\n3. Testing Environment Configuration:")
    print("-" * 40)
    if not test_environment():
        all_tests_passed = False
    
    print("\n4. Testing PDF Generation:")
    print("-" * 30)
    if not test_pdf_generation():
        all_tests_passed = False
    
    print("\n" + "="*60)
    if all_tests_passed:
        print("+ ALL TESTS PASSED - SYSTEM READY!")
        print("="*60)
        print("\nYour payment gateway is fully configured and ready to use!")
        print("\nTo start the application:")
        print("1. Run: python app.py")
        print("2. Open browser: http://localhost:5000") 
        print("3. Admin login: admin / admin123")
        print("\nKey features available:")
        print("- Create invoices with unique numbers")
        print("- Mobile money payment instructions")
        print("- Payment confirmation system")
        print("- Admin dashboard and management")
        print("- PDF receipt generation")
    else:
        print("- SOME TESTS FAILED")
        print("="*60)
        print("Please check the error messages above and install missing dependencies.")
    
if __name__ == "__main__":
    main()
