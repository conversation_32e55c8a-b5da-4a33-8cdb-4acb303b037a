{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}Dashibodi ya Utendaji{% else %}Performance Dashboard{% endif %} - EXLIPA
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">⚡ 
                        {% if session.language == 'sw' %}
                            Dashibodi ya Utendaji
                        {% else %}
                            Performance Dashboard
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">
                        {% if session.language == 'sw' %}
                            Fuatilia utendaji wa mfumo na haraka ya maombi
                        {% else %}
                            Monitor system performance and request speeds
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('master_admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        {% if session.language == 'sw' %}Rudi{% else %}Back{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-primary">{{ "%.0f"|format(stats.total_requests or 0) }}</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Maombi (Saa 1){% else %}Requests (1h){% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-success">{{ "%.2f"|format(stats.avg_response_time or 0) }}s</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Wastani wa Muda{% else %}Avg Response{% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-warning">{{ "%.2f"|format(stats.max_response_time or 0) }}s</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Muda wa Juu{% else %}Max Response{% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-danger">{{ stats.slow_requests or 0 }}</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Maombi Polepole{% else %}Slow Requests{% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Status -->
    <div class="row g-3 mb-4">
        <div class="col-md-6">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    {% if session.language == 'sw' %}Hali ya Utendaji{% else %}Performance Status{% endif %}
                </h5>
                
                {% set avg_time = stats.avg_response_time or 0 %}
                {% if avg_time < 0.5 %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>
                            {% if session.language == 'sw' %}Bora{% else %}Excellent{% endif %}
                        </strong> - 
                        {% if session.language == 'sw' %}
                            Mfumo unafanya kazi vizuri sana
                        {% else %}
                            System is performing excellently
                        {% endif %}
                    </div>
                {% elif avg_time < 1.0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>
                            {% if session.language == 'sw' %}Wastani{% else %}Good{% endif %}
                        </strong> - 
                        {% if session.language == 'sw' %}
                            Utendaji ni mzuri lakini unaweza kuboreshwa
                        {% else %}
                            Performance is good but could be optimized
                        {% endif %}
                    </div>
                {% else %}
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        <strong>
                            {% if session.language == 'sw' %}Unahitaji Uboreshaji{% else %}Needs Attention{% endif %}
                        </strong> - 
                        {% if session.language == 'sw' %}
                            Mfumo unahitaji uboreshaji wa haraka
                        {% else %}
                            System requires performance optimization
                        {% endif %}
                    </div>
                {% endif %}

                <!-- Performance Tips -->
                <div class="mt-3">
                    <h6 class="fw-bold">
                        {% if session.language == 'sw' %}Mapendekezo ya Uboreshaji{% else %}Optimization Tips{% endif %}
                    </h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-database text-primary me-2"></i>
                            {% if session.language == 'sw' %}
                                Tumia cache kwa data inayotumika mara kwa mara
                            {% else %}
                                Use caching for frequently accessed data
                            {% endif %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-compress text-success me-2"></i>
                            {% if session.language == 'sw' %}
                                Punguza ukubwa wa picha na faili za CSS/JS
                            {% else %}
                                Compress images and CSS/JS files
                            {% endif %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-search text-info me-2"></i>
                            {% if session.language == 'sw' %}
                                Ongeza indexes kwenye hifadhidata
                            {% else %}
                                Add database indexes for common queries
                            {% endif %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-line me-2 text-success"></i>
                    {% if session.language == 'sw' %}Takwimu za Haraka{% else %}Quick Stats{% endif %}
                </h5>
                
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <div class="h5 text-primary mb-1">{{ "%.2f"|format(stats.min_response_time or 0) }}s</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Muda wa Chini{% else %}Min Response{% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 border rounded">
                            <div class="h5 text-warning mb-1">{{ stats.slow_queries_count or 0 }}</div>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}Hojaji Polepole{% else %}Slow Queries{% endif %}
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Real-time Performance -->
                <div class="mt-4">
                    <h6 class="fw-bold mb-2">
                        {% if session.language == 'sw' %}Utendaji wa Wakati Halisi{% else %}Real-time Performance{% endif %}
                    </h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">
                            {% if session.language == 'sw' %}Kasi ya CPU{% else %}CPU Usage{% endif %}
                        </span>
                        <span class="small text-success" id="cpuUsage">--</span>
                    </div>
                    <div class="progress mb-3" style="height: 6px;">
                        <div class="progress-bar bg-success" id="cpuBar" style="width: 0%"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">
                            {% if session.language == 'sw' %}Kumbukumbu{% else %}Memory{% endif %}
                        </span>
                        <span class="small text-info" id="memoryUsage">--</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" id="memoryBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Actions -->
    <div class="row">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-tools me-2 text-warning"></i>
                    {% if session.language == 'sw' %}Vitendo vya Uboreshaji{% else %}Optimization Actions{% endif %}
                </h5>
                
                <div class="row g-3">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary w-100" onclick="clearCache()">
                            <i class="fas fa-broom me-2"></i>
                            {% if session.language == 'sw' %}Safisha Cache{% else %}Clear Cache{% endif %}
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-success w-100" onclick="optimizeDatabase()">
                            <i class="fas fa-database me-2"></i>
                            {% if session.language == 'sw' %}Boresha DB{% else %}Optimize DB{% endif %}
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info w-100" onclick="runPerformanceTest()">
                            <i class="fas fa-stopwatch me-2"></i>
                            {% if session.language == 'sw' %}Jaribio la Kasi{% else %}Speed Test{% endif %}
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning w-100" onclick="generateReport()">
                            <i class="fas fa-file-alt me-2"></i>
                            {% if session.language == 'sw' %}Ripoti{% else %}Report{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Real-time performance monitoring
function updateRealTimeStats() {
    // Simulate real-time data (in production, this would fetch from an API)
    const cpuUsage = Math.random() * 100;
    const memoryUsage = Math.random() * 100;
    
    document.getElementById('cpuUsage').textContent = cpuUsage.toFixed(1) + '%';
    document.getElementById('cpuBar').style.width = cpuUsage + '%';
    
    document.getElementById('memoryUsage').textContent = memoryUsage.toFixed(1) + '%';
    document.getElementById('memoryBar').style.width = memoryUsage + '%';
    
    // Update colors based on usage
    const cpuBar = document.getElementById('cpuBar');
    const memoryBar = document.getElementById('memoryBar');
    
    cpuBar.className = 'progress-bar ' + (cpuUsage > 80 ? 'bg-danger' : cpuUsage > 60 ? 'bg-warning' : 'bg-success');
    memoryBar.className = 'progress-bar ' + (memoryUsage > 80 ? 'bg-danger' : memoryUsage > 60 ? 'bg-warning' : 'bg-info');
}

// Performance actions
function clearCache() {
    if (confirm('{% if session.language == "sw" %}Una uhakika unataka kusafisha cache?{% else %}Are you sure you want to clear the cache?{% endif %}')) {
        // Implement cache clearing
        alert('{% if session.language == "sw" %}Cache imesafishwa!{% else %}Cache cleared!{% endif %}');
    }
}

function optimizeDatabase() {
    if (confirm('{% if session.language == "sw" %}Anza uboreshaji wa hifadhidata?{% else %}Start database optimization?{% endif %}')) {
        // Implement database optimization
        alert('{% if session.language == "sw" %}Uboreshaji umeanza!{% else %}Optimization started!{% endif %}');
    }
}

function runPerformanceTest() {
    alert('{% if session.language == "sw" %}Jaribio la utendaji limeanza...{% else %}Performance test started...{% endif %}');
    // Implement performance testing
}

function generateReport() {
    // Generate and download performance report
    const reportData = {
        timestamp: new Date().toISOString(),
        stats: {{ stats|tojson }},
        recommendations: [
            'Enable caching for frequently accessed data',
            'Optimize database queries',
            'Compress static assets',
            'Use CDN for static content'
        ]
    };
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'performance-report-' + new Date().toISOString().split('T')[0] + '.json';
    a.click();
    URL.revokeObjectURL(url);
}

// Auto-refresh real-time stats
setInterval(updateRealTimeStats, 5000);
updateRealTimeStats(); // Initial load
</script>
{% endblock %}
