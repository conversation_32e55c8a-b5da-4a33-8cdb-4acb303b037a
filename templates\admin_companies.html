{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Simamia Makampuni - Msimamizi
{%- else -%}
    Manage Companies - Admin
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-building me-2"></i>Manage Companies</h1>
    <a href="{{ url_for('create_company') }}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i>Add New Company
    </a>
</div>

<!-- Statistics Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h4 style="color: white; font-weight: 700;">{{ companies|length }}</h4>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Total Companies') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #10b981 0%, #**********%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h4 style="color: white; font-weight: 700;">{{ companies|selectattr('subscription_status', 'equalto', 'Active')|list|length }}</h4>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Active') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-pause-circle fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h4 style="color: white; font-weight: 700;">{{ companies|selectattr('subscription_status', 'equalto', 'Suspended')|list|length }}</h4>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Suspended') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-times-circle fa-2x mb-2" style="color: rgba(255,255,255,0.9);"></i>
                <h4 style="color: white; font-weight: 700;">{{ companies|selectattr('subscription_status', 'equalto', 'Cancelled')|list|length }}</h4>
                <p class="mb-0" style="color: rgba(255,255,255,0.9);">{{ t('Cancelled') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Companies Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Companies List
        </h5>
    </div>
    <div class="card-body">
        {% if companies %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>{{ t('Company') }}</th>
                        <th>{{ t('Contact') }}</th>
                        <th>Pricing Tier</th>
                        <th>{{ t('Status') }}</th>
                        <th>Next Billing</th>
                        <th>Revenue</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for company in companies %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ company.company_name }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Joined {{ company.created_at.strftime('%b %d, %Y') }}
                                </small>
                            </div>
                        </td>
                        <td>
                            <div>
                                {% if company.company_email %}
                                    <i class="fas fa-envelope me-1"></i>{{ company.company_email }}<br>
                                {% endif %}
                                {% if company.company_phone %}
                                    <i class="fas fa-phone me-1"></i>{{ company.company_phone }}
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if company.pricing_tier %}
                                <span class="badge bg-primary">{{ company.pricing_tier.name }}</span>
                                <br>
                                <small class="text-muted">
                                    {{ company.billing_cycle }}
                                </small>
                            {% else %}
                                <span class="text-muted">No tier assigned</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge 
                                {% if company.subscription_status == 'Active' %}bg-success
                                {% elif company.subscription_status == 'Suspended' %}bg-warning
                                {% elif company.subscription_status == 'Cancelled' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {{ company.subscription_status }}
                            </span>
                        </td>
                        <td>
                            {% if company.next_billing_date %}
                                {{ company.next_billing_date.strftime('%b %d, %Y') }}
                                {% set days_until = (company.next_billing_date - moment()).days %}
                                <br>
                                <small class="text-muted">
                                    {% if days_until < 0 %}
                                        <span class="text-danger">{{ days_until|abs }} days overdue</span>
                                    {% elif days_until == 0 %}
                                        <span class="text-warning">Due today</span>
                                    {% else %}
                                        {{ days_until }} days
                                    {% endif %}
                                </small>
                            {% else %}
                                <span class="text-muted">Not set</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong>TZS {{ "{:,.0f}".format(company.total_transaction_volume) }}</strong>
                            <br>
                            <small class="text-muted">Total volume</small>
                        </td>
                        <td>
                            <div class="btn-group-vertical btn-group-sm">
                                <a href="{{ url_for('edit_company', company_id=company.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{{ url_for('admin_billing', company_id=company.id) }}" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-file-invoice-dollar"></i> Bills
                                </a>
                                {% if company.subscription_status == 'Active' %}
                                <button class="btn btn-outline-warning btn-sm" 
                                        onclick="toggleStatus('{{ company.id }}', 'Suspended')">
                                    <i class="fas fa-pause"></i> Suspend
                                </button>
                                {% elif company.subscription_status == 'Suspended' %}
                                <button class="btn btn-outline-success btn-sm" 
                                        onclick="toggleStatus('{{ company.id }}', 'Active')">
                                    <i class="fas fa-play"></i> Activate
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Companies Yet</h4>
            <p class="text-muted">Start by adding your first client company</p>
            <a href="{{ url_for('create_company') }}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Add First Company
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function toggleStatus(companyId, newStatus) {
    if (confirm(`Are you sure you want to change the company status to ${newStatus}?`)) {
        fetch(`/admin/companies/${companyId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating status: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error updating status: ' + error);
        });
    }
}
</script>
{% endblock %}
