#!/usr/bin/env python3
"""
POS (Point of Sale) models for EXLIPA
"""

from datetime import datetime
from .base import db, BaseModel, ValidationMixin
from utils.validators import InputValidator, ValidationError

class PosProduct(BaseModel, ValidationMixin):
    """POS Product model"""
    
    __tablename__ = 'pos_product'
    
    # Product details
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    sku = db.Column(db.String(50), unique=True)  # Stock Keeping Unit
    barcode = db.Column(db.String(100), unique=True)
    
    # Pricing
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float)  # For profit calculation
    
    # Inventory
    stock_quantity = db.Column(db.Integer, default=0)
    low_stock_threshold = db.Column(db.Integer, default=10)
    track_inventory = db.Column(db.Boolean, default=True)
    
    # Category and organization
    category = db.Column(db.String(50))
    brand = db.Column(db.String(50))
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    
    # Company association
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    
    # Relationships
    company = db.relationship('ClientCompany', backref='pos_products')
    
    def __repr__(self):
        return f'<PosProduct {self.name}>'
    
    def validate(self):
        """Validate product data"""
        if not self.name:
            raise ValidationError("Product name is required")
        
        self.name = InputValidator.sanitize_string(self.name, 100)
        
        if not self.price or self.price < 0:
            raise ValidationError("Price must be a positive number")
        
        if self.cost_price is not None and self.cost_price < 0:
            raise ValidationError("Cost price must be a positive number")
        
        if self.stock_quantity < 0:
            raise ValidationError("Stock quantity cannot be negative")
    
    def is_low_stock(self):
        """Check if product is low on stock"""
        if not self.track_inventory:
            return False
        return self.stock_quantity <= self.low_stock_threshold
    
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        if not self.track_inventory:
            return False
        return self.stock_quantity <= 0
    
    def can_sell(self, quantity=1):
        """Check if product can be sold"""
        if not self.is_active:
            return False, "Product is inactive"
        
        if self.track_inventory and self.stock_quantity < quantity:
            return False, "Insufficient stock"
        
        return True, "OK"
    
    def reduce_stock(self, quantity):
        """Reduce stock quantity"""
        if self.track_inventory:
            if self.stock_quantity < quantity:
                raise ValidationError("Insufficient stock")
            self.stock_quantity -= quantity
            return self.save()
        return True
    
    def add_stock(self, quantity):
        """Add stock quantity"""
        if self.track_inventory:
            self.stock_quantity += quantity
            return self.save()
        return True
    
    def get_profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price and self.cost_price > 0:
            profit = self.price - self.cost_price
            return (profit / self.cost_price) * 100
        return 0
    
    @classmethod
    def get_low_stock_products(cls, company_id):
        """Get products with low stock"""
        return cls.query.filter(
            cls.company_id == company_id,
            cls.is_active == True,
            cls.track_inventory == True,
            cls.stock_quantity <= cls.low_stock_threshold
        ).all()
    
    @classmethod
    def search_products(cls, company_id, search_term):
        """Search products by name, SKU, or barcode"""
        return cls.query.filter(
            cls.company_id == company_id,
            cls.is_active == True,
            (cls.name.contains(search_term) | 
             cls.sku.contains(search_term) | 
             cls.barcode.contains(search_term))
        ).all()

class PosSale(BaseModel, ValidationMixin):
    """POS Sale model"""
    
    __tablename__ = 'pos_sale'
    
    # Sale details
    sale_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_name = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    
    # Financial details
    subtotal = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    
    # Payment details
    payment_method = db.Column(db.String(50), nullable=False)  # Cash, Mobile Money, Card
    amount_paid = db.Column(db.Float, nullable=False)
    change_amount = db.Column(db.Float, default=0.0)
    
    # Mobile money details (if applicable)
    mobile_operator = db.Column(db.String(50))
    transaction_id = db.Column(db.String(100))
    
    # Status
    status = db.Column(db.String(20), default='Completed')  # Completed, Refunded, Cancelled
    
    # Associations
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    cashier_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    cash_drawer_session_id = db.Column(db.Integer, db.ForeignKey('cash_drawer_session.id'))
    
    # Relationships
    company = db.relationship('ClientCompany', backref='pos_sales')
    cashier = db.relationship('User', backref='pos_sales')
    cash_drawer_session = db.relationship('CashDrawerSession', backref='pos_sales')
    
    def __repr__(self):
        return f'<PosSale {self.sale_number}>'
    
    def validate(self):
        """Validate sale data"""
        if not self.sale_number:
            raise ValidationError("Sale number is required")
        
        if not self.subtotal or self.subtotal < 0:
            raise ValidationError("Subtotal must be a positive number")
        
        if not self.total_amount or self.total_amount < 0:
            raise ValidationError("Total amount must be a positive number")
        
        if not self.amount_paid or self.amount_paid < 0:
            raise ValidationError("Amount paid must be a positive number")
        
        if not self.payment_method:
            raise ValidationError("Payment method is required")
    
    def calculate_totals(self):
        """Calculate sale totals from items"""
        items = PosSaleItem.query.filter_by(sale_id=self.id).all()
        
        self.subtotal = sum(item.line_total for item in items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        
        if self.amount_paid >= self.total_amount:
            self.change_amount = self.amount_paid - self.total_amount
        else:
            raise ValidationError("Amount paid is less than total amount")
    
    def add_item(self, product_id, quantity, unit_price=None):
        """Add item to sale"""
        from .pos import PosProduct
        
        product = PosProduct.get_by_id(product_id)
        if not product:
            raise ValidationError("Product not found")
        
        can_sell, message = product.can_sell(quantity)
        if not can_sell:
            raise ValidationError(message)
        
        # Use product price if unit price not specified
        if unit_price is None:
            unit_price = product.price
        
        # Create sale item
        item = PosSaleItem(
            sale_id=self.id,
            product_id=product_id,
            quantity=quantity,
            unit_price=unit_price,
            line_total=quantity * unit_price
        )
        
        item.save()
        
        # Reduce product stock
        product.reduce_stock(quantity)
        
        # Recalculate totals
        self.calculate_totals()
        self.save()
        
        return item
    
    @classmethod
    def generate_sale_number(cls, company_id):
        """Generate unique sale number"""
        # Get current count for sequence
        count = cls.query.filter_by(company_id=company_id).count() + 1
        
        # Generate format: SALE-YYYYMMDD-NNNN
        date_str = datetime.now().strftime('%Y%m%d')
        sequence = f"{count:04d}"
        
        return f"SALE-{date_str}-{sequence}"
    
    @classmethod
    def get_daily_sales(cls, company_id, date=None):
        """Get sales for a specific date"""
        if date is None:
            date = datetime.now().date()
        
        start_date = datetime.combine(date, datetime.min.time())
        end_date = datetime.combine(date, datetime.max.time())
        
        return cls.query.filter(
            cls.company_id == company_id,
            cls.created_at >= start_date,
            cls.created_at <= end_date,
            cls.status == 'Completed'
        ).all()

class PosSaleItem(BaseModel):
    """POS Sale Item model"""
    
    __tablename__ = 'pos_sale_item'
    
    # Associations
    sale_id = db.Column(db.Integer, db.ForeignKey('pos_sale.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('pos_product.id'), nullable=False)
    
    # Item details
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    line_total = db.Column(db.Float, nullable=False)
    
    # Relationships
    sale = db.relationship('PosSale', backref='items')
    product = db.relationship('PosProduct', backref='sale_items')
    
    def __repr__(self):
        return f'<PosSaleItem {self.product.name} x{self.quantity}>'

class CashDrawerSession(BaseModel, ValidationMixin):
    """Cash drawer session model"""
    
    __tablename__ = 'cash_drawer_session'
    
    # Session details
    session_number = db.Column(db.String(50), unique=True, nullable=False)
    opening_amount = db.Column(db.Float, nullable=False)
    closing_amount = db.Column(db.Float)
    expected_amount = db.Column(db.Float)
    variance_amount = db.Column(db.Float)
    
    # Timestamps
    opened_at = db.Column(db.DateTime, default=datetime.utcnow)
    closed_at = db.Column(db.DateTime)
    
    # Status
    status = db.Column(db.String(20), default='Open')  # Open, Closed
    
    # Associations
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    opened_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    closed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    company = db.relationship('ClientCompany', backref='cash_drawer_sessions')
    opener = db.relationship('User', foreign_keys=[opened_by])
    closer = db.relationship('User', foreign_keys=[closed_by])
    
    def __repr__(self):
        return f'<CashDrawerSession {self.session_number}>'
    
    def close_session(self, closing_amount, closed_by_id):
        """Close the cash drawer session"""
        self.closing_amount = closing_amount
        self.closed_at = datetime.utcnow()
        self.closed_by = closed_by_id
        self.status = 'Closed'
        
        # Calculate expected amount and variance
        cash_sales = PosSale.query.filter(
            PosSale.cash_drawer_session_id == self.id,
            PosSale.payment_method == 'Cash',
            PosSale.status == 'Completed'
        ).all()
        
        total_cash_sales = sum(sale.total_amount for sale in cash_sales)
        self.expected_amount = self.opening_amount + total_cash_sales
        self.variance_amount = self.closing_amount - self.expected_amount
        
        return self.save()
    
    @classmethod
    def get_current_session(cls, company_id):
        """Get current open cash drawer session"""
        return cls.query.filter_by(
            company_id=company_id,
            status='Open'
        ).first()
    
    @classmethod
    def generate_session_number(cls, company_id):
        """Generate unique session number"""
        count = cls.query.filter_by(company_id=company_id).count() + 1
        date_str = datetime.now().strftime('%Y%m%d')
        return f"DRAWER-{date_str}-{count:04d}"

class CashDrawerTransaction(BaseModel):
    """Cash drawer transaction model for tracking cash in/out"""
    
    __tablename__ = 'cash_drawer_transaction'
    
    # Transaction details
    transaction_type = db.Column(db.String(20), nullable=False)  # Sale, Refund, PayIn, PayOut
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.String(200))
    reference = db.Column(db.String(100))
    
    # Associations
    cash_drawer_session_id = db.Column(db.Integer, db.ForeignKey('cash_drawer_session.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Relationships
    cash_drawer_session = db.relationship('CashDrawerSession', backref='transactions')
    user = db.relationship('User', backref='cash_drawer_transactions')
    
    def __repr__(self):
        return f'<CashDrawerTransaction {self.transaction_type} {self.amount}>'
