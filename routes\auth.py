#!/usr/bin/env python3
"""
Authentication routes for EXLIPA
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_required, current_user
from werkzeug.security import check_password_hash
from services.auth_service import AuthenticationManager, require_super_admin, require_user_admin
from utils.validators import InputValidator, ValidationError
from models.user import User
from models.base import db
import logging

logger = logging.getLogger(__name__)

# Create blueprint
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/master-admin-login', methods=['GET', 'POST'])
def master_admin_login():
    """Master admin login route"""
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            
            # Validate inputs
            if not username or not password:
                flash('Username and password are required.', 'danger')
                return render_template('master_admin_login.html')
            
            # Validate username format
            try:
                username = InputValidator.validate_username(username)
            except ValidationError as e:
                flash(str(e), 'danger')
                return render_template('master_admin_login.html')
            
            # Find user
            user = User.query.filter_by(username=username, role='super_admin').first()
            
            if not user:
                flash('Invalid username or password.', 'danger')
                return render_template('master_admin_login.html')
            
            # Check if account is locked
            if user.is_account_locked():
                flash('Account is temporarily locked due to failed login attempts.', 'warning')
                return render_template('master_admin_login.html')
            
            # Verify password
            if user and check_password_hash(user.password_hash, password):
                # Successful login using new authentication system
                user.unlock_account()  # Reset failed attempts
                db.session.commit()
                
                # Use new authentication manager
                if AuthenticationManager.login_user_with_role(user):
                    logger.info(f"Master admin login successful: {username}")
                    return AuthenticationManager.smart_redirect_by_role(user)
                else:
                    flash('Login failed. Please try again.', 'danger')
            else:
                # Failed login
                user.increment_failed_login()
                flash('Invalid username or password.', 'danger')
                logger.warning(f"Master admin login failed: {username}")
        
        except Exception as e:
            logger.error(f"Master admin login error: {str(e)}")
            flash('An error occurred during login. Please try again.', 'danger')
    
    return render_template('master_admin_login.html')

@auth_bp.route('/user-admin-login', methods=['GET', 'POST'])
def user_admin_login():
    """User admin login route"""
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            
            # Validate inputs
            if not username or not password:
                flash('Username and password are required.', 'danger')
                return render_template('user_admin_login.html')
            
            # Validate username format
            try:
                username = InputValidator.validate_username(username)
            except ValidationError as e:
                flash(str(e), 'danger')
                return render_template('user_admin_login.html')
            
            # Find user
            user = User.query.filter_by(username=username, role='user_admin').first()
            
            if not user:
                flash('Invalid username or password.', 'danger')
                return render_template('user_admin_login.html')
            
            # Check if account is locked
            if user.is_account_locked():
                flash('Account is temporarily locked due to failed login attempts.', 'warning')
                return render_template('user_admin_login.html')
            
            # Verify password
            if user and check_password_hash(user.password_hash, password):
                # Successful login using new authentication system
                user.unlock_account()  # Reset failed attempts
                db.session.commit()
                
                # Use new authentication manager
                if AuthenticationManager.login_user_with_role(user):
                    logger.info(f"User admin login successful: {username}")
                    return AuthenticationManager.smart_redirect_by_role(user)
                else:
                    flash('Login failed. Please try again.', 'danger')
            else:
                # Failed login
                user.increment_failed_login()
                flash('Invalid username or password.', 'danger')
                logger.warning(f"User admin login failed: {username}")
        
        except Exception as e:
            logger.error(f"User admin login error: {str(e)}")
            flash('An error occurred during login. Please try again.', 'danger')
    
    return render_template('user_admin_login.html')

@auth_bp.route('/company-login', methods=['GET', 'POST'])
def company_login():
    """Company user login route"""
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            
            # Validate inputs
            if not username or not password:
                flash('Username and password are required.', 'danger')
                return render_template('company_login.html')
            
            # Validate username format
            try:
                username = InputValidator.validate_username(username)
            except ValidationError as e:
                flash(str(e), 'danger')
                return render_template('company_login.html')
            
            # Find user
            user = User.query.filter_by(username=username, role='company_user').first()
            
            if not user:
                flash('Invalid username or password.', 'danger')
                return render_template('company_login.html')
            
            # Check if account is locked
            if user.is_account_locked():
                flash('Account is temporarily locked due to failed login attempts.', 'warning')
                return render_template('company_login.html')
            
            # Verify password
            if user and check_password_hash(user.password_hash, password):
                # Successful login using new authentication system
                user.unlock_account()  # Reset failed attempts
                db.session.commit()
                
                # Use new authentication manager
                if AuthenticationManager.login_user_with_role(user):
                    logger.info(f"Company user login successful: {username}")
                    return AuthenticationManager.smart_redirect_by_role(user)
                else:
                    flash('Login failed. Please try again.', 'danger')
            else:
                # Failed login
                user.increment_failed_login()
                flash('Invalid username or password.', 'danger')
                logger.warning(f"Company user login failed: {username}")
        
        except Exception as e:
            logger.error(f"Company user login error: {str(e)}")
            flash('An error occurred during login. Please try again.', 'danger')
    
    return render_template('company_login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """Logout route"""
    try:
        # Use new authentication manager for secure logout
        AuthenticationManager.logout_user_safe()
        flash('You have been logged out successfully.', 'info')
        logger.info(f"User logged out: {current_user.username if current_user.is_authenticated else 'Unknown'}")
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        flash('An error occurred during logout.', 'warning')
    
    return redirect(url_for('index'))

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password route"""
    if request.method == 'POST':
        try:
            current_password = request.form.get('current_password', '')
            new_password = request.form.get('new_password', '')
            confirm_password = request.form.get('confirm_password', '')
            
            # Validate inputs
            if not current_password or not new_password or not confirm_password:
                flash('All fields are required.', 'danger')
                return render_template('change_password.html')
            
            # Verify current password
            if not check_password_hash(current_user.password_hash, current_password):
                flash('Current password is incorrect.', 'danger')
                return render_template('change_password.html')
            
            # Validate new password
            if new_password != confirm_password:
                flash('New passwords do not match.', 'danger')
                return render_template('change_password.html')
            
            try:
                # Validate password strength
                validated_password = InputValidator.validate_password(new_password)
                
                # Update password
                current_user.set_password(validated_password)
                db.session.commit()
                
                flash('Password changed successfully.', 'success')
                logger.info(f"Password changed for user: {current_user.username}")
                
                return redirect(url_for('auth.change_password'))
                
            except ValidationError as e:
                flash(str(e), 'danger')
                return render_template('change_password.html')
        
        except Exception as e:
            logger.error(f"Change password error: {str(e)}")
            flash('An error occurred while changing password.', 'danger')
    
    return render_template('change_password.html')

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile route"""
    if request.method == 'POST':
        try:
            full_name = request.form.get('full_name', '').strip()
            email = request.form.get('email', '').strip()
            preferred_language = request.form.get('preferred_language', 'en')
            
            # Validate inputs
            if not full_name or not email:
                flash('Full name and email are required.', 'danger')
                return render_template('profile.html')
            
            try:
                # Validate email
                email = InputValidator.validate_email(email)
                full_name = InputValidator.sanitize_string(full_name, 100)
                
                # Check if email is already taken by another user
                existing_user = User.query.filter(
                    User.email == email,
                    User.id != current_user.id
                ).first()
                
                if existing_user:
                    flash('Email is already taken by another user.', 'danger')
                    return render_template('profile.html')
                
                # Update user profile
                current_user.full_name = full_name
                current_user.email = email
                current_user.preferred_language = preferred_language
                
                db.session.commit()
                
                flash('Profile updated successfully.', 'success')
                logger.info(f"Profile updated for user: {current_user.username}")
                
            except ValidationError as e:
                flash(str(e), 'danger')
                return render_template('profile.html')
        
        except Exception as e:
            logger.error(f"Profile update error: {str(e)}")
            flash('An error occurred while updating profile.', 'danger')
    
    return render_template('profile.html')
