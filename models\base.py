#!/usr/bin/env python3
"""
Base model classes for EXLIPA
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from utils.validators import InputValidator, ValidationError

# Initialize SQLAlchemy instance
db = SQLAlchemy()

class BaseModel(db.Model):
    """Base model with common fields and methods"""
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def save(self):
        """Save the model to database"""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def delete(self):
        """Delete the model from database"""
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def to_dict(self):
        """Convert model to dictionary"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
    
    @classmethod
    def get_by_id(cls, id):
        """Get model by ID"""
        return cls.query.get(id)
    
    @classmethod
    def get_or_404(cls, id):
        """Get model by ID or raise 404"""
        return cls.query.get_or_404(id)

class TimestampMixin:
    """Mixin for timestamp fields"""
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ValidationMixin:
    """Mixin for model validation"""
    
    def validate(self):
        """Override in subclasses for custom validation"""
        pass
    
    def validate_and_save(self):
        """Validate and save the model"""
        self.validate()
        return self.save()

class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    is_deleted = db.Column(db.Boolean, default=False)
    deleted_at = db.Column(db.DateTime)
    
    def soft_delete(self):
        """Soft delete the model"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        return self.save()
    
    def restore(self):
        """Restore soft deleted model"""
        self.is_deleted = False
        self.deleted_at = None
        return self.save()
    
    @classmethod
    def active(cls):
        """Query only non-deleted records"""
        return cls.query.filter_by(is_deleted=False)
