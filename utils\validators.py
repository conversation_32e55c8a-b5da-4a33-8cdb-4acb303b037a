#!/usr/bin/env python3
"""
Input Validation Utilities for EXLIPA
Comprehensive validation for all user inputs
"""

import re
import html
from decimal import Decimal, InvalidOperation
from datetime import datetime
from typing import Optional, Union, List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom validation error"""
    pass

class InputValidator:
    """Comprehensive input validation class"""
    
    # Regex patterns
    EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    PHONE_PATTERN = r'^(\+255|0)[67]\d{8}$'  # Tanzanian phone format
    TIN_PATTERN = r'^\d{3}-\d{3}-\d{3}$'  # Tanzanian TIN format
    TRANSACTION_ID_PATTERN = r'^[A-Z0-9]{8,20}$'  # Mobile money transaction ID
    USERNAME_PATTERN = r'^[a-zA-Z0-9_]{3,50}$'  # Username format
    
    # Mobile money operators
    MOBILE_OPERATORS = ['M-Pesa', 'Tigo Pesa', 'Airtel Money', 'CRDB Lipa', 'Halo Pesa']
    
    # Payment statuses
    PAYMENT_STATUSES = ['Pending', 'Confirmed', 'Rejected', 'Processed']
    
    # User roles
    USER_ROLES = ['super_admin', 'user_admin', 'company_user']
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 255, allow_html: bool = False) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            raise ValidationError("Input must be a string")
        
        # Strip whitespace
        value = value.strip()
        
        # Check length
        if len(value) > max_length:
            raise ValidationError(f"Input too long. Maximum {max_length} characters allowed")
        
        # HTML escape if not allowing HTML
        if not allow_html:
            value = html.escape(value)
        
        return value
    
    @classmethod
    def validate_email(cls, email: str) -> str:
        """Validate email format"""
        if not email:
            raise ValidationError("Email is required")
        
        email = cls.sanitize_string(email, 120).lower()
        
        if not re.match(cls.EMAIL_PATTERN, email):
            raise ValidationError("Invalid email format")
        
        return email
    
    @classmethod
    def validate_phone(cls, phone: str, required: bool = False) -> Optional[str]:
        """Validate Tanzanian phone number"""
        if not phone:
            if required:
                raise ValidationError("Phone number is required")
            return None
        
        phone = cls.sanitize_string(phone, 20)
        
        if not re.match(cls.PHONE_PATTERN, phone):
            raise ValidationError("Invalid phone number format. Use +255XXXXXXXXX or 0XXXXXXXXX")
        
        return phone
    
    @classmethod
    def validate_username(cls, username: str) -> str:
        """Validate username"""
        if not username:
            raise ValidationError("Username is required")
        
        username = cls.sanitize_string(username, 50)
        
        if not re.match(cls.USERNAME_PATTERN, username):
            raise ValidationError("Username must be 3-50 characters, letters, numbers, and underscores only")
        
        return username
    
    @classmethod
    def validate_password(cls, password: str, min_length: int = 8) -> str:
        """Validate password strength"""
        if not password:
            raise ValidationError("Password is required")
        
        if len(password) < min_length:
            raise ValidationError(f"Password must be at least {min_length} characters")
        
        # Check for basic complexity
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        
        if not (has_upper and has_lower and has_digit):
            raise ValidationError("Password must contain uppercase, lowercase, and digit")
        
        return password
    
    @classmethod
    def validate_amount(cls, amount: Union[str, float, int], min_amount: float = 1000, max_amount: float = 10000000) -> float:
        """Validate payment amount"""
        try:
            if isinstance(amount, str):
                # Remove commas and spaces
                amount = amount.replace(',', '').replace(' ', '')
                amount = float(amount)
            elif isinstance(amount, int):
                amount = float(amount)
            elif not isinstance(amount, float):
                raise ValidationError("Invalid amount format")
            
            # Check range
            if amount < min_amount:
                raise ValidationError(f"Minimum amount is TZS {min_amount:,.2f}")
            
            if amount > max_amount:
                raise ValidationError(f"Maximum amount is TZS {max_amount:,.2f}")
            
            # Round to 2 decimal places
            return round(amount, 2)
            
        except (ValueError, InvalidOperation):
            raise ValidationError("Invalid amount format")
    
    @classmethod
    def validate_transaction_id(cls, transaction_id: str, operator: str = None) -> str:
        """Validate mobile money transaction ID"""
        if not transaction_id:
            raise ValidationError("Transaction ID is required")
        
        transaction_id = cls.sanitize_string(transaction_id, 50).upper()
        
        if len(transaction_id) < 8:
            raise ValidationError("Transaction ID must be at least 8 characters")
        
        # Basic format validation
        if not re.match(cls.TRANSACTION_ID_PATTERN, transaction_id):
            raise ValidationError("Invalid transaction ID format")
        
        return transaction_id
    
    @classmethod
    def validate_mobile_operator(cls, operator: str) -> str:
        """Validate mobile money operator"""
        if not operator:
            raise ValidationError("Mobile operator is required")
        
        operator = cls.sanitize_string(operator, 50)
        
        if operator not in cls.MOBILE_OPERATORS:
            raise ValidationError(f"Invalid mobile operator. Must be one of: {', '.join(cls.MOBILE_OPERATORS)}")
        
        return operator
    
    @classmethod
    def validate_role(cls, role: str) -> str:
        """Validate user role"""
        if not role:
            raise ValidationError("Role is required")
        
        role = cls.sanitize_string(role, 20)
        
        if role not in cls.USER_ROLES:
            raise ValidationError(f"Invalid role. Must be one of: {', '.join(cls.USER_ROLES)}")
        
        return role
    
    @classmethod
    def validate_tin(cls, tin: str, required: bool = False) -> Optional[str]:
        """Validate Tanzanian TIN"""
        if not tin:
            if required:
                raise ValidationError("TIN is required")
            return None
        
        tin = cls.sanitize_string(tin, 20)
        
        if not re.match(cls.TIN_PATTERN, tin):
            raise ValidationError("Invalid TIN format. Use XXX-XXX-XXX")
        
        return tin
    
    @classmethod
    def validate_payment_status(cls, status: str) -> str:
        """Validate payment status"""
        if not status:
            raise ValidationError("Payment status is required")
        
        status = cls.sanitize_string(status, 20)
        
        if status not in cls.PAYMENT_STATUSES:
            raise ValidationError(f"Invalid payment status. Must be one of: {', '.join(cls.PAYMENT_STATUSES)}")
        
        return status
    
    @classmethod
    def validate_date(cls, date_str: str, date_format: str = '%Y-%m-%d') -> datetime:
        """Validate date string"""
        if not date_str:
            raise ValidationError("Date is required")
        
        try:
            return datetime.strptime(date_str, date_format)
        except ValueError:
            raise ValidationError(f"Invalid date format. Expected {date_format}")
    
    @classmethod
    def validate_json_data(cls, data: Dict[str, Any], required_fields: List[str]) -> Dict[str, Any]:
        """Validate JSON data structure"""
        if not isinstance(data, dict):
            raise ValidationError("Invalid JSON data")
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")
        
        return data
    
    @classmethod
    def validate_api_key(cls, api_key: str) -> str:
        """Validate API key format"""
        if not api_key:
            raise ValidationError("API key is required")
        
        api_key = cls.sanitize_string(api_key, 100)
        
        if len(api_key) < 16:
            raise ValidationError("API key must be at least 16 characters")
        
        return api_key

def safe_int(value: Any, default: int = 0) -> int:
    """Safely convert value to integer"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_bool(value: Any, default: bool = False) -> bool:
    """Safely convert value to boolean"""
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on')
    if isinstance(value, (int, float)):
        return bool(value)
    return default

# SQL Injection prevention
def escape_sql_like(value: str) -> str:
    """Escape special characters for SQL LIKE queries"""
    if not value:
        return value
    return value.replace('%', '\\%').replace('_', '\\_')

# XSS prevention
def sanitize_html(html_content: str, allowed_tags: List[str] = None) -> str:
    """Sanitize HTML content"""
    if not html_content:
        return html_content
    
    # Basic HTML escaping
    return html.escape(html_content)

# CSRF token validation
def validate_csrf_token(token: str, session_token: str) -> bool:
    """Validate CSRF token"""
    if not token or not session_token:
        return False
    return token == session_token
