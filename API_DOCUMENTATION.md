# 🔌 EXLIPA Payment Gateway - API Documentation

## 📋 **API Overview**

The EXLIPA Payment Gateway provides RESTful APIs for payment processing, SMS verification, and business management. All APIs include comprehensive validation, security measures, and error handling.

---

## 🔐 **Authentication**

### **Session-Based Authentication**
All API endpoints require proper authentication through the web interface. Users must login through their respective portals:

- **Master Admin**: `/admin-login`
- **User Admin**: `/login`
- **Company User**: `/company-login`

### **Rate Limiting**
All endpoints are protected with rate limiting:
- **Login endpoints**: 5 attempts per 15 minutes per IP
- **Payment endpoints**: 10 submissions per 15 minutes per IP
- **General endpoints**: 100 requests per minute per user

---

## 💳 **Payment Processing APIs**

### **Submit Payment Confirmation**
Submit a new payment confirmation for SMS verification.

**Endpoint**: `POST /confirm-payment`

**Request Body** (application/x-www-form-urlencoded):
```
customer_name=<PERSON>
customer_email=<EMAIL>
mobile_money_sender_name=<PERSON>
amount=50000
mobile_operator=M-Pesa
transaction_id=**********
service_description=Business tier signup
```

**Validation Rules**:
- `customer_name`: Required, 2-100 characters, letters and spaces only
- `customer_email`: Required, valid email format
- `mobile_money_sender_name`: Required, 2-100 characters, letters and spaces only
- `amount`: Required, numeric, 1000-10000000 (TZS)
- `mobile_operator`: Required, one of: M-Pesa, TigoPesa, Airtel Money, CRDB Lipa
- `transaction_id`: Required, 8+ characters, unique, format validated per operator
- `service_description`: Optional, max 500 characters

**Response** (Success - 200):
```html
<!-- Redirects to confirmation success page -->
Location: /confirmation-success
```

**Response** (Validation Error - 400):
```html
<!-- Returns form with error messages -->
<div class="alert alert-danger">
    Minimum payment amount is TZS 1,000
</div>
```

**Response** (Rate Limited - 429):
```html
<div class="alert alert-danger">
    Too many payment submissions. Please try again in 15 minutes.
</div>
```

### **Check Payment Status**
Allow customers to check their payment confirmation status.

**Endpoint**: `POST /check-payment-status`

**Request Body**:
```
customer_name=John Doe
reference_id=REF000001
transaction_id=**********
```

**Response** (Found - 200):
```html
<!-- Payment status page with details -->
<div class="payment-status">
    <h3>Payment Status: Confirmed</h3>
    <p>Reference: REF000001</p>
    <p>Amount: TZS 50,000</p>
    <p>Status: Your payment has been verified and registration link sent.</p>
</div>
```

**Response** (Not Found - 404):
```html
<div class="alert alert-warning">
    No payment found matching the provided details.
</div>
```

### **Pricing Tier Signup**
Submit payment for a specific pricing tier.

**Endpoint**: `POST /start-signup/<int:tier_id>`

**Request Body**:
```
customer_name=John Doe
customer_email=<EMAIL>
mobile_money_sender_name=John Doe
mobile_operator=M-Pesa
transaction_id=**********
```

**Response** (Success - 200):
```html
<!-- Redirects to confirmation success page -->
Location: /confirmation-success
```

---

## 🔧 **Admin Management APIs**

### **Payment Verification Action**
Admin endpoint to approve or reject payment confirmations with SMS verification.

**Endpoint**: `POST /admin/payment/<int:payment_id>/action`
**Authentication**: Master Admin required

**Request Body** (Approve):
```
action=confirm
sms_sender=MPESA
sms_amount=50000
sms_sender_name=John Doe
sms_transaction_ref=**********
admin_verification_notes=SMS verified successfully, amounts match
```

**Request Body** (Reject):
```
action=reject
rejection_reason=Amount mismatch - SMS shows TZS 45,000 but customer claimed TZS 50,000
```

**Response** (Success - 200):
```html
<!-- Redirects to payment detail page -->
Location: /admin/payment/123
```

**Validation for Approval**:
- `sms_sender`: Required, one of: MPESA, TIGOPESA, AIRTELMONEY, CRDB
- `sms_amount`: Required, numeric, must match payment amount (±1 TZS tolerance)
- `sms_sender_name`: Required, must match `mobile_money_sender_name`
- `sms_transaction_ref`: Optional, transaction reference from SMS
- `admin_verification_notes`: Optional, max 1000 characters

### **Resend Registration Link**
Resend registration link to customer email.

**Endpoint**: `POST /admin/payment/<int:payment_id>/resend-link`
**Authentication**: Master Admin required

**Response** (Success - 200):
```json
{
    "success": true,
    "message": "Registration link sent <NAME_EMAIL>"
}
```

**Response** (Error - 400):
```json
{
    "error": "Payment not confirmed or email not available"
}
```

---

## 🏢 **Company Management APIs**

### **Company Landing Page**
Public endpoint for company-specific payment processing.

**Endpoint**: `GET /company/<int:company_id>`

**Response** (Success - 200):
```html
<!-- Company branded payment page -->
<div class="company-landing">
    <h1>Pay Tech Solutions Ltd</h1>
    <form method="POST">
        <!-- Payment form with company branding -->
    </form>
</div>
```

**Endpoint**: `POST /company/<int:company_id>`

**Request Body**:
```
customer_name=Customer ABC
amount=15000
payment_method=M-Pesa
transaction_id=ND87654321
notes=Service payment for website development
```

**Note**: Company landing pages do NOT collect email addresses as they are for existing customers making service payments.

### **Company Settings Update**
Update company profile and branding settings.

**Endpoint**: `POST /company-settings`
**Authentication**: User Admin or Company User required

**Request Body**:
```
company_name=Tech Solutions Ltd
company_address=Dar es Salaam, Tanzania
company_phone=+255712345678
company_email=<EMAIL>
primary_color=#28a745
secondary_color=#007bff
landing_page_title=Pay Tech Solutions
landing_page_description=Secure payment portal for our services
```

---

## 🛒 **POS System APIs**

### **Process POS Sale**
Process a point-of-sale transaction with inventory management.

**Endpoint**: `POST /pos/sale`
**Authentication**: User Admin or Company User required

**Request Body** (JSON):
```json
{
    "items": [
        {
            "id": 1,
            "name": "Product A",
            "price": 5000,
            "quantity": 2,
            "total": 10000
        }
    ],
    "total_amount": 10000,
    "payment_method": "Cash",
    "amount_received": 10000,
    "customer_name": "Walk-in Customer"
}
```

**Response** (Success - 200):
```json
{
    "success": true,
    "sale_id": 123,
    "sale_number": "SALE-001",
    "receipt_url": "/pos/receipt/123"
}
```

**Response** (Inventory Error - 400):
```json
{
    "error": "Inventory validation failed",
    "details": [
        "Insufficient stock for Product A. Available: 1, Requested: 2"
    ]
}
```

### **Add/Update Product**
Manage POS products and inventory.

**Endpoint**: `POST /pos/products`
**Authentication**: User Admin or Company User required

**Request Body**:
```
name=New Product
price=7500
stock_quantity=50
track_inventory=true
low_stock_alert=10
category=Electronics
```

**Response** (Success - 200):
```json
{
    "success": true,
    "product_id": 456,
    "message": "Product created successfully"
}
```

---

## 💰 **Cash Drawer APIs**

### **Open Cash Drawer Session**
Start a new cash drawer session.

**Endpoint**: `POST /pos/cash-drawer/open`
**Authentication**: User Admin or Company User required

**Request Body**:
```
opening_balance=50000
notes=Starting shift with counted cash
```

**Response** (Success - 200):
```json
{
    "success": true,
    "session_id": 789,
    "session_number": "CD-2024-001",
    "opening_balance": 50000
}
```

### **Close Cash Drawer Session**
End cash drawer session with reconciliation.

**Endpoint**: `POST /pos/cash-drawer/close`
**Authentication**: User Admin or Company User required

**Request Body**:
```
closing_balance=75000
notes=End of shift reconciliation
```

**Response** (Success - 200):
```json
{
    "success": true,
    "session_closed": true,
    "expected_balance": 74500,
    "actual_balance": 75000,
    "variance": 500
}
```

---

## 📊 **Reporting APIs**

### **Payment Summary Report**
Get payment processing statistics.

**Endpoint**: `GET /admin/reports/payments`
**Authentication**: Master Admin required

**Query Parameters**:
- `start_date`: YYYY-MM-DD format
- `end_date`: YYYY-MM-DD format
- `status`: pending, confirmed, rejected (optional)

**Response** (Success - 200):
```json
{
    "summary": {
        "total_payments": 150,
        "confirmed_payments": 142,
        "pending_payments": 5,
        "rejected_payments": 3,
        "total_amount": 7500000,
        "average_verification_time": "3.2 hours"
    },
    "daily_breakdown": [
        {
            "date": "2024-01-15",
            "payments": 25,
            "amount": 1250000
        }
    ]
}
```

---

## ⚠️ **Error Handling**

### **Standard Error Responses**

**Validation Error (400)**:
```json
{
    "error": "Validation failed",
    "details": [
        "Amount must be between TZS 1,000 and TZS 10,000,000",
        "Transaction ID format invalid for M-Pesa"
    ]
}
```

**Authentication Error (401)**:
```json
{
    "error": "Authentication required",
    "message": "Please login to access this resource"
}
```

**Authorization Error (403)**:
```json
{
    "error": "Access denied",
    "message": "Insufficient permissions for this operation"
}
```

**Rate Limit Error (429)**:
```json
{
    "error": "Rate limit exceeded",
    "message": "Too many requests. Please try again in 15 minutes.",
    "retry_after": 900
}
```

**Server Error (500)**:
```json
{
    "error": "Internal server error",
    "message": "An unexpected error occurred. Please try again later.",
    "reference": "ERR-2024-001-12345"
}
```

---

## 🔍 **Testing & Validation**

### **Test Endpoints**
Use these endpoints to validate API functionality:

**Health Check**:
```
GET /health
Response: {"status": "healthy", "timestamp": "2024-01-15T10:30:00Z"}
```

**API Version**:
```
GET /api/version
Response: {"version": "1.0.0", "build": "2024.01.15"}
```

### **Test Data**
Use these test values for development:

**Valid Transaction IDs**:
- M-Pesa: **********, NE87654321, NF11223344
- Tigo Pesa: TP87654321, TP11223344
- Airtel Money: AM99887766, AM55443322

**Test Amounts**:
- Minimum: 1000 (TZS 1,000)
- Maximum: 10000000 (TZS 10,000,000)
- Invalid: 500, 15000000

---

**© 2024 Exlipa Payment Solutions Ltd. All rights reserved.**
