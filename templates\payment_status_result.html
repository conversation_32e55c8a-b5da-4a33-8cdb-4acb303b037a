{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Malipo Hali - EXLIPA
{%- else -%}
    Payment Status - EXLIPA
{%- endif -%}
{%- endblock -%}

{% block content %}
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
}

body {
    background: var(--primary-gradient);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.result-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.result-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-soft);
    padding: 2rem;
    transition: all 0.3s ease;
}

.status-header {
    text-align: center;
    margin-bottom: 2rem;
}

.status-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.status-confirmed { background: linear-gradient(135deg, #10b981, #059669); }
.status-pending { background: linear-gradient(135deg, #f59e0b, #d97706); }
.status-rejected { background: linear-gradient(135deg, #ef4444, #dc2626); }
.status-processed { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }

.status-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.status-subtitle {
    color: #64748b;
    margin-bottom: 0;
}

.payment-details {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #374151;
}

.detail-value {
    color: #1f2937;
    font-weight: 500;
}

.action-button {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    width: 100%;
    justify-content: center;
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
    color: white;
}

.secondary-button {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    color: #374151;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
    justify-content: center;
    margin-top: 1rem;
}

.secondary-button:hover {
    border-color: #cbd5e1;
    background: #f1f5f9;
    color: #374151;
}

.alert-box {
    padding: 1rem;
    border-radius: 12px;
    margin: 1rem 0;
    font-size: 0.95rem;
}

.alert-info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.back-link:hover {
    color: #764ba2;
    transform: translateX(-2px);
}

@media (max-width: 576px) {
    .result-container { padding: 1rem 0.5rem; }
    .result-card { padding: 1.5rem; }
    .detail-row { flex-direction: column; align-items: flex-start; gap: 0.25rem; }
}
</style>

<div class="result-container">
    <!-- Back Link -->
    <a href="{{ url_for('check_payment_status') }}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        <span>
            {% if session.language == 'sw' %}
                Angalia Malipo Mengine
            {% else %}
                Check Another Payment
            {% endif %}
        </span>
    </a>

    <!-- Payment Status Card -->
    <div class="result-card">
        <div class="status-header">
            <div class="status-icon status-{{ payment.status.lower() }}">
                {% if payment.status == 'Confirmed' %}
                    <i class="fas fa-check"></i>
                {% elif payment.status == 'Pending' %}
                    <i class="fas fa-clock"></i>
                {% elif payment.status == 'Rejected' %}
                    <i class="fas fa-times"></i>
                {% elif payment.status == 'Processed' %}
                    <i class="fas fa-check-double"></i>
                {% endif %}
            </div>
            
            <h1 class="status-title">
                {% if payment.status == 'Confirmed' %}
                    {% if session.language == 'sw' %}
                        Malipo Yamethibitishwa
                    {% else %}
                        Payment Confirmed
                    {% endif %}
                {% elif payment.status == 'Pending' %}
                    {% if session.language == 'sw' %}
                        Malipo Yanasubiri Ukaguzi
                    {% else %}
                        Payment Pending Review
                    {% endif %}
                {% elif payment.status == 'Rejected' %}
                    {% if session.language == 'sw' %}
                        Malipo Yamekataliwa
                    {% else %}
                        Payment Rejected
                    {% endif %}
                {% elif payment.status == 'Processed' %}
                    {% if session.language == 'sw' %}
                        Malipo Yamechakatwa
                    {% else %}
                        Payment Processed
                    {% endif %}
                {% endif %}
            </h1>
            
            <p class="status-subtitle">
                {% if payment.status == 'Confirmed' %}
                    Your payment has been verified and approved
                {% elif payment.status == 'Pending' %}
                    Your payment is being reviewed by our team
                {% elif payment.status == 'Rejected' %}
                    Your payment could not be verified
                {% elif payment.status == 'Processed' %}
                    Your payment has been fully processed
                {% endif %}
            </p>
        </div>

        <!-- Payment Details -->
        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">
                    {% if session.language == 'sw' %}
                        Kitambulisho cha Kumbukumbu
                    {% else %}
                        Reference ID
                    {% endif %}
                </span>
                <span class="detail-value">{{ reference_id }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">{{ t('Customer Name') }}</span>
                <span class="detail-value">{{ payment.customer_name }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">{{ t('Amount') }}</span>
                <span class="detail-value">TZS {{ "{:,.2f}".format(payment.amount) }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">{{ t('Payment Method') }}</span>
                <span class="detail-value">{{ payment.mobile_operator }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">{{ t('Transaction ID') }}</span>
                <span class="detail-value">{{ payment.transaction_id }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Submitted</span>
                <span class="detail-value">{{ payment.submitted_at.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            {% if payment.processed_at %}
            <div class="detail-row">
                <span class="detail-label">Processed</span>
                <span class="detail-value">{{ payment.processed_at.strftime('%B %d, %Y at %I:%M %p') }}</span>
            </div>
            {% endif %}
        </div>

        <!-- Status-specific Actions and Messages -->
        {% if payment.status == 'Confirmed' %}
            {% if register_url %}
                <div class="alert-box alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Great news!</strong> Your payment has been confirmed. You can now complete your registration.
                </div>
                
                <a href="{{ register_url }}" class="action-button">
                    <i class="fas fa-user-plus"></i>
                    <span>Complete Registration</span>
                </a>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Registration link is valid for 24 hours
                    </small>
                </div>
            {% else %}
                <div class="alert-box alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Your payment has been confirmed. If you haven't received your registration link, please contact support.
                </div>
            {% endif %}
            
        {% elif payment.status == 'Pending' %}
            <div class="alert-box alert-warning">
                <i class="fas fa-clock me-2"></i>
                <strong>Please wait.</strong> Our team is reviewing your payment. You'll receive an email once it's approved.
            </div>
            
        {% elif payment.status == 'Rejected' %}
            <div class="alert-box alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Payment Issue.</strong> 
                {% if payment.rejection_reason %}
                    {{ payment.rejection_reason }}
                {% else %}
                    Your payment could not be verified. Please contact support for assistance.
                {% endif %}
            </div>
            
        {% elif payment.status == 'Processed' %}
            <div class="alert-box alert-success">
                <i class="fas fa-check-double me-2"></i>
                <strong>All done!</strong> Your payment has been fully processed and your account is active.
            </div>
        {% endif %}

        <!-- Additional Actions -->
        <a href="{{ url_for('index') }}" class="secondary-button">
            <i class="fas fa-home"></i>
            <span>Back to Home</span>
        </a>

        <!-- Support Contact -->
        <div class="text-center mt-4">
            <small class="text-muted">
                <i class="fas fa-question-circle me-1"></i>
                Need help? Contact us at <strong><EMAIL></strong> or <strong>+255 123 456 789</strong>
            </small>
        </div>
    </div>
</div>

{% endblock %}
