# reset_admin_password.py
# Usage: python reset_admin_password.py
# This script resets the password for the 'admin' user to 'admin123' and ensures the user is active.

from werkzeug.security import generate_password_hash
from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.orm import sessionmaker

DB_PATH = 'var/app-instance/payment_system.db'
USERNAME = 'admin'
NEW_PASSWORD = 'admin123'

engine = create_engine(f'sqlite:///{DB_PATH}')
metadata = MetaData()
metadata.reflect(bind=engine)
users = Table('user', metadata, autoload_with=engine)
Session = sessionmaker(bind=engine)
session = Session()

# Find the admin user
conn = engine.connect()
select_stmt = users.select().where(users.c.username == USERNAME)
user = conn.execute(select_stmt).fetchone()

if user:
    new_hash = generate_password_hash(NEW_PASSWORD)
    update_stmt = users.update().where(users.c.username == USERNAME).values(password_hash=new_hash, is_active=True)
    conn.execute(update_stmt)
    print(f"Password for user '{USERNAME}' has been reset and account is now active.")
else:
    print(f"User '{USERNAME}' not found.")

conn.close()
