#!/usr/bin/env python3
"""
Test critical fixes implementation
Simple test without pytest dependency
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_authentication_system():
    """Test the new authentication system"""
    print("🧪 Testing Authentication System...")
    
    try:
        from services.auth_service import AuthenticationManager, require_super_admin
        from utils.validators import InputValidator, ValidationError
        
        # Test role permissions
        assert AuthenticationManager.has_permission('super_admin', 'payments') == True
        assert AuthenticationManager.has_permission('company_user', 'company_management') == False
        print("✅ Role permissions working correctly")
        
        # Test dashboard routing
        dashboard = AuthenticationManager.get_dashboard_for_role('super_admin')
        assert dashboard == 'master_admin_dashboard'
        print("✅ Dashboard routing working correctly")
        
        # Test input validation
        try:
            email = InputValidator.validate_email('<EMAIL>')
            assert email == '<EMAIL>'
            print("✅ Email validation working correctly")
        except ValidationError:
            print("❌ Email validation failed")
            return False
        
        try:
            InputValidator.validate_email('invalid-email')
            print("❌ Email validation should have failed")
            return False
        except ValidationError:
            print("✅ Email validation correctly rejects invalid emails")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication system test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_fee_calculator():
    """Test the fee calculator service"""
    print("\n🧪 Testing Fee Calculator...")
    
    try:
        from services.fee_calculator import FeeCalculator
        
        # Test basic fee calculation
        result = FeeCalculator.calculate_transaction_fee(5000.0)
        
        assert result['gross_amount'] == 5000.0
        assert result['base_fee'] == 125.0  # 2.5% of 5000
        assert result['total_fee'] == 125.0
        assert result['net_amount'] == 4875.0
        print("✅ Basic fee calculation working correctly")
        
        # Test minimum fee
        result = FeeCalculator.calculate_transaction_fee(1000.0)
        assert result['base_fee'] == 100.0  # Minimum fee
        print("✅ Minimum fee calculation working correctly")
        
        # Test maximum fee
        result = FeeCalculator.calculate_transaction_fee(1000000.0)
        assert result['base_fee'] == 5000.0  # Maximum fee
        print("✅ Maximum fee calculation working correctly")
        
        # Test operator fees
        result = FeeCalculator.calculate_transaction_fee(5000.0, mobile_operator='CRDB Lipa')
        assert result['operator_fee'] == 75.0  # 0.5% + 50 fixed
        print("✅ Operator fee calculation working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Fee calculator test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_payment_verifier():
    """Test the payment verification service"""
    print("\n🧪 Testing Payment Verifier...")
    
    try:
        from services.payment_verifier import PaymentValidator, MobileMoneyVerifier
        
        verifier = MobileMoneyVerifier()
        
        # Test transaction ID validation
        assert verifier.validate_transaction_id_format('AB12345678', 'M-Pesa') == True
        assert verifier.validate_transaction_id_format('123', 'M-Pesa') == False
        print("✅ Transaction ID validation working correctly")
        
        # Test payment validation
        validator = PaymentValidator()
        
        valid_payment = {
            'customer_name': 'John Doe',
            'mobile_money_sender_name': 'JOHN DOE',
            'amount': '5000.00',
            'mobile_operator': 'M-Pesa',
            'transaction_id': 'AB12345678'
        }
        
        is_valid, errors = validator.validate_payment_submission(valid_payment)
        assert is_valid == True
        assert len(errors) == 0
        print("✅ Payment validation working correctly")
        
        # Test invalid payment
        invalid_payment = {
            'customer_name': '',
            'amount': '500',  # Below minimum
            'mobile_operator': 'Invalid Operator'
        }
        
        is_valid, errors = validator.validate_payment_submission(invalid_payment)
        assert is_valid == False
        assert len(errors) > 0
        print("✅ Payment validation correctly rejects invalid data")
        
        return True
        
    except Exception as e:
        print(f"❌ Payment verifier test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handler():
    """Test the error handling service"""
    print("\n🧪 Testing Error Handler...")
    
    try:
        from utils.error_handler import ErrorHandler, ErrorCategory, ErrorSeverity
        
        # Test error logging
        test_error = Exception("Test error")
        error_id = ErrorHandler.log_error(
            test_error,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.LOW
        )
        
        assert error_id.startswith('ERR_')
        print("✅ Error logging working correctly")
        
        # Test validation error handling
        validation_error = ErrorHandler.handle_validation_error(
            Exception("Invalid email"), 
            field_name='email'
        )
        
        assert validation_error['error_type'] == 'validation_error'
        assert validation_error['field'] == 'email'
        print("✅ Validation error handling working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handler test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test the configuration management"""
    print("\n🧪 Testing Configuration Management...")
    
    try:
        from config_enhanced import get_config, FeatureFlags, BusinessConfig
        
        # Test configuration loading
        dev_config = get_config('development')
        assert dev_config.__name__ == 'DevelopmentConfig'
        print("✅ Configuration loading working correctly")
        
        # Test feature flags
        flags = FeatureFlags.get_all_flags()
        assert isinstance(flags, dict)
        print("✅ Feature flags working correctly")
        
        # Test business config
        payment_limits = BusinessConfig.PAYMENT_LIMITS
        assert payment_limits['min_amount'] == 1000
        assert payment_limits['max_amount'] == 10000000
        print("✅ Business configuration working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_migration():
    """Test that database migration completed successfully"""
    print("\n🧪 Testing Database Migration...")
    
    try:
        # Check if migration backup exists
        import glob
        backup_files = glob.glob('payment_system_backup_*.db')
        
        if backup_files:
            print("✅ Database backup created during migration")
        else:
            print("⚠️  No backup files found (migration may not have run)")
        
        # Test that we can import models without errors
        from models.user import User
        from models.payment import PaymentConfirmation
        from models.company import ClientCompany
        
        print("✅ Model imports working correctly after migration")
        
        return True
        
    except Exception as e:
        print(f"❌ Database migration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical tests"""
    print("🚨 TESTING CRITICAL FIXES IMPLEMENTATION")
    print("=" * 60)
    
    tests = [
        ("Authentication System", test_authentication_system),
        ("Fee Calculator", test_fee_calculator),
        ("Payment Verifier", test_payment_verifier),
        ("Error Handler", test_error_handler),
        ("Configuration Management", test_configuration),
        ("Database Migration", test_database_migration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 CRITICAL FIXES TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} critical tests passed")
    
    if passed == total:
        print("\n🎉 ALL CRITICAL FIXES WORKING CORRECTLY!")
        print("\n✅ ACHIEVEMENTS:")
        print("   • Authentication system fixed and secured")
        print("   • Input validation system implemented")
        print("   • Fee calculation standardized")
        print("   • Payment verification enhanced")
        print("   • Error handling centralized")
        print("   • Configuration management improved")
        print("   • Database integrity fixed")
        print("   • Modular architecture implemented")
        
        print("\n🚀 NEXT STEPS:")
        print("   • Deploy to staging environment")
        print("   • Run comprehensive integration tests")
        print("   • Perform security audit")
        print("   • Update documentation")
        
        return True
    else:
        print(f"\n❌ {total - passed} critical test(s) failed")
        print("\n🔧 IMMEDIATE ACTIONS REQUIRED:")
        print("   • Review failed test output above")
        print("   • Fix critical issues before deployment")
        print("   • Re-run tests to verify fixes")
        
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
