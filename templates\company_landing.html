{% extends "base.html" %}

{% block title %}Landing Page Settings - EXLIPA{% endblock %}

{% block content %}
<style>
.glass-card {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 20px !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.preview-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 1.5rem;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.preview-card:hover {
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.form-control, .form-select {
    border-radius: 12px !important;
    border: 2px solid #e2e8f0 !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.btn {
    border-radius: 12px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn:hover {
    transform: translateY(-1px);
}

.color-preview {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.badge {
    border-radius: 8px !important;
    padding: 0.5rem 0.75rem !important;
    font-weight: 600 !important;
}

.alert {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1) !important;
}

.form-check-input:checked {
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

.input-group .btn {
    border-radius: 0 12px 12px 0 !important;
}

.input-group .form-control {
    border-radius: 12px 0 0 12px !important;
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1"><i class="fas fa-globe me-2 text-primary"></i>Public Landing Page</h2>
                    <p class="text-muted mb-0">Customize your public payment page for customers</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/company/{{ company.id }}" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>Preview Page
                    </a>
                    <a href="{{ url_for('company_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Share Section -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="glass-card p-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <h6 class="mb-0">
                                <i class="fas fa-share-alt me-2 text-primary"></i>Share Payment Page
                            </h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="toggleSharePanel()" id="shareToggleBtn">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>

                        <!-- Collapsible Share Panel -->
                        <div id="sharePanel" style="display: none;" class="mt-3">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control form-control-sm" id="paymentPageUrl"
                                       value="{{ url_for('public_company_landing', company_id=company.id, _external=True) }}"
                                       readonly style="font-size: 0.8rem;">
                                <button class="btn btn-outline-primary btn-sm" type="button" onclick="copyPaymentUrl()" title="{{ t('Copy') }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="d-flex gap-1">
                                <button class="btn btn-outline-success btn-sm" onclick="shareWhatsAppSimple()" title="WhatsApp">
                                    <i class="fab fa-whatsapp"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="shareEmailSimple()" title="{{ t('Email') }}">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="previewPaymentPage()" title="{{ t('Preview') }}">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Landing Page Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="glass-card p-4" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(20px); border-radius: 20px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 8px 32px rgba(0,0,0,0.1);"
                        <form method="POST" id="landingPageForm">
                            <!-- Page Status -->
                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_public_page_enabled" 
                                           name="is_public_page_enabled" {{ 'checked' if company.is_public_page_enabled else '' }}>
                                    <label class="form-check-label fw-bold" for="is_public_page_enabled">
                                        Enable Public Landing Page
                                    </label>
                                </div>
                                <small class="text-muted">When enabled, customers can access your payment page at: 
                                    <code>{{ request.host_url }}company/{{ company.id }}</code>
                                </small>
                            </div>

                            <hr>

                            <!-- Page Content -->
                            <h5 class="fw-bold mb-3"><i class="fas fa-edit me-2 text-info"></i>Page Content</h5>
                            
                            <div class="mb-3">
                                <label for="landing_page_title" class="form-label">Page Title</label>
                                <input type="text" class="form-control" id="landing_page_title" name="landing_page_title" 
                                       value="{{ company.landing_page_title or company.company_name + ' - Pay Online' }}" 
                                       placeholder="Your Company - Pay Online">
                                <small class="text-muted">This appears in the browser tab and search results</small>
                            </div>

                            <div class="mb-3">
                                <label for="landing_page_description" class="form-label">Page Description</label>
                                <textarea class="form-control" id="landing_page_description" name="landing_page_description" 
                                          rows="3" placeholder="Brief description of your business or services">{{ company.landing_page_description or '' }}</textarea>
                                <small class="text-muted">Optional description shown below your company name</small>
                            </div>

                            <div class="mb-4">
                                <label for="custom_message" class="form-label">Custom Message</label>
                                <textarea class="form-control" id="custom_message" name="custom_message" 
                                          rows="3" placeholder="Special instructions, promotions, or important information for customers">{{ company.custom_message or '' }}</textarea>
                                <small class="text-muted">This message will be highlighted in a blue box on your landing page</small>
                            </div>

                            <!-- Current Settings Display -->
                            <hr>
                            <h5 class="fw-bold mb-3"><i class="fas fa-palette me-2 text-warning"></i>Current Branding</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Primary Color</label>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="color-preview" style="width: 30px; height: 30px; background-color: {{ company.primary_color or '#007bff' }}; border-radius: 4px; border: 1px solid #ddd;"></div>
                                            <span>{{ company.primary_color or '#007bff' }}</span>
                                        </div>
                                        <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Secondary Color</label>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="color-preview" style="width: 30px; height: 30px; background-color: {{ company.secondary_color or '#6c757d' }}; border-radius: 4px; border: 1px solid #ddd;"></div>
                                            <span>{{ company.secondary_color or '#6c757d' }}</span>
                                        </div>
                                        <small class="text-muted">Edit in <a href="/company-profile">Profile Settings</a></small>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Methods Display -->
                            <hr>
                            <h5 class="fw-bold mb-3"><i class="fas fa-credit-card me-2 text-success"></i>{{ t('Payment Methods') }}</h5>
                            <div class="row">
                                {% if company.mpesa_till %}
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <span class="badge bg-success me-2">{{ t('M-Pesa') }}</span>
                                        <strong>{{ company.mpesa_till }}</strong>
                                    </div>
                                </div>
                                {% endif %}
                                {% if company.tigo_paybill %}
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <span class="badge bg-primary me-2">{{ t('Tigo Pesa') }}</span>
                                        <strong>{{ company.tigo_paybill }}</strong>
                                    </div>
                                </div>
                                {% endif %}
                                {% if company.airtel_merchant %}
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <span class="badge bg-danger me-2">{{ t('Airtel Money') }}</span>
                                        <strong>{{ company.airtel_merchant }}</strong>
                                    </div>
                                </div>
                                {% endif %}
                                {% if company.crdb_merchant %}
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <span class="badge bg-info me-2">CRDB Bank</span>
                                        <strong>{{ company.crdb_merchant }}</strong>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% if not (company.mpesa_till or company.tigo_paybill or company.airtel_merchant or company.crdb_merchant) %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No payment methods configured. 
                                <a href="/company-profile">Add payment methods</a> to enable customer payments.
                            </div>
                            {% endif %}

                            <!-- Submit Button -->
                            <div class="d-flex justify-content-end gap-2 mt-4">
                                <a href="{{ url_for('company_dashboard') }}" class="btn btn-outline-secondary">{{ t('Cancel') }}</a>
                                <button type="submit" class="btn btn-success" id="saveButton">
                                    <i class="fas fa-save me-1"></i>Save Landing Page
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Landing page loaded');

    const form = document.getElementById('landingPageForm');
    const saveButton = document.getElementById('saveButton');

    console.log('Form found:', !!form);
    console.log('Save button found:', !!saveButton);

    if (form && saveButton) {
        console.log('Form setup successful');

        form.addEventListener('submit', function(e) {
            console.log('Form submitted!');
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
            saveButton.disabled = true;
        });

        saveButton.addEventListener('click', function(e) {
            console.log('Save button clicked!');
        });
    } else {
        console.error('Form or button not found!');
        console.log('Available forms:', document.querySelectorAll('form').length);
        console.log('Available buttons:', document.querySelectorAll('button').length);
    }
});
</script>

                <!-- Preview Panel -->
                <div class="col-lg-4">
                    <div class="glass-card p-4">
                        <h5 class="fw-bold mb-3"><i class="fas fa-eye me-2 text-info"></i>Live Preview</h5>

                        <div class="preview-card mb-3">
                            <div class="text-center mb-3">
                                {% if company.logo_filename %}
                                    <div class="mb-2">
                                        <i class="fas fa-image text-muted"></i> Logo Uploaded
                                    </div>
                                {% endif %}
                                <h5 class="fw-bold" style="color: {{ company.primary_color or '#007bff' }}">
                                    {{ company.company_name }}
                                </h5>
                                {% if company.company_website %}
                                    <small class="text-muted">{{ company.company_website }}</small>
                                {% endif %}
                                {% if company.landing_page_description %}
                                    <p class="small text-muted mt-2">{{ company.landing_page_description }}</p>
                                {% endif %}
                            </div>
                            
                            {% if company.custom_message %}
                            <div class="alert alert-info py-2 px-3 small mb-3">
                                {{ company.custom_message }}
                            </div>
                            {% endif %}
                            
                            <div class="card card-body small">
                                <h6 class="mb-2">{{ t('Payment Methods') }}</h6>
                                {% if company.mpesa_till or company.tigo_paybill or company.airtel_merchant or company.crdb_merchant %}
                                    <ul class="list-unstyled mb-0 small">
                                        {% if company.mpesa_till %}<li>M-Pesa: {{ company.mpesa_till }}</li>{% endif %}
                                        {% if company.tigo_paybill %}<li>Tigo Pesa: {{ company.tigo_paybill }}</li>{% endif %}
                                        {% if company.airtel_merchant %}<li>Airtel: {{ company.airtel_merchant }}</li>{% endif %}
                                        {% if company.crdb_merchant %}<li>CRDB: {{ company.crdb_merchant }}</li>{% endif %}
                                    </ul>
                                {% else %}
                                    <small class="text-muted">No payment methods configured</small>
                                {% endif %}
                            </div>
                            
                            <div class="text-center mt-3">
                                <button class="btn btn-success btn-sm" disabled>Confirm Payment</button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Public URL</label>
                            <div class="input-group">
                                <input type="text" class="form-control form-control-sm" 
                                       value="{{ request.host_url }}company/{{ company.id }}" readonly>
                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyUrl()">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted">Share this URL with customers for payments</small>
                        </div>

                        <div class="d-grid">
                            <a href="/company/{{ company.id }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>Open in New Tab
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy URL functionality
function copyUrl() {
    const urlInput = document.querySelector('input[readonly]');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999);
    navigator.clipboard.writeText(urlInput.value);
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.replace('btn-outline-secondary', 'btn-success');
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.classList.replace('btn-success', 'btn-outline-secondary');
    }, 2000);
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Share Panel Functions
function toggleSharePanel() {
    const panel = document.getElementById('sharePanel');
    const btn = document.getElementById('shareToggleBtn');

    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    } else {
        panel.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-chevron-down"></i>';
    }
}

function copyPaymentUrl() {
    const urlInput = document.getElementById('paymentPageUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showSimpleToast('✅ URL copied to clipboard!', 'success');
    } catch (err) {
        navigator.clipboard.writeText(urlInput.value).then(() => {
            showSimpleToast('✅ URL copied to clipboard!', 'success');
        }).catch(() => {
            showSimpleToast('❌ Failed to copy URL', 'error');
        });
    }
}

function shareWhatsAppSimple() {
    const url = document.getElementById('paymentPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const message = `Make payments to ${companyName} securely: ${url}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareEmailSimple() {
    const url = document.getElementById('paymentPageUrl').value;
    const companyName = '{{ company.company_name }}';
    const subject = `Payment Page - ${companyName}`;
    const body = `Hi,\n\nYou can make payments to ${companyName} using this secure payment page:\n\n${url}\n\nThank you!`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = emailUrl;
}

function previewPaymentPage() {
    const url = document.getElementById('paymentPageUrl').value;
    window.open(url, '_blank');
}

function showSimpleToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInSimple 0.3s ease-out;
    `;
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="me-2">${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// Add CSS for simple toast animation
const simpleStyle = document.createElement('style');
simpleStyle.textContent = `
    @keyframes slideInSimple {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(simpleStyle);
</script>
{% endblock %}
