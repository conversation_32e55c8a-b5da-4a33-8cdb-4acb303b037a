{% extends "base.html" %}

{% block title %}{{ t('User {%- if session.language == "sw" -%}<PERSON><PERSON><PERSON><PERSON>{%- else -%}Admin{%- endif -%} Dashboard') }} - EXLIPA{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="fw-bold mb-1 elegant-gradient-text">{{ t('{%- if session.language == "sw" -%}Karibu{%- else -%}Welcome{%- endif -%}') }}, {{ current_user.full_name or current_user.username }}</h2>
            <div class="text-muted mb-2">{{ t('Role') }}: <strong>{{ t('User Administrator') }}</strong></div>
            <span class="badge bg-primary">{{ t('Administrative Access') }}</span>
        </div>
    </div>

    <!-- Upgrade Prompt Banner -->
    {% if usage_status and usage_status.should_show_upgrade %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <i class="fas fa-arrow-up fa-2x text-primary me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">Ready to upgrade?</h6>
                        <p class="mb-0">
                            {% if usage_status.tier_name == 'Free' %}
                                You're running low on free transactions. Upgrade to {{ usage_status.next_tier }} for unlimited transactions and lower fees!
                            {% else %}
                                Your usage suggests you could benefit from {{ usage_status.next_tier }} tier features.
                            {% endif %}
                        </p>
                    </div>
                    <a href="{{ url_for('pricing') }}" class="btn btn-primary">
                        <i class="fas fa-crown me-1"></i>Upgrade Now
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Usage Tracking Section -->
    {% if usage_status %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    Current Plan: {{ usage_status.tier_name }}
                </h5>

                {% if usage_status.tier_name == 'Free' %}
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Free Transactions Used</span>
                            <span class="fw-bold">{{ 100 - usage_status.free_transactions_remaining }}/100</span>
                        </div>
                        <div class="progress mb-2" style="height: 8px;">
                            {% set usage_percent = ((100 - usage_status.free_transactions_remaining) / 100 * 100) %}
                            <div class="progress-bar {% if usage_percent > 80 %}bg-danger{% elif usage_percent > 60 %}bg-warning{% else %}bg-success{% endif %}"
                                 style="width: {{ usage_percent }}%"></div>
                        </div>
                        <small class="text-muted">{{ usage_status.free_transactions_remaining }} free transactions remaining</small>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="h4 mb-1">{{ usage_status.transaction_fee_percentage }}%</div>
                            <div class="small text-muted">Transaction fee after free limit</div>
                            {% if usage_status.free_transactions_remaining <= 10 %}
                            <div class="mt-2">
                                <a href="{{ url_for('pricing') }}" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-arrow-up me-1"></i>Upgrade Plan
                                </a>
                                <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-chart-line me-1"></i>View Analytics
                                </a>
                            </div>
                            {% else %}
                            <div class="mt-2">
                                <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-chart-line me-1"></i>View Analytics
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="h4 mb-1 text-success">{{ usage_status.transaction_fee_percentage }}%</div>
                            <div class="small text-muted">Transaction fee</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="h4 mb-1 text-primary">{{ usage_status.transactions_used }}</div>
                            <div class="small text-muted">Transactions this month</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="h4 mb-1 text-info">Unlimited</div>
                            <div class="small text-muted">Monthly limit</div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row g-3 mb-4">
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-file-invoice fa-2x text-primary mb-2"></i>
                <h4 class="mb-0">{{ total_invoices }}</h4>
                <div class="small text-muted">{{ t('{%- if session.language == "sw" -%}Ankara{%- else -%}Invoices{%- endif -%}') }}</div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-credit-card fa-2x text-success mb-2"></i>
                <h4 class="mb-0">{{ confirmed_count }}</h4>
                <div class="small text-muted">{{ t('{%- if session.language == "sw" -%}Malipo{%- else -%}Payments{%- endif -%}') }}</div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-users fa-2x text-info mb-2"></i>
                <h4 class="mb-0">{{ team_members|length if team_members else 1 }}</h4>
                <div class="small text-muted">{{ t('{%- if session.language == "sw" -%}Timu{%- else -%}Team{%- endif -%}') }}</div>
            </div>
        </div>
        <div class="col-6 col-md-3">
            <div class="glass-card text-center p-3 h-100">
                <i class="fas fa-cash-register fa-2x text-warning mb-2"></i>
                <h4 class="mb-0">POS</h4>
                <div class="small text-muted">
                  {% if user_company and user_company.dynamic_pos_enabled %}
                    <span class="text-success">{{ t('{%- if session.language == "sw" -%}Hai{%- else -%}Active{%- endif -%}') }}</span>
                  {% elif user_company and user_company.dynamic_pos_payment_pending %}
                    <span class="text-warning">{{ t('{%- if session.language == "sw" -%}Inasubiri{%- else -%}Pending{%- endif -%}') }}</span>
                  {% else %}
                    <span class="text-danger">{{ t('{%- if session.language == "sw" -%}Imefungwa{%- else -%}Locked{%- endif -%}') }}</span>
                  {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-8">
            <div class="glass-card p-4 h-100">
                <h5 class="fw-bold mb-3"><i class="fas fa-bullhorn me-2 text-primary"></i>Quick Actions</h5>
                <div class="d-flex flex-wrap gap-2">
                    <!-- Core {%- if session.language == "sw" -%}Vipengele{%- else -%}Features{%- endif -%} - Available to all tiers -->
                    <a href="{{ url_for('user_invoices') }}" class="btn btn-outline-primary flex-fill"><i class="fas fa-file-invoice me-1"></i>{{ t('My Invoices') }}</a>
                    <a href="{{ url_for('user_payments') }}" class="btn btn-outline-success flex-fill"><i class="fas fa-credit-card me-1"></i>{{ t('My Payments') }}</a>
                    <a href="{{ url_for('company_profile') }}" class="btn btn-outline-warning flex-fill"><i class="fas fa-mobile-alt me-1"></i>{{ t('Payment Methods') }}</a>
                    <a href="{{ url_for('company_profile') }}" class="btn btn-outline-secondary flex-fill"><i class="fas fa-edit me-1"></i>{{ t('Edit Profile') }}</a>

                    <!-- Basic Landing Page - Available to all tiers -->
                    {% if user_company %}
                        <a href="{{ url_for('advanced_landing', company_id=user_company.id) }}" class="btn btn-outline-primary flex-fill"><i class="fas fa-globe me-1"></i>{{ t('Landing Page') }}</a>
                    {% else %}
                        <a href="/company-landing" class="btn btn-outline-primary flex-fill"><i class="fas fa-globe me-1"></i>{{ t('Landing Page') }}</a>
                    {% endif %}

                    <!-- Business+ {%- if session.language == "sw" -%}Vipengele{%- else -%}Features{%- endif -%} -->
                    {% if user_company and user_company.pricing_tier and user_company.pricing_tier.name in ['Business', 'Enterprise'] %}
                        <a href="{{ url_for('team_management') }}" class="btn btn-outline-info flex-fill"><i class="fas fa-users me-1"></i>Manage Team</a>
                        <a href="{{ url_for('api_management') }}" class="btn btn-outline-dark flex-fill"><i class="fas fa-key me-1"></i>API Access</a>
                    {% else %}
                        <button class="btn btn-outline-info flex-fill" disabled title="Available in Business tier"><i class="fas fa-users me-1"></i>Team (Business+)</button>
                        <button class="btn btn-outline-dark flex-fill" disabled title="Available in Business tier"><i class="fas fa-key me-1"></i>API (Business+)</button>
                    {% endif %}

                    <!-- Enterprise {%- if session.language == "sw" -%}Vipengele{%- else -%}Features{%- endif -%} -->
                    {% if user_company and user_company.pricing_tier and user_company.pricing_tier.name == 'Enterprise' %}
                        <a href="{{ url_for('webhook_management') }}" class="btn btn-outline-info flex-fill"><i class="fas fa-webhook me-1"></i>Webhooks</a>
                        <a href="{{ url_for('bulk_operations') }}" class="btn btn-outline-success flex-fill"><i class="fas fa-tasks me-1"></i>Bulk Ops</a>
                    {% else %}
                        <button class="btn btn-outline-info flex-fill" disabled title="Available in Enterprise tier"><i class="fas fa-webhook me-1"></i>Webhooks (Enterprise)</button>
                        <button class="btn btn-outline-success flex-fill" disabled title="Available in Enterprise tier"><i class="fas fa-tasks me-1"></i>Bulk Ops (Enterprise)</button>
                    {% endif %}

                    <!-- POS Feature -->
                    {% if user_company and user_company.dynamic_pos_enabled %}
                        <a href="{{ url_for('pos_dashboard') }}" class="btn btn-warning flex-fill"><i class="fas fa-cash-register me-1"></i>Launch POS</a>
                    {% else %}
                        <a href="{{ url_for('request_pos') }}" class="btn btn-outline-warning flex-fill"><i class="fas fa-cash-register me-1"></i>Request POS</a>
                    {% endif %}
                </div>
                {% if user_company and not user_company.dynamic_pos_enabled %}
                  <div class="alert alert-warning mt-3 mb-0">
                    {% if user_company.dynamic_pos_payment_pending %}
                      <i class="fas fa-hourglass-half me-1"></i> Your POS activation is pending payment confirmation. Please wait for admin approval.
                    {% else %}
                      <i class="fas fa-lock me-1"></i> POS feature is locked. <strong>To unlock, please request and pay for POS below.</strong>
                      <a href="{{ url_for('request_pos') }}" class="btn btn-sm btn-primary ms-2">Request POS Unlock</a>
                    {% endif %}
                  </div>
                {% endif %}
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="glass-card p-4 h-100">
                <h6 class="fw-bold mb-2"><i class="fas fa-info-circle me-2 text-info"></i>Company Info</h6>
                {% if user_company %}
                <div class="mb-1"><strong>Company:</strong> {{ user_company.company_name }}</div>
                <div class="mb-1"><strong>Email:</strong> {{ user_company.company_email }}</div>
                <div class="mb-1"><strong>Phone:</strong> {{ user_company.company_phone or 'N/A' }}</div>
                <div class="mb-1"><strong>Pricing Tier:</strong> {{ user_company.pricing_tier.name if user_company.pricing_tier else 'N/A' }}</div>
                {% else %}
                <div class="mb-1"><strong>Username:</strong> {{ current_user.username }}</div>
                <div class="mb-1"><strong>Role:</strong> {{ current_user.role }}</div>
                <div class="mb-1"><strong>Access Level:</strong> {%- if session.language == "sw" -%}Mtumiaji{%- else -%}User{%- endif -%} Administrative</div>
                <div class="mb-1"><strong>Company:</strong> Not Associated</div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="row g-3">
        <div class="col-12">
            <div class="glass-card p-4">
                <h6 class="fw-bold mb-2"><i class="fas fa-star me-2 text-warning"></i>{{ t('{%- if session.language == "sw" -%}Vipengele{%- else -%}Features{%- endif -%}') }}</h6>
                <ul class="list-inline mb-0">
                    <li class="list-inline-item"><span class="badge bg-primary"><i class="fas fa-file-invoice me-1"></i>Billing & Invoicing</span></li>
                    <li class="list-inline-item"><span class="badge bg-success"><i class="fas fa-credit-card me-1"></i>Payment Tracking</span></li>
                    <li class="list-inline-item"><span class="badge bg-info"><i class="fas fa-users me-1"></i>Team Management</span></li>
                    <li class="list-inline-item"><span class="badge bg-warning text-dark"><i class="fas fa-cash-register me-1"></i>POS {% if user_company and user_company.dynamic_pos_enabled %}Active{% elif user_company and user_company.dynamic_pos_payment_pending %}Pending{% else %}Locked{% endif %}</span></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<style>
    .glass-card { background: rgba(255,255,255,0.85); box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10); border-radius: 1.2rem; border: 1px solid rgba(255,255,255,0.18); transition: box-shadow 0.3s; }
    .glass-card:hover { box-shadow: 0 12px 36px 0 rgba(31,38,135,0.18); }
    .elegant-gradient-text { background: linear-gradient(90deg, #764ba2 0%, #667eea 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
    @media (max-width: 767px) {
        .glass-card { padding: 1.2rem !important; }
        h2 { font-size: 1.5rem; }
        h4 { font-size: 1.1rem; }
        .btn, .badge { font-size: 0.95rem; }
    }
</style>
{% endblock %}
