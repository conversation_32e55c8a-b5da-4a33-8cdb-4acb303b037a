#!/usr/bin/env python3
"""
Payment Verification Service for EXLIPA
Handles mobile money payment verification and validation
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PaymentStatus(Enum):
    """Payment verification status"""
    PENDING = "pending"
    VERIFIED = "verified"
    FAILED = "failed"
    DUPLICATE = "duplicate"
    INVALID = "invalid"

@dataclass
class PaymentVerificationResult:
    """Result of payment verification"""
    status: PaymentStatus
    verified: bool
    message: str
    transaction_details: Optional[Dict] = None
    confidence_score: float = 0.0

class MobileMoneyVerifier:
    """Mobile money payment verification"""
    
    # Transaction ID patterns for different operators
    TRANSACTION_PATTERNS = {
        'M-Pesa': r'^[A-Z]{2}\d{8}$',  # Example: **********
        'Tigo Pesa': r'^TP\d{8,10}$',  # Example: **********
        'Airtel Money': r'^AM\d{8,10}$',  # Example: AM12345678
        'CRDB Lipa': r'^CR\d{8,10}$',  # Example: CR12345678
        'Halo Pesa': r'^HP\d{8,10}$'   # Example: HP12345678
    }
    
    # SMS patterns for extracting transaction details
    SMS_PATTERNS = {
        'M-Pesa': {
            'amount': r'Tsh\s*([\d,]+\.?\d*)',
            'sender': r'from\s+([A-Z\s]+)',
            'reference': r'Reference:\s*([A-Z0-9]+)',
            'time': r'on\s+(\d{1,2}/\d{1,2}/\d{4})\s+at\s+(\d{1,2}:\d{2})'
        },
        'Tigo Pesa': {
            'amount': r'TSh\s*([\d,]+\.?\d*)',
            'sender': r'from\s+([A-Z\s]+)',
            'reference': r'Ref:\s*([A-Z0-9]+)',
            'time': r'(\d{1,2}/\d{1,2}/\d{4})\s+(\d{1,2}:\d{2})'
        }
    }
    
    def __init__(self):
        self.verified_transactions = set()  # Cache for verified transactions
    
    def validate_transaction_id_format(self, transaction_id: str, operator: str) -> bool:
        """Validate transaction ID format for specific operator"""
        try:
            if not transaction_id or not operator:
                return False
            
            pattern = self.TRANSACTION_PATTERNS.get(operator)
            if not pattern:
                # Generic validation for unknown operators
                return len(transaction_id) >= 8 and transaction_id.isalnum()
            
            return bool(re.match(pattern, transaction_id.upper()))
            
        except Exception as e:
            logger.error(f"Transaction ID validation error: {str(e)}")
            return False
    
    def parse_sms_transaction(self, sms_text: str, operator: str) -> Dict:
        """Parse transaction details from SMS text"""
        try:
            patterns = self.SMS_PATTERNS.get(operator, {})
            details = {}
            
            # Extract amount
            amount_match = re.search(patterns.get('amount', ''), sms_text, re.IGNORECASE)
            if amount_match:
                amount_str = amount_match.group(1).replace(',', '')
                details['amount'] = float(amount_str)
            
            # Extract sender name
            sender_match = re.search(patterns.get('sender', ''), sms_text, re.IGNORECASE)
            if sender_match:
                details['sender_name'] = sender_match.group(1).strip()
            
            # Extract reference
            ref_match = re.search(patterns.get('reference', ''), sms_text, re.IGNORECASE)
            if ref_match:
                details['reference'] = ref_match.group(1).strip()
            
            # Extract time
            time_match = re.search(patterns.get('time', ''), sms_text, re.IGNORECASE)
            if time_match:
                details['transaction_time'] = time_match.groups()
            
            return details
            
        except Exception as e:
            logger.error(f"SMS parsing error: {str(e)}")
            return {}
    
    def verify_transaction_amount(self, claimed_amount: float, sms_amount: float, tolerance: float = 0.01) -> bool:
        """Verify transaction amount matches SMS"""
        try:
            if not claimed_amount or not sms_amount:
                return False
            
            # Allow small tolerance for rounding differences
            difference = abs(claimed_amount - sms_amount)
            return difference <= tolerance
            
        except Exception as e:
            logger.error(f"Amount verification error: {str(e)}")
            return False
    
    def verify_sender_name(self, claimed_name: str, sms_name: str, similarity_threshold: float = 0.8) -> bool:
        """Verify sender name similarity"""
        try:
            if not claimed_name or not sms_name:
                return False
            
            # Normalize names
            claimed = claimed_name.upper().strip()
            sms = sms_name.upper().strip()
            
            # Exact match
            if claimed == sms:
                return True
            
            # Simple similarity check (can be enhanced with fuzzy matching)
            # Check if one name contains the other
            if claimed in sms or sms in claimed:
                return True
            
            # Check word overlap
            claimed_words = set(claimed.split())
            sms_words = set(sms.split())
            
            if claimed_words and sms_words:
                overlap = len(claimed_words.intersection(sms_words))
                total_words = len(claimed_words.union(sms_words))
                similarity = overlap / total_words
                return similarity >= similarity_threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Name verification error: {str(e)}")
            return False
    
    def check_duplicate_transaction(self, transaction_id: str, operator: str) -> bool:
        """Check if transaction ID has been used before"""
        try:
            # This would typically check against database
            # For now, check against in-memory cache
            transaction_key = f"{operator}:{transaction_id}"
            
            if transaction_key in self.verified_transactions:
                return True
            
            # TODO: Check against database
            # from app import PaymentConfirmation
            # existing = PaymentConfirmation.query.filter_by(
            #     transaction_id=transaction_id,
            #     mobile_operator=operator
            # ).first()
            # return existing is not None
            
            return False
            
        except Exception as e:
            logger.error(f"Duplicate check error: {str(e)}")
            return False
    
    def verify_payment(self, 
                      transaction_id: str,
                      operator: str,
                      claimed_amount: float,
                      claimed_sender: str,
                      sms_text: str = None) -> PaymentVerificationResult:
        """Comprehensive payment verification"""
        try:
            # 1. Validate transaction ID format
            if not self.validate_transaction_id_format(transaction_id, operator):
                return PaymentVerificationResult(
                    status=PaymentStatus.INVALID,
                    verified=False,
                    message=f"Invalid transaction ID format for {operator}",
                    confidence_score=0.0
                )
            
            # 2. Check for duplicates
            if self.check_duplicate_transaction(transaction_id, operator):
                return PaymentVerificationResult(
                    status=PaymentStatus.DUPLICATE,
                    verified=False,
                    message="Transaction ID has already been used",
                    confidence_score=0.0
                )
            
            # 3. Parse SMS if provided
            sms_details = {}
            if sms_text:
                sms_details = self.parse_sms_transaction(sms_text, operator)
            
            # 4. Verify amount if SMS details available
            amount_verified = True
            if sms_details.get('amount'):
                amount_verified = self.verify_transaction_amount(
                    claimed_amount, sms_details['amount']
                )
            
            # 5. Verify sender name if SMS details available
            name_verified = True
            if sms_details.get('sender_name'):
                name_verified = self.verify_sender_name(
                    claimed_sender, sms_details['sender_name']
                )
            
            # 6. Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                has_sms=bool(sms_text),
                amount_verified=amount_verified,
                name_verified=name_verified,
                format_valid=True
            )
            
            # 7. Determine verification result
            if confidence_score >= 0.8:
                status = PaymentStatus.VERIFIED
                verified = True
                message = "Payment verification successful"
                
                # Add to verified transactions cache
                self.verified_transactions.add(f"{operator}:{transaction_id}")
                
            elif confidence_score >= 0.5:
                status = PaymentStatus.PENDING
                verified = False
                message = "Payment requires manual verification"
            else:
                status = PaymentStatus.FAILED
                verified = False
                message = "Payment verification failed"
            
            return PaymentVerificationResult(
                status=status,
                verified=verified,
                message=message,
                transaction_details=sms_details,
                confidence_score=confidence_score
            )
            
        except Exception as e:
            logger.error(f"Payment verification error: {str(e)}")
            return PaymentVerificationResult(
                status=PaymentStatus.FAILED,
                verified=False,
                message=f"Verification error: {str(e)}",
                confidence_score=0.0
            )
    
    def _calculate_confidence_score(self, 
                                  has_sms: bool,
                                  amount_verified: bool,
                                  name_verified: bool,
                                  format_valid: bool) -> float:
        """Calculate confidence score for verification"""
        score = 0.0
        
        # Base score for valid format
        if format_valid:
            score += 0.3
        
        # SMS availability
        if has_sms:
            score += 0.2
            
            # Amount verification
            if amount_verified:
                score += 0.3
            
            # Name verification
            if name_verified:
                score += 0.2
        else:
            # Without SMS, we can only do basic validation
            score += 0.1
        
        return min(score, 1.0)

class PaymentValidator:
    """High-level payment validation service"""
    
    def __init__(self):
        self.verifier = MobileMoneyVerifier()
    
    def validate_payment_submission(self, payment_data: Dict) -> Tuple[bool, List[str]]:
        """Validate payment submission data"""
        errors = []
        
        try:
            # Required fields
            required_fields = [
                'customer_name', 'mobile_money_sender_name', 'amount',
                'mobile_operator', 'transaction_id'
            ]
            
            for field in required_fields:
                if not payment_data.get(field):
                    errors.append(f"{field.replace('_', ' ').title()} is required")
            
            if errors:
                return False, errors
            
            # Validate amount
            try:
                amount = float(payment_data['amount'])
                if amount < 1000:
                    errors.append("Minimum payment amount is TZS 1,000")
                if amount > 10000000:
                    errors.append("Maximum payment amount is TZS 10,000,000")
            except (ValueError, TypeError):
                errors.append("Invalid amount format")
            
            # Validate transaction ID format
            if not self.verifier.validate_transaction_id_format(
                payment_data['transaction_id'],
                payment_data['mobile_operator']
            ):
                errors.append("Invalid transaction ID format")
            
            # Check for duplicates
            if self.verifier.check_duplicate_transaction(
                payment_data['transaction_id'],
                payment_data['mobile_operator']
            ):
                errors.append("Transaction ID has already been used")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Payment validation error: {str(e)}")
            return False, [f"Validation error: {str(e)}"]
