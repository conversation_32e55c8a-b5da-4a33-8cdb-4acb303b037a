# Routes package for EXLIPA
from .auth import auth_bp
from .admin import admin_bp
from .payment import payment_bp
from .api import api_bp
from .pos import pos_bp

def register_blueprints(app):
    """Register all blueprints with the Flask app"""
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(payment_bp, url_prefix='/payment')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(pos_bp, url_prefix='/pos')

__all__ = [
    'auth_bp',
    'admin_bp', 
    'payment_bp',
    'api_bp',
    'pos_bp',
    'register_blueprints'
]
