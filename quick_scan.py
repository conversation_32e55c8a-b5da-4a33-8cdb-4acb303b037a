#!/usr/bin/env python3
"""
Quick EXLIPA Codebase Scanner
"""

import os
import re

def quick_scan():
    print("🔍 EXLIPA Quick Codebase Scan")
    print("="*50)
    
    errors = []
    warnings = []
    info = []
    
    # 1. Check main Python files
    python_files = ['app.py']
    for file in python_files:
        if os.path.exists(file):
            print(f"✅ Found: {file}")
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for debug mode
                if 'debug=True' in content:
                    warnings.append(f"⚠️  {file}: Debug mode enabled")
                
                # Check for hardcoded secrets
                if re.search(r'SECRET_KEY\s*=\s*["\'][^"\']{1,20}["\']', content):
                    warnings.append(f"⚠️  {file}: Weak SECRET_KEY detected")
                    
                # Check for print statements
                print_count = len(re.findall(r'\bprint\s*\(', content))
                if print_count > 5:
                    warnings.append(f"⚠️  {file}: {print_count} print statements found")
                    
            except Exception as e:
                errors.append(f"❌ {file}: {str(e)}")
        else:
            errors.append(f"❌ Missing file: {file}")
    
    # 2. Check template files
    template_files = [
        'templates/base.html',
        'templates/index.html', 
        'templates/contact.html',
        'templates/pricing.html'
    ]
    
    for file in template_files:
        if os.path.exists(file):
            print(f"✅ Found: {file}")
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for missing {% endblock %}
                blocks = content.count('{% block')
                endblocks = content.count('{% endblock')
                if blocks != endblocks:
                    errors.append(f"❌ {file}: Mismatched block tags ({blocks} blocks, {endblocks} endblocks)")
                
                # Check for inline styles
                inline_styles = len(re.findall(r'style\s*=\s*"[^"]*"', content))
                if inline_styles > 10:
                    warnings.append(f"⚠️  {file}: {inline_styles} inline styles found")
                    
            except Exception as e:
                errors.append(f"❌ {file}: {str(e)}")
        else:
            warnings.append(f"⚠️  Missing template: {file}")
    
    # 3. Check static files structure
    static_dirs = ['static/css', 'static/js', 'static/img']
    for dir_path in static_dirs:
        if os.path.exists(dir_path):
            print(f"✅ Found: {dir_path}")
        else:
            info.append(f"ℹ️  Missing directory: {dir_path}")
    
    # 4. Check for requirements.txt
    if os.path.exists('requirements.txt'):
        print("✅ Found: requirements.txt")
        with open('requirements.txt', 'r') as f:
            reqs = f.read()
            if 'Flask' not in reqs:
                warnings.append("⚠️  requirements.txt: Flask not listed")
    else:
        warnings.append("⚠️  Missing: requirements.txt")
    
    # 5. Check for common security files
    security_files = ['.gitignore', 'README.md']
    for file in security_files:
        if os.path.exists(file):
            info.append(f"ℹ️  Found: {file}")
        else:
            info.append(f"ℹ️  Missing: {file}")
    
    # Report results
    print("\n" + "="*50)
    print("📊 SCAN RESULTS")
    print("="*50)
    
    if errors:
        print(f"\n❌ ERRORS ({len(errors)}):")
        for error in errors:
            print(error)
    
    if warnings:
        print(f"\n⚠️  WARNINGS ({len(warnings)}):")
        for warning in warnings:
            print(warning)
    
    if info:
        print(f"\nℹ️  INFO ({len(info)}):")
        for item in info:
            print(item)
    
    # Overall assessment
    print("\n" + "="*50)
    print("🎯 ASSESSMENT")
    print("="*50)
    
    if not errors:
        print("✅ No critical errors found!")
    else:
        print(f"❌ {len(errors)} critical errors need fixing")
    
    if len(warnings) <= 5:
        print("✅ Low warning count - good code quality")
    else:
        print(f"⚠️  {len(warnings)} warnings - consider addressing")
    
    print(f"\n📈 OVERALL STATUS:")
    if not errors and len(warnings) <= 10:
        print("🎉 CODEBASE STATUS: EXCELLENT - Ready for production!")
        return True
    elif not errors:
        print("✅ CODEBASE STATUS: GOOD - Minor improvements recommended")
        return True
    else:
        print("❌ CODEBASE STATUS: NEEDS ATTENTION - Fix errors before deployment")
        return False

if __name__ == '__main__':
    success = quick_scan()
    exit(0 if success else 1)
