{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}<PERSON><PERSON><PERSON><PERSON> - EXLIPA{% else %}Free Signup - EXLIPA{% endif %}
{% endblock %}

{% block head %}
<style>
/* Ensure visibility with high contrast */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
    color: #ffffff !important;
}

.container-fluid {
    background: transparent !important;
}

.glass-card {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
    color: #ffffff !important;
}

.text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.form-control {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #333 !important;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
}

h1, h2, h3, h4, h5, h6 {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.gradient-text-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.alert-info {
    background: rgba(59, 130, 246, 0.2) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
    color: #ffffff !important;
}

.alert-success {
    background: rgba(16, 185, 129, 0.2) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    color: #ffffff !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Progress Indicator -->
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8">
            <div class="glass-card p-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0 gradient-text">
                        {% if session.language == 'sw' %}Jisajili Bure{% else %}Free Registration{% endif %}
                    </h6>
                    <span class="badge bg-success">
                        {% if session.language == 'sw' %}Bure 100%{% else %}100% Free{% endif %}
                    </span>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-success" style="width: 100%"></div>
                </div>
                <div class="text-center mt-2">
                    <small class="text-success fw-bold">
                        {% if session.language == 'sw' %}Hakuna malipo yanayohitajika!{% else %}No payment required!{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Free Plan Summary -->
            <div class="glass-card p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="fw-bold gradient-text-success mb-2">
                            🎉 {% if session.language == 'sw' %}Mpango wa Bure{% else %}Free Plan{% endif %}
                        </h4>
                        <p class="text-muted mb-0">
                            {% if session.language == 'sw' %}
                                Mzuri kwa biashara ndogo zinazoanza - miamala 100 ya bure kila mwezi
                            {% else %}
                                Perfect for small businesses getting started - 100 free transactions monthly
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="h2 text-success mb-0">
                            {% if session.language == 'sw' %}BURE{% else %}FREE{% endif %}
                        </div>
                        <small class="text-success fw-bold">
                            {% if session.language == 'sw' %}Hakuna malipo{% else %}No payment required{% endif %}
                        </small>
                    </div>
                </div>
                
                <!-- Free Plan Features -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold text-success">
                            {% if session.language == 'sw' %}Unachopata{% else %}What You Get{% endif %}:
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {% if session.language == 'sw' %}Miamala 100 ya bure kila mwezi{% else %}100 free transactions monthly{% endif %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {% if session.language == 'sw' %}Malipo ya M-Pesa, Tigo Pesa, Airtel Money{% else %}M-Pesa, Tigo Pesa, Airtel Money payments{% endif %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {% if session.language == 'sw' %}Dashibodi ya msingi{% else %}Basic dashboard{% endif %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {% if session.language == 'sw' %}Ripoti za msingi{% else %}Basic reports{% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>
                                {% if session.language == 'sw' %}Hakuna ada za kuanzisha!{% else %}No setup fees!{% endif %}
                            </strong><br>
                            <small>
                                {% if session.language == 'sw' %}
                                    Anza kutumia mara moja baada ya kujisajili
                                {% else %}
                                    Start using immediately after registration
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="glass-card p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-2x text-success mb-3"></i>
                    <h3 class="fw-bold gradient-text-success">
                        {% if session.language == 'sw' %}Jisajili Sasa{% else %}Register Now{% endif %}
                    </h3>
                    <p class="text-muted">
                        {% if session.language == 'sw' %}
                            Jaza taarifa zako ili kuunda akaunti yako ya bure
                        {% else %}
                            Fill in your details to create your free account
                        {% endif %}
                    </p>
                </div>

                <form method="POST" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="full_name" class="form-label fw-bold">
                                <i class="fas fa-user me-2 text-success"></i>
                                {% if session.language == 'sw' %}Jina Lako Kamili{% else %}Your Full Name{% endif %} *
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required
                                   placeholder="{% if session.language == 'sw' %}Jina la kwanza na la mwisho{% else %}First and last name{% endif %}">
                            <div class="invalid-feedback">
                                {% if session.language == 'sw' %}Tafadhali ingiza jina lako{% else %}Please enter your name{% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="email" class="form-label fw-bold">
                                <i class="fas fa-envelope me-2 text-success"></i>
                                {% if session.language == 'sw' %}Barua Pepe{% else %}Email Address{% endif %} *
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required
                                   placeholder="{% if session.language == 'sw' %}<EMAIL>{% else %}<EMAIL>{% endif %}">
                            <div class="form-text">
                                {% if session.language == 'sw' %}
                                    Tutakutumia taarifa za kuingia kwenye akaunti yako
                                {% else %}
                                    We'll send your login credentials to this email
                                {% endif %}
                            </div>
                            <div class="invalid-feedback">
                                {% if session.language == 'sw' %}Tafadhali ingiza barua pepe sahihi{% else %}Please enter a valid email address{% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="company_name" class="form-label fw-bold">
                                <i class="fas fa-building me-2 text-success"></i>
                                {% if session.language == 'sw' %}Jina la Kampuni{% else %}Company Name{% endif %} *
                            </label>
                            <input type="text" class="form-control" id="company_name" name="company_name" required
                                   placeholder="{% if session.language == 'sw' %}Jina la biashara yako{% else %}Your business name{% endif %}">
                            <div class="invalid-feedback">
                                {% if session.language == 'sw' %}Tafadhali ingiza jina la kampuni{% else %}Please enter company name{% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="phone" class="form-label fw-bold">
                                <i class="fas fa-phone me-2 text-success"></i>
                                {% if session.language == 'sw' %}Nambari ya Simu{% else %}Phone Number{% endif %}
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   placeholder="+255...">
                            <div class="form-text">
                                {% if session.language == 'sw' %}Si lazima{% else %}Optional{% endif %}
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <label for="business_type" class="form-label fw-bold">
                                <i class="fas fa-store me-2 text-success"></i>
                                {% if session.language == 'sw' %}Aina ya Biashara{% else %}Business Type{% endif %}
                            </label>
                            <select class="form-control" id="business_type" name="business_type">
                                <option value="">
                                    {% if session.language == 'sw' %}Chagua aina ya biashara{% else %}Select business type{% endif %}
                                </option>
                                <option value="retail">
                                    {% if session.language == 'sw' %}Rejareja{% else %}Retail{% endif %}
                                </option>
                                <option value="restaurant">
                                    {% if session.language == 'sw' %}Mgahawa{% else %}Restaurant{% endif %}
                                </option>
                                <option value="services">
                                    {% if session.language == 'sw' %}Huduma{% else %}Services{% endif %}
                                </option>
                                <option value="ecommerce">
                                    {% if session.language == 'sw' %}Biashara ya mtandaoni{% else %}E-commerce{% endif %}
                                </option>
                                <option value="education">
                                    {% if session.language == 'sw' %}Elimu{% else %}Education{% endif %}
                                </option>
                                <option value="healthcare">
                                    {% if session.language == 'sw' %}Afya{% else %}Healthcare{% endif %}
                                </option>
                                <option value="other">
                                    {% if session.language == 'sw' %}Nyingine{% else %}Other{% endif %}
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    {% if session.language == 'sw' %}
                                        Ninakubali <a href="#" class="text-primary">Masharti ya Matumizi</a> na <a href="#" class="text-primary">Sera ya Faragha</a>
                                    {% else %}
                                        I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                    {% endif %}
                                </label>
                                <div class="invalid-feedback">
                                    {% if session.language == 'sw' %}Tafadhali kubali masharti{% else %}Please accept the terms{% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-success btn-lg px-5">
                                <i class="fas fa-rocket me-2"></i>
                                {% if session.language == 'sw' %}Unda Akaunti Yangu ya Bure{% else %}Create My Free Account{% endif %}
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <p class="text-muted">
                        {% if session.language == 'sw' %}
                            Una akaunti tayari? <a href="{{ url_for('user_admin_login') }}" class="text-primary">Ingia hapa</a>
                        {% else %}
                            Already have an account? <a href="{{ url_for('user_admin_login') }}" class="text-primary">Login here</a>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
