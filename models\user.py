#!/usr/bin/env python3
"""
User model for EXLIPA
"""

from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from .base import db, BaseModel, ValidationMixin
from utils.validators import InputValidator, ValidationError

class User(UserMixin, BaseModel, ValidationMixin):
    """User model with role-based access"""
    
    __tablename__ = 'user'
    
    # Basic user information
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    
    # Role and permissions
    role = db.Column(db.String(20), nullable=False)  # super_admin, user_admin, company_user
    is_active = db.Column(db.Bo<PERSON>, default=True)
    
    # Localization
    preferred_language = db.Column(db.String(5), default='en')  # en, sw
    
    # Security and audit
    last_login = db.Column(db.DateTime)
    failed_login_attempts = db.Column(db.Integer, default=0)
    account_locked_until = db.Column(db.DateTime)
    
    # Audit trail
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relationships
    created_users = db.relationship('User', backref=db.backref('creator', remote_side='User.id'))
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def validate(self):
        """Validate user data"""
        # Validate username
        if not self.username:
            raise ValidationError("Username is required")
        
        self.username = InputValidator.validate_username(self.username)
        
        # Validate email
        if not self.email:
            raise ValidationError("Email is required")
        
        self.email = InputValidator.validate_email(self.email)
        
        # Validate role
        if not self.role:
            raise ValidationError("Role is required")
        
        self.role = InputValidator.validate_role(self.role)
        
        # Validate full name
        if not self.full_name:
            raise ValidationError("Full name is required")
        
        self.full_name = InputValidator.sanitize_string(self.full_name, 100)
    
    def set_password(self, password):
        """Set user password with validation"""
        validated_password = InputValidator.validate_password(password)
        self.password_hash = generate_password_hash(validated_password)
    
    def check_password(self, password):
        """Check if password is correct"""
        return check_password_hash(self.password_hash, password)
    
    def is_account_locked(self):
        """Check if account is locked due to failed login attempts"""
        if self.account_locked_until:
            if datetime.utcnow() < self.account_locked_until:
                return True
            else:
                # Lock period expired, unlock account
                self.unlock_account()
                return False
        return False
    
    def increment_failed_login(self):
        """Increment failed login attempts and lock account if necessary"""
        self.failed_login_attempts += 1
        
        # Lock account after 5 failed attempts for 15 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = datetime.utcnow() + timedelta(minutes=15)
        
        db.session.commit()
    
    def unlock_account(self):
        """Unlock account and reset failed login attempts"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        db.session.commit()
    
    def has_role(self, role):
        """Check if user has specific role"""
        return self.role == role
    
    def has_any_role(self, *roles):
        """Check if user has any of the specified roles"""
        return self.role in roles
    
    def is_super_admin(self):
        """Check if user is super admin"""
        return self.role == 'super_admin'
    
    def is_user_admin(self):
        """Check if user is user admin"""
        return self.role == 'user_admin'
    
    def is_company_user(self):
        """Check if user is company user"""
        return self.role == 'company_user'
    
    def get_owned_companies(self):
        """Get companies owned by this user"""
        from .company import ClientCompany
        return ClientCompany.query.filter_by(owner_user_id=self.id).all()
    
    def can_access_company(self, company_id):
        """Check if user can access specific company"""
        if self.is_super_admin():
            return True
        
        if self.is_user_admin():
            # User admin can access companies they own
            from .company import ClientCompany
            company = ClientCompany.query.get(company_id)
            return company and company.owner_user_id == self.id
        
        if self.is_company_user():
            # Company user can access companies they're associated with
            # This would need additional logic for company user associations
            pass
        
        return False
    
    @classmethod
    def get_by_username_or_email(cls, identifier):
        """Get user by username or email"""
        return cls.query.filter(
            (cls.username == identifier) | (cls.email == identifier)
        ).first()
    
    @classmethod
    def get_active_users(cls):
        """Get all active users"""
        return cls.query.filter_by(is_active=True).all()
    
    @classmethod
    def get_by_role(cls, role):
        """Get users by role"""
        return cls.query.filter_by(role=role, is_active=True).all()
    
    @classmethod
    def create_user(cls, username, email, full_name, password, role, created_by_id=None):
        """Create a new user with validation"""
        user = cls(
            username=username,
            email=email,
            full_name=full_name,
            role=role,
            created_by=created_by_id
        )
        
        user.set_password(password)
        user.validate_and_save()
        
        return user
    
    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'is_active': self.is_active,
            'preferred_language': self.preferred_language,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        
        if include_sensitive:
            data.update({
                'failed_login_attempts': self.failed_login_attempts,
                'account_locked_until': self.account_locked_until.isoformat() if self.account_locked_until else None
            })
        
        return data
