{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Unda Ankara - Malipo System
{%- else -%}
    Create Invoice - Payment System
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    {% if session.language == 'sw' %}
                        Unda Ankara Mpya
                    {% else %}
                        Create New Invoice
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="invoiceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    {% if session.language == 'sw' %}
                                        Jina la Mteja *
                                    {% else %}
                                        Customer Name *
                                    {% endif %}
                                </label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                       placeholder="{% if session.language == 'sw' %}Ingiza jina kamili la mteja{% else %}Enter customer full name{% endif %}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    {% if session.language == 'sw' %}
                                        Barua Pepe ya Mteja
                                    {% else %}
                                        Customer Email
                                    {% endif %}
                                </label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email" 
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Customer Phone
                                </label>
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone" 
                                       placeholder="+255 XXX XXX XXX">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>Amount (TZS) *
                                </label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       placeholder="e.g., 25000" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="service_description" class="form-label">
                            <i class="fas fa-clipboard-list me-1"></i>Service/Product Description *
                        </label>
                        <textarea class="form-control" id="service_description" name="service_description" 
                                  rows="3" placeholder="Describe what this invoice is for..." required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="due_days" class="form-label">
                            <i class="fas fa-calendar me-1"></i>Payment Due In (Days)
                        </label>
                        <select class="form-select" id="due_days" name="due_days">
                            <option value="1">1 Day</option>
                            <option value="3">3 Days</option>
                            <option value="7" selected>7 Days (1 Week)</option>
                            <option value="14">14 Days (2 Weeks)</option>
                            <option value="30">30 Days (1 Month)</option>
                        </select>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>Create Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h6 class="text-muted">
                    <i class="fas fa-info-circle me-2"></i>How it works:
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Create invoice with customer details and amount</li>
                    <li><i class="fas fa-share text-info me-2"></i>Share invoice link with customer</li>
                    <li><i class="fas fa-mobile text-warning me-2"></i>Customer makes payment via mobile money</li>
                    <li><i class="fas fa-check-circle text-primary me-2"></i>Customer confirms payment online</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    const amount = document.getElementById('amount').value;
    const description = document.getElementById('service_description').value;
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Please enter a valid amount greater than 0');
        return;
    }
    
    if (description.trim().length < 10) {
        e.preventDefault();
        alert('Please provide a more detailed service description (at least 10 characters)');
        return;
    }
});
</script>
{% endblock %}
