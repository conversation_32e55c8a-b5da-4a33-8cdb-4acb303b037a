#!/usr/bin/env python3
"""
Update EXLIPA templates with translation functions
This script automatically adds t() translation functions to common text in templates
"""

import os
import re
import glob

def update_template_translations():
    """Update all templates with translation functions"""
    
    # Common translations to apply
    translations = {
        # Titles and Headers
        'Dashboard': "{{ t('Dashboard') }}",
        'Admin Dashboard': "{{ t('Admin Dashboard') }}",
        'Master Admin Dashboard': "{{ t('Master Admin Dashboard') }}",
        'User Admin Dashboard': "{{ t('User Admin Dashboard') }}",
        'User Dashboard': "{{ t('User Dashboard') }}",
        'Company Dashboard': "{{ t('Company Dashboard') }}",
        'Analytics': "{{ t('Analytics') }}",
        'Settings': "{{ t('Settings') }}",
        'Payments': "{{ t('Payments') }}",
        'Invoices': "{{ t('Invoices') }}",
        
        # Navigation
        'Home': "{{ t('Home') }}",
        'Login': "{{ t('Login') }}",
        'Logout': "{{ t('Logout') }}",
        'Register': "{{ t('Register') }}",
        'Back': "{{ t('Back') }}",
        'Back to Dashboard': "{{ t('Back to Dashboard') }}",
        'Next': "{{ t('Next') }}",
        'Previous': "{{ t('Previous') }}",
        
        # Actions
        'Save': "{{ t('Save') }}",
        'Cancel': "{{ t('Cancel') }}",
        'Delete': "{{ t('Delete') }}",
        'Edit': "{{ t('Edit') }}",
        'View': "{{ t('View') }}",
        'Create': "{{ t('Create') }}",
        'Update': "{{ t('Update') }}",
        'Search': "{{ t('Search') }}",
        'Filter': "{{ t('Filter') }}",
        'Submit': "{{ t('Submit') }}",
        'Send': "{{ t('Send') }}",
        'Download': "{{ t('Download') }}",
        'Upload': "{{ t('Upload') }}",
        'Copy': "{{ t('Copy') }}",
        'Share': "{{ t('Share') }}",
        'Preview': "{{ t('Preview') }}",
        
        # Forms
        'Name': "{{ t('Name') }}",
        'Email': "{{ t('Email') }}",
        'Phone': "{{ t('Phone') }}",
        'Password': "{{ t('Password') }}",
        'Confirm Password': "{{ t('Confirm Password') }}",
        'Company Name': "{{ t('Company Name') }}",
        'Company Email': "{{ t('Company Email') }}",
        'Company Phone': "{{ t('Company Phone') }}",
        'Amount': "{{ t('Amount') }}",
        'Description': "{{ t('Description') }}",
        'Status': "{{ t('Status') }}",
        'Date': "{{ t('Date') }}",
        'Time': "{{ t('Time') }}",
        
        # Payment System
        'Payment': "{{ t('Payment') }}",
        'Payment Method': "{{ t('Payment Method') }}",
        'Payment Methods': "{{ t('Payment Methods') }}",
        'Payment Confirmation': "{{ t('Payment Confirmation') }}",
        'Payment Status': "{{ t('Payment Status') }}",
        'Pay Now': "{{ t('Pay Now') }}",
        'Make Payment': "{{ t('Make Payment') }}",
        'Check Payment Status': "{{ t('Check Payment Status') }}",
        'M-Pesa': "{{ t('M-Pesa') }}",
        'Tigo Pesa': "{{ t('Tigo Pesa') }}",
        'Airtel Money': "{{ t('Airtel Money') }}",
        'CRDB Lipa': "{{ t('CRDB Lipa') }}",
        'Mobile Money': "{{ t('Mobile Money') }}",
        'Transaction ID': "{{ t('Transaction ID') }}",
        'Reference Number': "{{ t('Reference Number') }}",
        'Customer Name': "{{ t('Customer Name') }}",
        'Customer Email': "{{ t('Customer Email') }}",
        
        # Company & Business
        'Company': "{{ t('Company') }}",
        'Business': "{{ t('Business') }}",
        'Landing Page': "{{ t('Landing Page') }}",
        'Templates': "{{ t('Templates') }}",
        'Basic': "{{ t('Basic') }}",
        'Modern': "{{ t('Modern') }}",
        'Minimal': "{{ t('Minimal') }}",
        'Professional': "{{ t('Professional') }}",
        'Luxury': "{{ t('Luxury') }}",
        
        # Subscription & Pricing
        'Subscription': "{{ t('Subscription') }}",
        'Package': "{{ t('Package') }}",
        'Pricing': "{{ t('Pricing') }}",
        'Plan': "{{ t('Plan') }}",
        'Tier': "{{ t('Tier') }}",
        'Starter': "{{ t('Starter') }}",
        'Enterprise': "{{ t('Enterprise') }}",
        'Free': "{{ t('Free') }}",
        'Features': "{{ t('Features') }}",
        'Upgrade': "{{ t('Upgrade') }}",
        
        # Status
        'Active': "{{ t('Active') }}",
        'Inactive': "{{ t('Inactive') }}",
        'Pending': "{{ t('Pending') }}",
        'Confirmed': "{{ t('Confirmed') }}",
        'Rejected': "{{ t('Rejected') }}",
        'Success': "{{ t('Success') }}",
        'Error': "{{ t('Error') }}",
        'Warning': "{{ t('Warning') }}",
        'Loading': "{{ t('Loading') }}",
        'Processing': "{{ t('Processing') }}",
        
        # Common Messages
        'Welcome': "{{ t('Welcome') }}",
        'Thank you': "{{ t('Thank you') }}",
        'Please wait': "{{ t('Please wait') }}",
        'Are you sure?': "{{ t('Are you sure?') }}",
        'Yes': "{{ t('Yes') }}",
        'No': "{{ t('No') }}",
        'OK': "{{ t('OK') }}",
        
        # Help & Support
        'Help': "{{ t('Help') }}",
        'Support': "{{ t('Support') }}",
        'Contact': "{{ t('Contact') }}",
        'About': "{{ t('About') }}",
        'FAQ': "{{ t('FAQ') }}",
        'Documentation': "{{ t('Documentation') }}",
    }
    
    # Get all HTML templates
    template_files = glob.glob('templates/*.html')
    
    updated_files = []
    
    for template_file in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply translations
            for english_text, translation_code in translations.items():
                # Skip if already translated
                if f"t('{english_text}')" in content or f't("{english_text}")' in content:
                    continue
                
                # Replace in various contexts
                patterns = [
                    # In HTML text content
                    f'>{english_text}<',
                    # In button text
                    f'>{english_text}</button>',
                    f'>{english_text}</a>',
                    # In form labels
                    f'>{english_text}</label>',
                    # In headings
                    f'>{english_text}</h1>',
                    f'>{english_text}</h2>',
                    f'>{english_text}</h3>',
                    f'>{english_text}</h4>',
                    f'>{english_text}</h5>',
                    f'>{english_text}</h6>',
                    # In spans
                    f'>{english_text}</span>',
                    # In divs
                    f'>{english_text}</div>',
                    # In title attributes
                    f'title="{english_text}"',
                    f"title='{english_text}'",
                    # In placeholder attributes
                    f'placeholder="{english_text}"',
                    f"placeholder='{english_text}'",
                ]
                
                for pattern in patterns:
                    if pattern in content:
                        if 'title=' in pattern or 'placeholder=' in pattern:
                            # For attributes, replace differently
                            if 'title=' in pattern:
                                content = content.replace(f'title="{english_text}"', f'title="{translation_code}"')
                                content = content.replace(f"title='{english_text}'", f"title='{translation_code}'")
                            elif 'placeholder=' in pattern:
                                content = content.replace(f'placeholder="{english_text}"', f'placeholder="{translation_code}"')
                                content = content.replace(f"placeholder='{english_text}'", f"placeholder='{translation_code}'")
                        else:
                            # For text content, replace the text between tags
                            replacement = pattern.replace(english_text, translation_code)
                            content = content.replace(pattern, replacement)
            
            # Only write if content changed
            if content != original_content:
                with open(template_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                updated_files.append(template_file)
                print(f"✅ Updated: {template_file}")
            
        except Exception as e:
            print(f"❌ Error updating {template_file}: {e}")
    
    print(f"\n🎉 Translation update complete!")
    print(f"📝 Updated {len(updated_files)} template files")
    print(f"🌍 Templates now support English/Swahili switching")
    
    return updated_files

if __name__ == '__main__':
    print("🌍 EXLIPA Template Translation Updater")
    print("=" * 50)
    updated_files = update_template_translations()
    
    if updated_files:
        print(f"\n📋 Updated files:")
        for file in updated_files:
            print(f"   • {file}")
    else:
        print("\n📋 No files needed updating (already translated)")
    
    print(f"\n🚀 Ready! Switch language at: http://localhost:5000/set_language/sw")
