/* EXLIPA Beautiful Modern Design - Visibility & Accessibility */
/* Stunning color scheme with perfect readability */

/* Global Text Visibility */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Beautiful modern color scheme */
body,
html {
    color: #1e293b !important;
    background-color: #f8fafc !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Beautiful headings */
h1, h2, h3, h4, h5, h6 {
    color: #1e293b !important;
    font-weight: 800 !important;
    letter-spacing: -0.02em !important;
}

/* Modern text hierarchy */
p, span, div, li, td, th {
    color: #475569 !important;
}

/* Strong contrast for important text */
.text-primary {
    color: #1e293b !important;
}

.text-secondary {
    color: #64748b !important;
}

.text-muted {
    color: #9ca3af !important;
}

/* White text overrides */
.text-white,
.text-white *,
.navbar-dark .navbar-nav .nav-link,
.bg-primary *,
.bg-success *,
.bg-warning *,
.bg-danger *,
.bg-info *,
.btn-primary *,
.btn-success *,
.btn-warning *,
.btn-danger *,
.btn-info *,
.badge * {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Card visibility fixes */
.card,
.glass-card {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    background-color: #f8fafc !important;
    border-bottom: 1px solid #e5e7eb !important;
    color: #1e293b !important;
}

.card-body {
    background-color: #ffffff !important;
    color: #1e293b !important;
}

.card-title {
    color: #1e293b !important;
    font-weight: 600 !important;
}

.card-text {
    color: #64748b !important;
}

/* Form visibility fixes */
.form-control,
.form-select,
input,
textarea,
select {
    background-color: #ffffff !important;
    border: 1px solid #d1d5db !important;
    color: #1e293b !important;
}

.form-control:focus,
.form-select:focus,
input:focus,
textarea:focus,
select:focus {
    background-color: #ffffff !important;
    border-color: #2563eb !important;
    color: #1e293b !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

.form-control::placeholder,
input::placeholder,
textarea::placeholder {
    color: #9ca3af !important;
    opacity: 1 !important;
}

.form-label,
label {
    color: #374151 !important;
    font-weight: 500 !important;
}

/* Table visibility fixes */
.table {
    background-color: #ffffff !important;
    color: #1e293b !important;
}

.table th {
    background-color: #f8fafc !important;
    color: #374151 !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #e5e7eb !important;
}

.table td {
    color: #1e293b !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.table-hover tbody tr:hover {
    background-color: #f8fafc !important;
    color: #1e293b !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 250, 252, 0.5) !important;
    color: #1e293b !important;
}

/* Navigation visibility fixes */
.navbar {
    background-color: #ffffff !important;
    border-bottom: 1px solid #e5e7eb !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.navbar-brand {
    color: #2563eb !important;
    font-weight: 700 !important;
}

.navbar-nav .nav-link {
    color: #475569 !important;
    font-weight: 500 !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: #2563eb !important;
    background-color: #f1f5f9 !important;
}

/* Dropdown visibility fixes */
.dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
    color: #475569 !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #f1f5f9 !important;
    color: #2563eb !important;
}

/* Button visibility fixes */
.btn {
    font-weight: 500 !important;
    border-radius: 8px !important;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3) !important;
}

.btn-primary:hover,
.btn-primary:focus {
    background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4) !important;
    transform: translateY(-3px) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3) !important;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 12px 35px rgba(100, 116, 139, 0.4) !important;
    transform: translateY(-3px) !important;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3) !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4) !important;
    transform: translateY(-3px) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3) !important;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4) !important;
    transform: translateY(-3px) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3) !important;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4) !important;
    transform: translateY(-3px) !important;
}

.btn-outline-primary {
    color: #6366f1 !important;
    border: 2px solid #6366f1 !important;
    background-color: transparent !important;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.1) !important;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    border-color: transparent !important;
    color: #ffffff !important;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3) !important;
    transform: translateY(-3px) !important;
}

/* Alert visibility fixes */
.alert {
    border-radius: 8px !important;
    border: 1px solid transparent !important;
    font-weight: 500 !important;
}

.alert-success {
    background-color: #dcfce7 !important;
    border-color: #bbf7d0 !important;
    color: #166534 !important;
}

.alert-warning {
    background-color: #fef3c7 !important;
    border-color: #fde68a !important;
    color: #92400e !important;
}

.alert-danger {
    background-color: #fee2e2 !important;
    border-color: #fecaca !important;
    color: #991b1b !important;
}

.alert-info {
    background-color: #e0f2fe !important;
    border-color: #b3e5fc !important;
    color: #0c4a6e !important;
}

/* Badge visibility fixes */
.badge {
    font-weight: 500 !important;
    padding: 0.5em 0.75em !important;
    border-radius: 6px !important;
}

.badge.bg-primary {
    background-color: #2563eb !important;
    color: #ffffff !important;
}

.badge.bg-success {
    background-color: #059669 !important;
    color: #ffffff !important;
}

.badge.bg-warning {
    background-color: #d97706 !important;
    color: #ffffff !important;
}

.badge.bg-danger {
    background-color: #dc2626 !important;
    color: #ffffff !important;
}

/* Footer visibility fixes */
footer {
    background-color: #ffffff !important;
    border-top: 1px solid #e5e7eb !important;
    color: #64748b !important;
}

/* Sidebar visibility fixes */
.sidebar {
    background-color: #ffffff !important;
    border-right: 1px solid #e5e7eb !important;
}

/* Modal visibility fixes */
.modal-content {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    color: #1e293b !important;
}

.modal-header {
    background-color: #f8fafc !important;
    border-bottom: 1px solid #e5e7eb !important;
    color: #1e293b !important;
}

.modal-title {
    color: #1e293b !important;
    font-weight: 600 !important;
}

.modal-body {
    color: #475569 !important;
}

/* Ensure links are visible */
a {
    color: #2563eb !important;
    text-decoration: none !important;
}

a:hover {
    color: #1d4ed8 !important;
    text-decoration: underline !important;
}

/* Breadcrumb visibility */
.breadcrumb {
    background-color: #f8fafc !important;
    border: 1px solid #e5e7eb !important;
}

.breadcrumb-item {
    color: #64748b !important;
}

.breadcrumb-item.active {
    color: #1e293b !important;
}

/* Pagination visibility */
.page-link {
    color: #2563eb !important;
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

.page-link:hover {
    color: #1d4ed8 !important;
    background-color: #f8fafc !important;
    border-color: #d1d5db !important;
}

.page-item.active .page-link {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}
