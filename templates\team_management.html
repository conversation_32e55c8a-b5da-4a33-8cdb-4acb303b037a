{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Timu Management - Exlipa
{%- else -%}
    Team Management - Exlipa
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        {% if session.language == 'sw' %}
                            Usimamizi wa Timu
                        {% else %}
                            Team Management
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <h5 class="mb-3">
                        {% if session.language == 'sw' %}
                            Timu Yako
                        {% else %}
                            Your Team
                        {% endif %}
                    </h5>
                    {% if team_members %}
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ t('Name') }}</th>
                                <th>{{ t('Email') }}</th>
                                <th>
                                    {% if session.language == 'sw' %}
                                        Jukumu
                                    {% else %}
                                        Role
                                    {% endif %}
                                </th>
                                <th>{{ t('Status') }}</th>
                                <th>
                                    {% if session.language == 'sw' %}
                                        Vitendo
                                    {% else %}
                                        Actions
                                    {% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for member in team_members %}
                            <tr>
                                <td>{{ member.full_name or member.username }}</td>
                                <td>{{ member.email }}</td>
                                <td>{{ member.role }}</td>
                                <td>
                                    <span class="badge {{ 'bg-success' if member.is_active else 'bg-secondary' }}">
                                        {{ 'Active' if member.is_active else 'Disabled' }}
                                    </span>
                                </td>
                                <td>
                                    {% if member.id != current_user.id %}
                                    <form method="POST" action="{{ url_for('remove_team_member', user_id=member.id) }}" style="display:inline;">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Remove this user?')">Remove</button>
                                    </form>
                                    {% else %}
                                    <span class="text-muted">You</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="alert alert-info">No team members found.</div>
                    {% endif %}
                    <hr>
                    <h5>Invite New Team Member</h5>
                    <form method="POST" action="{{ url_for('invite_team_member') }}" class="row g-2">
                        <div class="col-md-8">
                            <input type="email" class="form-control" name="email" placeholder="Email address" required>
                        </div>
                        <div class="col-md-4 d-grid">
                            <button type="submit" class="btn btn-success">Invite</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
