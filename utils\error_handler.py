#!/usr/bin/env python3
"""
Error Handling Utilities for EXLIPA
Centralized error handling and logging
"""

import logging
import traceback
from datetime import datetime
from flask import request, session, current_app
from typing import Dict, Optional, Any
from enum import Enum

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    PAYMENT = "payment"
    DATABASE = "database"
    EXTERNAL_API = "external_api"
    SYSTEM = "system"
    USER_INPUT = "user_input"
    BUSINESS_LOGIC = "business_logic"

class ErrorHandler:
    """Centralized error handling service"""
    
    @staticmethod
    def log_error(error: Exception, 
                  category: ErrorCategory = ErrorCategory.SYSTEM,
                  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                  context: Optional[Dict] = None,
                  user_id: Optional[int] = None) -> str:
        """
        Log error with context information
        
        Returns:
            Error ID for tracking
        """
        try:
            # Generate unique error ID
            error_id = ErrorHandler._generate_error_id()
            
            # Collect context information
            error_context = ErrorHandler._collect_context(context, user_id)
            
            # Create error record
            error_record = {
                'error_id': error_id,
                'timestamp': datetime.utcnow().isoformat(),
                'error_type': type(error).__name__,
                'error_message': str(error),
                'category': category.value,
                'severity': severity.value,
                'context': error_context,
                'traceback': traceback.format_exc()
            }
            
            # Log based on severity
            if severity == ErrorSeverity.CRITICAL:
                logger.critical(f"CRITICAL ERROR [{error_id}]: {error}", extra=error_record)
            elif severity == ErrorSeverity.HIGH:
                logger.error(f"HIGH SEVERITY ERROR [{error_id}]: {error}", extra=error_record)
            elif severity == ErrorSeverity.MEDIUM:
                logger.warning(f"MEDIUM SEVERITY ERROR [{error_id}]: {error}", extra=error_record)
            else:
                logger.info(f"LOW SEVERITY ERROR [{error_id}]: {error}", extra=error_record)
            
            # Store in database for critical errors
            if severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
                ErrorHandler._store_error_in_db(error_record)
            
            return error_id
            
        except Exception as e:
            # Fallback logging if error handler itself fails
            logger.critical(f"Error handler failed: {str(e)}")
            return "ERROR_HANDLER_FAILED"
    
    @staticmethod
    def handle_validation_error(error: Exception, field_name: str = None) -> Dict:
        """Handle validation errors"""
        error_id = ErrorHandler.log_error(
            error,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            context={'field_name': field_name}
        )
        
        return {
            'error_id': error_id,
            'error_type': 'validation_error',
            'message': str(error),
            'field': field_name,
            'user_message': ErrorHandler._get_user_friendly_message(error, ErrorCategory.VALIDATION)
        }
    
    @staticmethod
    def handle_payment_error(error: Exception, 
                           transaction_id: str = None,
                           amount: float = None,
                           operator: str = None) -> Dict:
        """Handle payment-related errors"""
        context = {
            'transaction_id': transaction_id,
            'amount': amount,
            'operator': operator
        }
        
        error_id = ErrorHandler.log_error(
            error,
            category=ErrorCategory.PAYMENT,
            severity=ErrorSeverity.HIGH,
            context=context
        )
        
        return {
            'error_id': error_id,
            'error_type': 'payment_error',
            'message': str(error),
            'context': context,
            'user_message': ErrorHandler._get_user_friendly_message(error, ErrorCategory.PAYMENT)
        }
    
    @staticmethod
    def handle_authentication_error(error: Exception, username: str = None) -> Dict:
        """Handle authentication errors"""
        error_id = ErrorHandler.log_error(
            error,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            context={'username': username}
        )
        
        return {
            'error_id': error_id,
            'error_type': 'authentication_error',
            'message': str(error),
            'user_message': ErrorHandler._get_user_friendly_message(error, ErrorCategory.AUTHENTICATION)
        }
    
    @staticmethod
    def handle_database_error(error: Exception, operation: str = None) -> Dict:
        """Handle database errors"""
        error_id = ErrorHandler.log_error(
            error,
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            context={'operation': operation}
        )
        
        return {
            'error_id': error_id,
            'error_type': 'database_error',
            'message': str(error),
            'user_message': ErrorHandler._get_user_friendly_message(error, ErrorCategory.DATABASE)
        }
    
    @staticmethod
    def handle_api_error(error: Exception, 
                        endpoint: str = None,
                        method: str = None,
                        status_code: int = None) -> Dict:
        """Handle API errors"""
        context = {
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code
        }
        
        error_id = ErrorHandler.log_error(
            error,
            category=ErrorCategory.EXTERNAL_API,
            severity=ErrorSeverity.MEDIUM,
            context=context
        )
        
        return {
            'error_id': error_id,
            'error_type': 'api_error',
            'message': str(error),
            'context': context,
            'user_message': ErrorHandler._get_user_friendly_message(error, ErrorCategory.EXTERNAL_API)
        }
    
    @staticmethod
    def _generate_error_id() -> str:
        """Generate unique error ID"""
        import uuid
        return f"ERR_{datetime.utcnow().strftime('%Y%m%d')}_{str(uuid.uuid4())[:8].upper()}"
    
    @staticmethod
    def _collect_context(additional_context: Optional[Dict] = None, user_id: Optional[int] = None) -> Dict:
        """Collect context information for error logging"""
        context = {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'session_id': session.get('_id') if session else None,
            'user_agent': request.headers.get('User-Agent') if request else None,
            'ip_address': request.remote_addr if request else None,
            'url': request.url if request else None,
            'method': request.method if request else None,
            'referrer': request.referrer if request else None
        }
        
        # Add additional context if provided
        if additional_context:
            context.update(additional_context)
        
        return context
    
    @staticmethod
    def _get_user_friendly_message(error: Exception, category: ErrorCategory) -> str:
        """Get user-friendly error message"""
        error_str = str(error).lower()
        
        # Category-specific messages
        if category == ErrorCategory.VALIDATION:
            if 'email' in error_str:
                return "Please enter a valid email address."
            elif 'password' in error_str:
                return "Password does not meet requirements."
            elif 'phone' in error_str:
                return "Please enter a valid phone number."
            elif 'amount' in error_str:
                return "Please enter a valid amount."
            else:
                return "Please check your input and try again."
        
        elif category == ErrorCategory.PAYMENT:
            if 'transaction' in error_str:
                return "There was an issue processing your payment. Please try again."
            elif 'duplicate' in error_str:
                return "This transaction has already been processed."
            elif 'insufficient' in error_str:
                return "Insufficient funds or invalid payment details."
            else:
                return "Payment processing failed. Please contact support."
        
        elif category == ErrorCategory.AUTHENTICATION:
            if 'password' in error_str:
                return "Invalid username or password."
            elif 'locked' in error_str:
                return "Account is temporarily locked. Please try again later."
            elif 'expired' in error_str:
                return "Session has expired. Please login again."
            else:
                return "Authentication failed. Please try again."
        
        elif category == ErrorCategory.DATABASE:
            return "A system error occurred. Please try again later."
        
        elif category == ErrorCategory.EXTERNAL_API:
            return "External service is temporarily unavailable. Please try again later."
        
        else:
            return "An unexpected error occurred. Please try again or contact support."
    
    @staticmethod
    def _store_error_in_db(error_record: Dict):
        """Store critical errors in database for tracking"""
        try:
            # This would store in a dedicated error_log table
            # For now, we'll just ensure it's logged
            logger.critical(f"STORING CRITICAL ERROR: {error_record['error_id']}")
            
            # TODO: Implement database storage
            # from models.error_log import ErrorLog
            # error_log = ErrorLog(
            #     error_id=error_record['error_id'],
            #     error_type=error_record['error_type'],
            #     message=error_record['error_message'],
            #     category=error_record['category'],
            #     severity=error_record['severity'],
            #     context=json.dumps(error_record['context']),
            #     traceback=error_record['traceback']
            # )
            # error_log.save()
            
        except Exception as e:
            logger.critical(f"Failed to store error in database: {str(e)}")

class ExlipaException(Exception):
    """Base exception class for EXLIPA"""
    
    def __init__(self, message: str, 
                 category: ErrorCategory = ErrorCategory.SYSTEM,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict] = None):
        super().__init__(message)
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.error_id = ErrorHandler.log_error(self, category, severity, context)

class ValidationException(ExlipaException):
    """Validation error exception"""
    
    def __init__(self, message: str, field_name: str = None):
        context = {'field_name': field_name} if field_name else None
        super().__init__(message, ErrorCategory.VALIDATION, ErrorSeverity.LOW, context)

class PaymentException(ExlipaException):
    """Payment error exception"""
    
    def __init__(self, message: str, transaction_id: str = None, amount: float = None):
        context = {
            'transaction_id': transaction_id,
            'amount': amount
        }
        super().__init__(message, ErrorCategory.PAYMENT, ErrorSeverity.HIGH, context)

class AuthenticationException(ExlipaException):
    """Authentication error exception"""
    
    def __init__(self, message: str, username: str = None):
        context = {'username': username} if username else None
        super().__init__(message, ErrorCategory.AUTHENTICATION, ErrorSeverity.MEDIUM, context)

class BusinessLogicException(ExlipaException):
    """Business logic error exception"""
    
    def __init__(self, message: str, operation: str = None):
        context = {'operation': operation} if operation else None
        super().__init__(message, ErrorCategory.BUSINESS_LOGIC, ErrorSeverity.MEDIUM, context)
