#!/usr/bin/env python3
"""
Test runner for EXLIPA
Runs all tests and generates coverage reports
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(command)}")
    print()
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, cwd=project_root)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED (exit code: {result.returncode})")
            return False
            
    except FileNotFoundError:
        print(f"❌ {description} - FAILED (command not found)")
        return False
    except Exception as e:
        print(f"❌ {description} - FAILED (error: {str(e)})")
        return False

def check_dependencies():
    """Check if required testing dependencies are installed"""
    print("🔍 Checking testing dependencies...")
    
    required_packages = ['pytest', 'pytest-cov', 'pytest-mock']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        install_cmd = [sys.executable, '-m', 'pip', 'install'] + missing_packages
        
        if run_command(install_cmd, "Installing missing packages"):
            print("✅ All dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies")
            return False
    else:
        print("✅ All testing dependencies are available")
        return True

def run_unit_tests():
    """Run unit tests"""
    command = [
        sys.executable, '-m', 'pytest',
        'tests/unit/',
        '-v',
        '--tb=short',
        '--durations=10'
    ]
    
    return run_command(command, "Running Unit Tests")

def run_integration_tests():
    """Run integration tests"""
    command = [
        sys.executable, '-m', 'pytest',
        'tests/integration/',
        '-v',
        '--tb=short',
        '--durations=10'
    ]
    
    return run_command(command, "Running Integration Tests")

def run_all_tests():
    """Run all tests"""
    command = [
        sys.executable, '-m', 'pytest',
        'tests/',
        '-v',
        '--tb=short',
        '--durations=10'
    ]
    
    return run_command(command, "Running All Tests")

def run_tests_with_coverage():
    """Run tests with coverage report"""
    command = [
        sys.executable, '-m', 'pytest',
        'tests/',
        '--cov=services',
        '--cov=utils',
        '--cov=models',
        '--cov-report=html',
        '--cov-report=term-missing',
        '--cov-fail-under=80',
        '-v'
    ]
    
    return run_command(command, "Running Tests with Coverage")

def run_specific_test(test_path):
    """Run a specific test file or test function"""
    command = [
        sys.executable, '-m', 'pytest',
        test_path,
        '-v',
        '--tb=short'
    ]
    
    return run_command(command, f"Running Specific Test: {test_path}")

def run_linting():
    """Run code linting"""
    print("\n🔍 Running Code Quality Checks...")
    
    # Check if flake8 is available
    try:
        import flake8
        command = [sys.executable, '-m', 'flake8', 'services/', 'utils/', 'models/', '--max-line-length=120']
        return run_command(command, "Running Flake8 Linting")
    except ImportError:
        print("⚠️  Flake8 not installed, skipping linting")
        return True

def run_type_checking():
    """Run type checking with mypy"""
    print("\n🔍 Running Type Checking...")
    
    # Check if mypy is available
    try:
        import mypy
        command = [sys.executable, '-m', 'mypy', 'services/', 'utils/', 'models/', '--ignore-missing-imports']
        return run_command(command, "Running MyPy Type Checking")
    except ImportError:
        print("⚠️  MyPy not installed, skipping type checking")
        return True

def run_security_checks():
    """Run security checks"""
    print("\n🔒 Running Security Checks...")
    
    # Check if bandit is available
    try:
        import bandit
        command = [sys.executable, '-m', 'bandit', '-r', 'services/', 'utils/', 'models/', '-f', 'json']
        return run_command(command, "Running Bandit Security Checks")
    except ImportError:
        print("⚠️  Bandit not installed, skipping security checks")
        return True

def generate_test_report():
    """Generate comprehensive test report"""
    print("\n📊 Generating Test Report...")
    
    command = [
        sys.executable, '-m', 'pytest',
        'tests/',
        '--html=test_report.html',
        '--self-contained-html',
        '--cov=services',
        '--cov=utils',
        '--cov=models',
        '--cov-report=html:htmlcov',
        '-v'
    ]
    
    success = run_command(command, "Generating Test Report")
    
    if success:
        print("\n📋 Test Report Generated:")
        print("  • HTML Report: test_report.html")
        print("  • Coverage Report: htmlcov/index.html")
    
    return success

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='EXLIPA Test Runner')
    parser.add_argument('--unit', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration', action='store_true', help='Run only integration tests')
    parser.add_argument('--coverage', action='store_true', help='Run tests with coverage')
    parser.add_argument('--lint', action='store_true', help='Run linting checks')
    parser.add_argument('--type-check', action='store_true', help='Run type checking')
    parser.add_argument('--security', action='store_true', help='Run security checks')
    parser.add_argument('--report', action='store_true', help='Generate comprehensive test report')
    parser.add_argument('--all', action='store_true', help='Run all checks and tests')
    parser.add_argument('--test', type=str, help='Run specific test file or function')
    
    args = parser.parse_args()
    
    print("🧪 EXLIPA TEST RUNNER")
    print("=" * 60)
    
    # Check dependencies first
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages.")
        return False
    
    results = []
    
    # Run specific test if requested
    if args.test:
        results.append(('Specific Test', run_specific_test(args.test)))
        
    # Run individual test suites
    elif args.unit:
        results.append(('Unit Tests', run_unit_tests()))
        
    elif args.integration:
        results.append(('Integration Tests', run_integration_tests()))
        
    elif args.coverage:
        results.append(('Tests with Coverage', run_tests_with_coverage()))
        
    elif args.lint:
        results.append(('Linting', run_linting()))
        
    elif args.type_check:
        results.append(('Type Checking', run_type_checking()))
        
    elif args.security:
        results.append(('Security Checks', run_security_checks()))
        
    elif args.report:
        results.append(('Test Report', generate_test_report()))
        
    elif args.all:
        # Run comprehensive test suite
        results.append(('Unit Tests', run_unit_tests()))
        results.append(('Integration Tests', run_integration_tests()))
        results.append(('Code Coverage', run_tests_with_coverage()))
        results.append(('Linting', run_linting()))
        results.append(('Type Checking', run_type_checking()))
        results.append(('Security Checks', run_security_checks()))
        results.append(('Test Report', generate_test_report()))
        
    else:
        # Default: run all tests
        results.append(('All Tests', run_all_tests()))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ ACHIEVEMENTS:")
        print("   • All test suites executed successfully")
        print("   • Code quality checks passed")
        print("   • Security validation completed")
        print("   • Coverage requirements met")
        return True
    else:
        print(f"\n❌ {total - passed} test suite(s) failed")
        print("\n🔧 NEXT STEPS:")
        print("   • Review failed test output above")
        print("   • Fix failing tests and code issues")
        print("   • Re-run tests to verify fixes")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
