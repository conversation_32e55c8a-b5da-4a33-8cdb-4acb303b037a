{% extends "base.html" %}

{% block title %}POS Sales - {{ company.company_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1"><i class="fas fa-chart-line me-2 text-success"></i>Sales Reports</h2>
            <p class="text-muted mb-0">{{ company.company_name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="/pos" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to POS
            </a>
        </div>
    </div>

    <!-- Sales Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-day fa-2x text-primary mb-3"></i>
                    <h4 class="text-primary">TZS {{ "{:,.0f}".format(stats.today_sales) }}</h4>
                    <p class="text-muted mb-0">Today's Sales</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-alt fa-2x text-success mb-3"></i>
                    <h4 class="text-success">TZS {{ "{:,.0f}".format(stats.month_sales) }}</h4>
                    <p class="text-muted mb-0">This Month</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-bar fa-2x text-info mb-3"></i>
                    <h4 class="text-info">TZS {{ "{:,.0f}".format(stats.total_sales) }}</h4>
                    <p class="text-muted mb-0">Total Sales</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales History -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Sales History</h5>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" style="width: auto;">
                    <option>All Time</option>
                    <option>{{ t('{%- if session.language == "sw" -%}Leo{%- else -%}Today{%- endif -%}') }}</option>
                    <option>This Week</option>
                    <option>This Month</option>
                </select>
                <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            {% if sales.items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Sale #</th>
                                <th>Date & Time</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Mteja{%- else -%}Customer{%- endif -%}') }}</th>
                                <th>Items</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%}') }}</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}') }}</th>
                                <th>{{ t('{%- if session.language == "sw" -%}Vitendo{%- else -%}Actions{%- endif -%}') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales.items %}
                            <tr>
                                <td>
                                    <strong>{{ sale.sale_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <div>{{ sale.created_at.strftime('%Y-%m-%d') }}</div>
                                        <small class="text-muted">{{ sale.created_at.strftime('%H:%M:%S') }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if sale.customer_name %}
                                        <div>
                                            <div>{{ sale.customer_name }}</div>
                                            {% if sale.customer_phone %}
                                                <small class="text-muted">{{ sale.customer_phone }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Walk-in Customer</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ sale.sale_items|length }} items</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ sale.payment_method }}</span>
                                    <div>
                                        <small class="text-{% if sale.payment_status == 'Completed' %}success{% elif sale.payment_status == '{%- if session.language == "sw" -%}Inasubiri{%- else -%}Pending{%- endif -%}' %}warning{% else %}danger{% endif %}">
                                            {{ sale.payment_status }}
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <strong class="text-success">TZS {{ "{:,.0f}".format(sale.total_amount) }}</strong>
                                    {% if sale.discount_amount > 0 %}
                                        <br><small class="text-muted">-TZS {{ "{:,.0f}".format(sale.discount_amount) }} discount</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="viewSaleDetails({{ sale.id }})" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="printReceipt({{ sale.id }})" title="Print Receipt">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if sales.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Sales pagination">
                        <ul class="pagination pagination-sm justify-content-center mb-0">
                            {% if sales.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('pos_sales', page=sales.prev_num) }}">{{ t('{%- if session.language == "sw" -%}Iliyotangulia{%- else -%}Previous{%- endif -%}') }}</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in sales.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != sales.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pos_sales', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if sales.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('pos_sales', page=sales.next_num) }}">{{ t('{%- if session.language == "sw" -%}Ifuatayo{%- else -%}Next{%- endif -%}') }}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted mb-3">No Sales Yet</h4>
                    <p class="text-muted mb-4">Start selling products to see sales data here</p>
                    <a href="/pos" class="btn btn-primary btn-lg">
                        <i class="fas fa-cash-register me-2"></i>Go to POS
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Sale Details Modal -->
<div class="modal fade" id="saleDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-receipt me-2"></i>Sale Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="sale-details-content">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-success" onclick="printCurrentSale()">
                    <i class="fas fa-print me-1"></i>Print Receipt
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentSaleId = null;

function viewSaleDetails(saleId) {
    currentSaleId = saleId;
    
    // In a real implementation, you'd fetch sale details via AJAX
    // For now, we'll show a placeholder
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted">Sale Information</h6>
                <p><strong>Sale #:</strong> SALE-2024-${saleId.toString().padStart(6, '0')}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Time:</strong> ${new Date().toLocaleTimeString()}</p>
                <p><strong>Payment:</strong> Cash</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-muted">Customer Information</h6>
                <p><strong>Name:</strong> Walk-in Customer</p>
                <p><strong>Phone:</strong> -</p>
            </div>
        </div>
        <hr>
        <h6 class="text-muted">Items Sold</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>{{ t('{%- if session.language == "sw" -%}Wingi{%- else -%}Quantity{%- endif -%}') }}</th>
                        <th>Unit Price</th>
                        <th>{{ t('{%- if session.language == "sw" -%}Jumla{%- else -%}Total{%- endif -%}') }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Sample Product</td>
                        <td>2</td>
                        <td>TZS 5,000</td>
                        <td>TZS 10,000</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="row mt-3">
            <div class="col-md-6 offset-md-6">
                <div class="d-flex justify-content-between">
                    <span>Subtotal:</span>
                    <span>TZS 10,000</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Tax (18%):</span>
                    <span>TZS 1,800</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <span>Total:</span>
                    <span>TZS 11,800</span>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('sale-details-content').innerHTML = content;
    const modal = new bootstrap.Modal(document.getElementById('saleDetailsModal'));
    modal.show();
}

function printReceipt(saleId) {
    // In a real implementation, this would generate and print a receipt
    alert(`Print receipt for sale ${saleId}`);
}

function printCurrentSale() {
    if (currentSaleId) {
        printReceipt(currentSaleId);
    }
}
</script>
{% endblock %}
