{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}Dashibodi ya Simu{% else %}Mobile Dashboard{% endif %} - EXLIPA
{% endblock %}

{% block head %}
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="theme-color" content="#007bff">

<!-- PWA Manifest -->
<link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">

<!-- Mobile-specific styles -->
<style>
.mobile-dashboard {
    padding: 0;
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.mobile-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    color: white;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.mobile-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    margin: 1rem;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.quick-action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 1rem;
}

.quick-action-btn {
    background: white;
    border: none;
    border-radius: 15px;
    padding: 1.5rem 1rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: #333;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    color: #007bff;
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #007bff;
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 0.5rem;
    text-align: center;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-around;
    padding: 0.5rem 0;
    z-index: 1000;
}

.nav-item {
    text-align: center;
    padding: 0.5rem;
    color: #666;
    text-decoration: none;
    flex: 1;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #007bff;
}

.nav-item i {
    display: block;
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.nav-item span {
    font-size: 0.75rem;
}

/* Swipe gestures */
.swipe-container {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    display: flex;
    gap: 1rem;
    padding: 1rem;
}

.swipe-card {
    min-width: 280px;
    scroll-snap-align: start;
    flex-shrink: 0;
}

/* Pull to refresh */
.pull-to-refresh {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pull-to-refresh.visible {
    opacity: 1;
}

/* Haptic feedback simulation */
.haptic-feedback {
    animation: haptic 0.1s ease-in-out;
}

@keyframes haptic {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .mobile-card {
        background: rgba(30, 30, 30, 0.95);
        color: white;
    }
    
    .quick-action-btn {
        background: #2d2d2d;
        color: white;
    }
}

/* Safe area for notched devices */
.mobile-header {
    padding-top: max(1rem, env(safe-area-inset-top));
}

.bottom-nav {
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
}
</style>
{% endblock %}

{% block content %}
<div class="mobile-dashboard">
    <!-- Pull to Refresh -->
    <div class="pull-to-refresh" id="pullToRefresh">
        <i class="fas fa-arrow-down me-2"></i>
        {% if session.language == 'sw' %}Vuta chini kuonyesha upya{% else %}Pull to refresh{% endif %}
    </div>

    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    {% if session.language == 'sw' %}Karibu{% else %}Welcome{% endif %}, 
                    {{ current_user.username if current_user.is_authenticated else 'User' }}
                </h5>
                <small class="opacity-75">
                    {% if user_company %}{{ user_company.company_name }}{% endif %}
                </small>
            </div>
            <div>
                <button class="btn btn-link text-white p-0" onclick="toggleNotifications()">
                    <i class="fas fa-bell fa-lg"></i>
                    <span class="badge bg-danger position-absolute translate-middle rounded-pill" style="font-size: 0.6rem;">3</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Key Metrics Swipe Cards -->
    <div class="swipe-container">
        {% if usage_status %}
        <div class="metric-card swipe-card">
            <div class="metric-value">{{ usage_status.tier_name }}</div>
            <div class="metric-label">
                {% if session.language == 'sw' %}Mpango Wako{% else %}Your Plan{% endif %}
            </div>
        </div>
        
        {% if usage_status.tier_name == 'Free' %}
        <div class="metric-card swipe-card">
            <div class="metric-value">{{ usage_status.free_transactions_remaining }}</div>
            <div class="metric-label">
                {% if session.language == 'sw' %}Miamala ya Bure{% else %}Free Transactions{% endif %}
            </div>
        </div>
        {% endif %}
        
        <div class="metric-card swipe-card">
            <div class="metric-value">{{ usage_status.transactions_used }}</div>
            <div class="metric-label">
                {% if session.language == 'sw' %}Miamala Mwezi Huu{% else %}This Month{% endif %}
            </div>
        </div>
        
        <div class="metric-card swipe-card">
            <div class="metric-value">{{ usage_status.transaction_fee_percentage }}%</div>
            <div class="metric-label">
                {% if session.language == 'sw' %}Ada ya Muamala{% else %}Transaction Fee{% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions Grid -->
    <div class="quick-action-grid">
        <a href="{{ url_for('admin_payments') }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-credit-card quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}Malipo{% else %}Payments{% endif %}</span>
        </a>
        
        <a href="{{ url_for('analytics_dashboard') }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-chart-line quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}Uchambuzi{% else %}Analytics{% endif %}</span>
        </a>
        
        <a href="{{ url_for('advanced_landing', company_id=user_company.id if user_company else 1) }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-globe quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}Ukurasa{% else %}Landing Page{% endif %}</span>
        </a>
        
        <a href="{{ url_for('api_management') }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-key quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}API{% else %}API Access{% endif %}</span>
        </a>
        
        <a href="{{ url_for('admin_invoices') }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-file-invoice quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}Ankara{% else %}Invoices{% endif %}</span>
        </a>
        
        <a href="{{ url_for('pricing') }}" class="quick-action-btn" onclick="addHapticFeedback(this)">
            <i class="fas fa-arrow-up quick-action-icon"></i>
            <span>{% if session.language == 'sw' %}Boresha{% else %}Upgrade{% endif %}</span>
        </a>
    </div>

    <!-- Recent Activity -->
    <div class="mobile-card">
        <h6 class="fw-bold mb-3">
            <i class="fas fa-clock me-2"></i>
            {% if session.language == 'sw' %}Shughuli za Hivi Karibuni{% else %}Recent Activity{% endif %}
        </h6>
        <div class="list-group list-group-flush">
            <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-bold">Payment Received</div>
                        <small class="text-muted">TSh 25,000 - M-Pesa</small>
                    </div>
                    <small class="text-muted">2 min ago</small>
                </div>
            </div>
            <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-bold">New Customer</div>
                        <small class="text-muted">John Doe registered</small>
                    </div>
                    <small class="text-muted">1 hour ago</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="{{ url_for('mobile_dashboard') }}" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>{% if session.language == 'sw' %}Nyumbani{% else %}Home{% endif %}</span>
        </a>
        <a href="{{ url_for('admin_payments') }}" class="nav-item">
            <i class="fas fa-credit-card"></i>
            <span>{% if session.language == 'sw' %}Malipo{% else %}Payments{% endif %}</span>
        </a>
        <a href="{{ url_for('analytics_dashboard') }}" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>{% if session.language == 'sw' %}Ripoti{% else %}Reports{% endif %}</span>
        </a>
        <a href="{{ url_for('user_admin_dashboard') }}" class="nav-item">
            <i class="fas fa-user"></i>
            <span>{% if session.language == 'sw' %}Profaili{% else %}Profile{% endif %}</span>
        </a>
    </div>

    <!-- Spacer for bottom nav -->
    <div style="height: 80px;"></div>
</div>

<script>
// PWA functionality
let deferredPrompt;
let pullToRefreshDistance = 0;
let startY = 0;

// Add haptic feedback simulation
function addHapticFeedback(element) {
    element.classList.add('haptic-feedback');
    setTimeout(() => {
        element.classList.remove('haptic-feedback');
    }, 100);
    
    // Vibrate if supported
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
}

// Pull to refresh functionality
document.addEventListener('touchstart', function(e) {
    startY = e.touches[0].pageY;
}, { passive: true });

document.addEventListener('touchmove', function(e) {
    const currentY = e.touches[0].pageY;
    const pullDistance = currentY - startY;
    
    if (pullDistance > 0 && window.scrollY === 0) {
        pullToRefreshDistance = Math.min(pullDistance, 100);
        const pullElement = document.getElementById('pullToRefresh');
        
        if (pullToRefreshDistance > 60) {
            pullElement.classList.add('visible');
        } else {
            pullElement.classList.remove('visible');
        }
    }
}, { passive: true });

document.addEventListener('touchend', function(e) {
    if (pullToRefreshDistance > 60) {
        // Trigger refresh
        location.reload();
    }
    pullToRefreshDistance = 0;
    document.getElementById('pullToRefresh').classList.remove('visible');
}, { passive: true });

// Install PWA prompt
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    
    // Show install button
    const installBtn = document.createElement('button');
    installBtn.className = 'btn btn-primary btn-sm position-fixed';
    installBtn.style.cssText = 'top: 10px; right: 10px; z-index: 1001;';
    installBtn.innerHTML = '<i class="fas fa-download me-1"></i>Install App';
    installBtn.onclick = installPWA;
    document.body.appendChild(installBtn);
});

function installPWA() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((result) => {
            deferredPrompt = null;
        });
    }
}

// Notification toggle
function toggleNotifications() {
    addHapticFeedback(event.target);
    // Implement notification logic here
    alert('Notifications feature coming soon!');
}

// Service Worker registration
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/static/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
</script>
{% endblock %}
