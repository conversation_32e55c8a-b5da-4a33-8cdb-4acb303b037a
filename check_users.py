#!/usr/bin/env python3
"""
Check user credentials and authentication
"""

from app import app, db, User
from werkzeug.security import check_password_hash

def check_users():
    """Check all users and their authentication details"""
    with app.app_context():
        print("=== CHECKING USER AUTHENTICATION ===")
        
        users = User.query.all()
        print(f"Total users in database: {len(users)}")
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   Role: {user.role}")
            print(f"   Email: {user.email}")
            print(f"   Active: {user.is_active}")
            print(f"   Password Hash: {user.password_hash[:20]}...")
            
            # Test password
            if user.username == 'admin':
                password_valid = check_password_hash(user.password_hash, 'admin123')
                print(f"   Password 'admin123' valid: {password_valid}")
            elif user.username == 'useradmin':
                password_valid = check_password_hash(user.password_hash, 'useradmin123')
                print(f"   Password 'useradmin123' valid: {password_valid}")
            elif user.username == 'juma':
                password_valid = check_password_hash(user.password_hash, 'juma123')
                print(f"   Password 'juma123' valid: {password_valid}")
            elif user.username == 'test':
                password_valid = check_password_hash(user.password_hash, 'test123')
                print(f"   Password 'test123' valid: {password_valid}")
        
        print("\n=== TESTING SPECIFIC LOGIN QUERIES ===")
        
        # Test master admin login query
        master_admin = User.query.filter_by(username='admin', is_active=True, role='master_admin').first()
        print(f"Master admin query result: {master_admin}")
        
        # Test user admin login query  
        user_admin = User.query.filter_by(username='useradmin', is_active=True, role='user_admin').first()
        print(f"User admin query result: {user_admin}")
        
        # Test company user login query
        company_user = User.query.filter_by(username='juma', is_active=True, role='company_user').first()
        print(f"Company user query result: {company_user}")

if __name__ == '__main__':
    check_users()
