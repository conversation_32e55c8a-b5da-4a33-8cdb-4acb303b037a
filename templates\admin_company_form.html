{% extends "base.html" %}

{% block title %}{{ '{%- if session.language == "sw" -%}<PERSON><PERSON>{%- else -%}Edit{%- endif -%}' if company else '{%- if session.language == "sw" -%}Unda{%- else -%}Create{%- endif -%}' }} {%- if session.language == "sw" -%}Kampuni{%- else -%}Company{%- endif -%} - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-building me-2"></i>
        {{ 'Edit Company' if company else 'Create New Company' }}
    </h1>
    <a href="{{ url_for('admin_companies') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Companies
    </a>
</div>

<form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
    <div class="row">
        <!-- {%- if session.language == "sw" -%}Kampuni{%- else -%}Company{%- endif -%} Information -->
        <div class="col-12 col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Company Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_name" class="form-label">Company {%- if session.language == "sw" -%}Jina{%- else -%}Name{%- endif -%} *</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="{{ company.company_name if company else '' }}" required>
                                <div class="invalid-feedback">Please provide a company name.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_email" class="form-label">{{ t('{%- if session.language == "sw" -%}Barua pepe{%- else -%}Email{%- endif -%}') }}</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" 
                                       value="{{ company.company_email if company else '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_phone" class="form-label">{{ t('{%- if session.language == "sw" -%}Simu{%- else -%}Phone{%- endif -%}') }}</label>
                                <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                       value="{{ company.company_phone if company else '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="company_website" name="company_website" 
                                       value="{{ company.company_website if company else '' }}" 
                                       placeholder="https://www.example.com">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="company_address" class="form-label">Address</label>
                                <textarea class="form-control" id="company_address" name="company_address" 
                                          rows="3">{{ company.company_address if company else '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="company_tin" class="form-label">TIN Number</label>
                                <input type="text" class="form-control" id="company_tin" name="company_tin" 
                                       value="{{ company.company_tin if company else '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- Logo upload field -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_logo" class="form-label">Logo (optional)</label>
                                <input type="file" class="form-control" id="company_logo" name="company_logo" accept="image/*">
                                {% if company and company.logo_filename %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename=company.logo_filename) }}" alt="Company Logo" style="max-height: 60px;">
                                    </div>
                                {% endif %}
                                <div class="form-text">Upload a logo for your company (PNG, JPG, max 1MB).</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} Method Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-mobile-alt me-2"></i>Payment Method Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="mpesa_till" class="form-label">M-Pesa Till</label>
                                <input type="text" class="form-control" id="mpesa_till" name="mpesa_till" 
                                       value="{{ company.mpesa_till if company else '' }}" 
                                       placeholder="123456">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="tigo_paybill" class="form-label">Tigo Paybill</label>
                                <input type="text" class="form-control" id="tigo_paybill" name="tigo_paybill" 
                                       value="{{ company.tigo_paybill if company else '' }}" 
                                       placeholder="654321">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="airtel_merchant" class="form-label">Airtel Merchant</label>
                                <input type="text" class="form-control" id="airtel_merchant" name="airtel_merchant" 
                                       value="{{ company.airtel_merchant if company else '' }}" 
                                       placeholder="789012">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="crdb_merchant" class="form-label">CRDB Merchant</label>
                                <input type="text" class="form-control" id="crdb_merchant" name="crdb_merchant" 
                                       value="{{ company.crdb_merchant if company else '' }}" 
                                       placeholder="890123">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branding Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-palette me-2"></i>Branding Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="primary_color" class="form-label">Primary Color</label>
                                <input type="color" class="form-control form-control-color" id="primary_color" 
                                       name="primary_color" value="{{ company.primary_color if company else '#28a745' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="secondary_color" class="form-label">Secondary Color</label>
                                <input type="color" class="form-control form-control-color" id="secondary_color" 
                                       name="secondary_color" value="{{ company.secondary_color if company else '#007bff' }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="currency_code" class="form-label">Currency</label>
                                <select class="form-select" id="currency_code" name="currency_code">
                                    <option value="TZS" {{ 'selected' if company and company.currency_code == 'TZS' else '' }}>
                                        TZS - Tanzanian Shilling
                                    </option>
                                    <option value="USD" {{ 'selected' if company and company.currency_code == 'USD' else '' }}>
                                        USD - US Dollar
                                    </option>
                                    <option value="KES" {{ 'selected' if company and company.currency_code == 'KES' else '' }}>
                                        KES - Kenyan Shilling
                                    </option>
                                    <option value="UGX" {{ 'selected' if company and company.currency_code == 'UGX' else '' }}>
                                        UGX - Ugandan Shilling
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                               value="{{ company.currency_symbol if company else 'TZS' }}" 
                               placeholder="TZS" maxlength="5">
                        <div class="form-text">Symbol to display with amounts (e.g., TZS, $, KSh)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription & {%- if session.language == "sw" -%}Malipo{%- else -%}Billing{%- endif -%} -->
        <div class="col-12 col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Subscription & Billing
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="pricing_tier_id" class="form-label">Pricing Tier *</label>
                        <select class="form-select" id="pricing_tier_id" name="pricing_tier_id" required>
                            <option value="" >Select a pricing tier...</option>
                            {% for tier in pricing_tiers %}
                            <option value="{{ tier.id }}" 
                                    {{ 'selected' if company and company.pricing_tier_id == tier.id else '' }}>
                                {{ tier.name }} - TZS {{ "{:,}".format(tier.monthly_fee|int) }}/month
                            </option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">Please select a pricing tier.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="billing_cycle" class="form-label">Billing Cycle</label>
                        <select class="form-select" id="billing_cycle" name="billing_cycle">
                            <option value="Monthly" {{ 'selected' if company and company.billing_cycle == 'Monthly' else '' }}>
                                Monthly
                            </option>
                            <option value="Annual" {{ 'selected' if company and company.billing_cycle == 'Annual' else '' }}>
                                Annual (2 months free)
                            </option>
                        </select>
                    </div>
                    
                    {% if company %}
                    <div class="mb-3">
                        <label class="form-label">Subscription Status</label>
                        <div>
                            <span class="badge 
                                {% if company.subscription_status == '{%- if session.language == "sw" -%}Hai{%- else -%}Active{%- endif -%}' %}bg-success
                                {% elif company.subscription_status == 'Suspended' %}bg-warning
                                {% elif company.subscription_status == 'Cancelled' %}bg-danger
                                {% else %}bg-secondary{% endif %} fs-6">
                                {{ company.subscription_status }}
                            </span>
                        </div>
                    </div>
                    
                    {% if company.next_billing_date %}
                    <div class="mb-3">
                        <label class="form-label">Next {%- if session.language == "sw" -%}Malipo{%- else -%}Billing{%- endif -%} Date</label>
                        <div class="text-muted">
                            {{ company.next_billing_date.strftime('%B %d, %Y') }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label class="form-label">Total Transaction Volume</label>
                        <div class="h5 text-success">
                            TZS {{ (company.total_transaction_volume or 0)|round(0)|int }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Pricing Tier Details -->
            <div class="card" id="tier-details" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Tier Details
                    </h6>
                </div>
                <div class="card-body" id="tier-info">
                    <!-- Will be populated via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Dynamic POS Feature -->
    <div class="card mb-4">
        <div class="card-header d-flex align-items-center">
            <i class="fas fa-cash-register me-2 text-primary"></i>
            <h5 class="mb-0">Dynamic POS (Point of Sale)</h5>
            {% if company and company.dynamic_pos_enabled %}
                <span class="badge bg-success ms-2">{{ t('{%- if session.language == "sw" -%}Hai{%- else -%}Active{%- endif -%}') }}</span>
            {% elif company and company.dynamic_pos_payment_pending %}
                <span class="badge bg-warning text-dark ms-2">Awaiting Payment</span>
            {% else %}
                <span class="badge bg-secondary ms-2">{{ t('{%- if session.language == "sw" -%}Imefungwa{%- else -%}Locked{%- endif -%}') }}</span>
            {% endif %}
        </div>
        <div class="card-body">
            {% if company and company.dynamic_pos_enabled %}
                <!-- POS features/actions go here. Visible only if POS is enabled. -->
                <div class="alert alert-success mt-3 mb-0">
                    <i class="fas fa-check-circle me-1"></i>
                    Dynamic POS is active for this company.
                </div>
                <!-- Example POS action button (replace or extend as needed) -->
                <button class="btn btn-primary mt-3" type="button">
                    <i class="fas fa-cash-register me-1"></i> Launch POS
                </button>
            {% else %}
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="dynamic_pos_request" name="dynamic_pos_request"
                        {% if company and (company.dynamic_pos_enabled or company.dynamic_pos_payment_pending) %}checked disabled{% endif %}>
                    <label class="form-check-label" for="dynamic_pos_request">
                        Request Dynamic POS (TZS 50,000 setup fee)
                    </label>
                </div>
                <small class="text-muted">
                    Unlock a modern, mobile-friendly POS system for your business. After requesting, payment instructions will be sent. Feature is activated after payment confirmation.
                </small>
                {% if company and company.dynamic_pos_payment_pending %}
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        {%- if session.language == "sw" -%}Malipo{%- else -%}Payment{%- endif -%} pending. Please complete payment to activate Dynamic POS.
                        <form method="POST" action="{{ url_for('unlock_dynamic_pos', company_id=company.id) }}" class="d-inline ms-2">
                            <button type="submit" class="btn btn-sm btn-outline-success">Unlock POS (Admin)</button>
                        </form>
                    </div>
                {% else %}
                    <div class="alert alert-secondary mt-3 mb-0">
                        <i class="fas fa-lock me-1"></i>
                        Feature is locked. Request to unlock and receive payment instructions.
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
    <!-- End Dynamic POS Feature -->
    <!--
    To show POS features in any template, use this Jinja block:
    {% if company and company.dynamic_pos_enabled %}
        ...POS features/actions...
    {% endif %}
    -->

    <div class="d-flex flex-column flex-md-row justify-content-between gap-2 mt-4">
        <a href="{{ url_for('admin_companies') }}" class="btn btn-outline-secondary w-100 w-md-auto mb-2 mb-md-0">
            <i class="fas fa-times me-2"></i>Cancel
        </a>
        <button type="submit" class="btn btn-success w-100 w-md-auto">
            <i class="fas fa-save me-2"></i>
            {{ 'Update Company' if company else 'Create Company' }}
        </button>
    </div>
</form>

<input type="hidden" id="triggerTierDetailsOnLoad" value="{{ 'true' if company and company.pricing_tier_id else 'false' }}">
<script type="application/json" id="tierDetailsData">
{{ pricing_tiers|tojson }}
</script>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Pricing tier details
const tierDetailsArr = JSON.parse(document.getElementById('tierDetailsData').textContent);
const tierDetails = {};
tierDetailsArr.forEach(function(tier) {
    tierDetails[tier.id] = tier;
});

// Set trigger variable using Jinja
var triggerTierDetailsOnLoad = document.getElementById('triggerTierDetailsOnLoad').value === 'true';

document.addEventListener('DOMContentLoaded', function() {
    if (triggerTierDetailsOnLoad) {
        document.getElementById('pricing_tier_id').dispatchEvent(new Event('change'));
    }
});

document.getElementById('pricing_tier_id').addEventListener('change', function() {
    const tierId = this.value;
    const detailsCard = document.getElementById('tier-details');
    const tierInfo = document.getElementById('tier-info');
    
    if (tierId && tierDetails[tierId]) {
        const tier = tierDetails[tierId];
        const billingCycle = document.getElementById('billing_cycle').value;
        const fee = billingCycle === 'Annual' ? tier.annual_fee : tier.monthly_fee;
        
        tierInfo.innerHTML = `
            <h6>${tier.name}</h6>
            <p class="text-muted small">${tier.description}</p>
            <hr>
            <div class="mb-2">
                <strong>Setup Fee:</strong> TZS ${tier.setup_fee.toLocaleString()}
            </div>
            <div class="mb-2">
                <strong>${billingCycle} Fee:</strong> TZS ${fee.toLocaleString()}
            </div>
            <div class="mb-2">
                <strong>Transaction Fee:</strong> ${tier.transaction_fee_percentage}%
            </div>
            <div class="mb-3">
                <strong>Max Transactions:</strong> ${tier.max_transactions_per_month || 'Unlimited'}/month
            </div>
            <div>
                <strong>Features:</strong>
                <ul class="list-unstyled small mt-1">
                    ${tier.features.custom_branding ? '<li><i class="fas fa-check text-success me-1"></i>Custom Branding</li>' : ''}
                    ${tier.features.api_access ? '<li><i class="fas fa-check text-success me-1"></i>API Access</li>' : ''}
                    ${tier.features.priority_support ? '<li><i class="fas fa-check text-success me-1"></i>Priority Support</li>' : ''}
                    ${tier.features.analytics_dashboard ? '<li><i class="fas fa-check text-success me-1"></i>Analytics Dashboard</li>' : ''}
                    ${tier.features.white_label ? '<li><i class="fas fa-check text-success me-1"></i>White Label</li>' : ''}
                    ${tier.features.multi_location ? '<li><i class="fas fa-check text-success me-1"></i>Multi-location</li>' : ''}
                </ul>
            </div>
        `;
        detailsCard.style.display = 'block';
    } else {
        detailsCard.style.display = 'none';
    }
});

// Currency symbol auto-update
document.getElementById('currency_code').addEventListener('change', function() {
    const currencySymbols = {
        'TZS': 'TZS',
        'USD': '$',
        'KES': 'KSh',
        'UGX': 'UGX'
    };
    
    document.getElementById('currency_symbol').value = currencySymbols[this.value] || this.value;
});
</script>
{% endblock %}
