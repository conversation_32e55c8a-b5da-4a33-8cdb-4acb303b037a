{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Malipo Details - Malipo System
{%- else -%}
    Payment Details - Payment System
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-file-invoice me-2"></i>Payment Details</h1>
    <div>
        <a href="{{ url_for('admin_payments') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>Back to List
        </a>
        {% if payment.status == 'Confirmed' %}
        <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" class="btn btn-success">
            <i class="fas fa-receipt me-1"></i>Generate Receipt
        </a>
        {% endif %}
    </div>
</div>

{% if register_url %}
<div class="alert alert-info">
    <h5><i class="fas fa-user-plus me-2"></i>Registration Link</h5>
    <p class="mb-2">Share this link with the company to complete registration. <strong>This link is valid for 24 hours.</strong></p>
    <div class="mb-3">
        <a href="{{ register_url }}" class="btn btn-warning fw-bold me-2" target="_blank">
            <i class="fas fa-user-plus me-1"></i>Complete Registration
        </a>
        <form method="POST" action="{{ url_for('resend_registration_link', payment_id=payment.id) }}" class="d-inline">
            <button type="submit" class="btn btn-outline-primary" onclick="return confirm('Resend registration link to customer?')">
                <i class="fas fa-paper-plane me-1"></i>Resend Link
            </button>
        </form>
    </div>
    <div class="small text-muted">
        <i class="fas fa-info-circle me-1"></i>
        Registration link will be sent to the customer's email address. Do not share this link publicly.
    </div>
</div>
{% elif payment.status == 'Confirmed' %}
<div class="alert alert-warning">
    <h5><i class="fas fa-exclamation-triangle me-2"></i>Registration Link Needed</h5>
    <p class="mb-2">This payment is confirmed but no registration link is available.</p>
    <form method="POST" action="{{ url_for('resend_registration_link', payment_id=payment.id) }}" class="d-inline">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-paper-plane me-1"></i>Generate & Send Registration Link
        </button>
    </form>
</div>
{% endif %}

<div class="row">
    <!-- Payment Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Payment Information
                    <span class="badge 
                        {% if payment.status == 'Pending' %}bg-warning text-dark
                        {% elif payment.status == 'Confirmed' %}bg-success
                        {% elif payment.status == 'Rejected' %}bg-danger
                        {% elif payment.status == 'Processed' %}bg-info
                        {% else %}bg-secondary{% endif %} ms-2">
                        {{ payment.status }}
                    </span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Reference ID:</dt>
                            <dd class="col-sm-7"><code>REF{{ "%06d"|format(payment.id) }}</code></dd>
                            
                            <dt class="col-sm-5">Customer Name:</dt>
                            <dd class="col-sm-7">{{ payment.customer_name }}</dd>

                            {% if payment.customer_email %}
                            <dt class="col-sm-5">Customer Email:</dt>
                            <dd class="col-sm-7">
                                <a href="mailto:{{ payment.customer_email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>{{ payment.customer_email }}
                                </a>
                            </dd>
                            {% endif %}

                            <dt class="col-sm-5">Amount:</dt>
                            <dd class="col-sm-7"><strong>TZS {{ "{:,.0f}".format(payment.amount or 0) }}</strong></dd>
                            
                            <dt class="col-sm-5">Mobile Operator:</dt>
                            <dd class="col-sm-7">
                                <span class="badge 
                                    {% if payment.mobile_operator == 'M-Pesa' %}bg-success
                                    {% elif payment.mobile_operator == 'Tigo Pesa' %}bg-primary
                                    {% elif payment.mobile_operator == 'Airtel Money' %}bg-danger
                                    {% elif payment.mobile_operator == 'Halo Pesa' %}bg-warning text-dark
                                    {% else %}bg-secondary{% endif %}">
                                    {{ payment.mobile_operator }}
                                </span>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Transaction ID:</dt>
                            <dd class="col-sm-7"><code>{{ payment.transaction_id }}</code></dd>

                            {% if payment.mobile_money_sender_name %}
                            <dt class="col-sm-5">Mobile Money Name:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-info">{{ payment.mobile_money_sender_name }}</span>
                                <small class="text-muted d-block">Name registered with mobile money</small>
                            </dd>
                            {% endif %}

                            <dt class="col-sm-5">Submitted:</dt>
                            <dd class="col-sm-7">{{ payment.submitted_at.strftime('%d/%m/%Y at %H:%M') }}</dd>
                            
                            {% if payment.processed_at %}
                            <dt class="col-sm-5">Processed:</dt>
                            <dd class="col-sm-7">{{ payment.processed_at.strftime('%d/%m/%Y at %H:%M') }}</dd>
                            {% endif %}
                            
                            {% if payment.processed_by %}
                            <dt class="col-sm-5">Processed By:</dt>
                            <dd class="col-sm-7">Admin User #{{ payment.processed_by }}</dd>
                            {% endif %}
                        </dl>
                    </div>
                </div>
                
                {% if payment.service_description %}
                <hr>
                <h6>Service/Product Description:</h6>
                <p class="mb-0">{{ payment.service_description }}</p>
                {% endif %}

                <!-- SMS Verification Section for Admins -->
                {% if payment.status == 'Pending' %}
                <hr>
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-sms me-2"></i>SMS Verification Required
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Verification Instructions:</h6>
                            <ol class="mb-0">
                                <li>Check your mobile money SMS for a payment from <strong>{{ payment.mobile_money_sender_name }}</strong></li>
                                <li>Verify the amount matches: <strong>TZS {{ "{:,.0f}".format(payment.amount) }}</strong></li>
                                <li>Verify the transaction ID matches: <strong>{{ payment.transaction_id }}</strong></li>
                                <li>Fill in the SMS details below and confirm the payment</li>
                            </ol>
                        </div>

                        <form method="POST" action="{{ url_for('payment_action', payment_id=payment.id) }}">
                            <input type="hidden" name="action" value="confirm">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sms_sender" class="form-label">SMS Sender</label>
                                        <select class="form-select" id="sms_sender" name="sms_sender" required>
                                            <option value="">Select SMS sender...</option>
                                            <option value="MPESA">MPESA (Vodacom)</option>
                                            <option value="TIGOPESA">TIGOPESA</option>
                                            <option value="AIRTELMONEY">AIRTELMONEY</option>
                                            <option value="CRDB">CRDB LIPA</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sms_amount" class="form-label">Amount in SMS (TZS)</label>
                                        <input type="number" class="form-control" id="sms_amount" name="sms_amount"
                                               placeholder="Amount from SMS" min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sms_sender_name" class="form-label">Sender Name in SMS</label>
                                        <input type="text" class="form-control" id="sms_sender_name" name="sms_sender_name"
                                               placeholder="Name from SMS" required>
                                        <div class="form-text">Should match: {{ payment.mobile_money_sender_name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sms_transaction_ref" class="form-label">Transaction Ref in SMS</label>
                                        <input type="text" class="form-control" id="sms_transaction_ref" name="sms_transaction_ref"
                                               placeholder="Transaction reference from SMS">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="admin_verification_notes" class="form-label">Verification Notes</label>
                                <textarea class="form-control" id="admin_verification_notes" name="admin_verification_notes"
                                          rows="3" placeholder="Add any notes about the verification process..."></textarea>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>Confirm Payment
                                </button>
                                <button type="button" class="btn btn-danger" onclick="rejectPayment()">
                                    <i class="fas fa-times me-1"></i>Reject Payment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}
                
                {% if payment.rejection_reason %}
                <hr>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Rejection Reason:</h6>
                    <p class="mb-0">{{ payment.rejection_reason }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Internal Notes -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Internal Notes</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('payment_action', payment_id=payment.id) }}">
                    <input type="hidden" name="action" value="add_notes">
                    <div class="mb-3">
                        <textarea class="form-control" name="internal_notes" rows="4" 
                                  placeholder="Add internal notes about this payment...">{{ payment.internal_notes or '' }}</textarea>
                    </div>
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-save me-1"></i>Save Notes
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Actions Panel -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h5>
            </div>
            <div class="card-body">
                {% if payment.status == 'Pending' %}
                <!-- Confirm Payment -->
                <form method="POST" action="{{ url_for('payment_action', payment_id=payment.id) }}" class="mb-3">
                    <input type="hidden" name="action" value="confirm">
                    <button type="submit" class="btn btn-success w-100" 
                            onclick="return confirm('Are you sure you want to confirm this payment? This action cannot be undone.')">
                        <i class="fas fa-check me-2"></i>Confirm Payment
                    </button>
                </form>

                <!-- Reject Payment -->
                <form method="POST" action="{{ url_for('payment_action', payment_id=payment.id) }}">
                    <input type="hidden" name="action" value="reject">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Rejection Reason:</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" 
                                  rows="3" placeholder="Explain why this payment is being rejected..." required></textarea>
                    </div>
                    <button type="submit" class="btn btn-danger w-100" 
                            onclick="return confirm('Are you sure you want to reject this payment?')">
                        <i class="fas fa-times me-2"></i>Reject Payment
                    </button>
                </form>

                {% elif payment.status == 'Confirmed' %}
                <!-- Mark as Processed -->
                <form method="POST" action="{{ url_for('payment_action', payment_id=payment.id) }}" class="mb-3">
                    <input type="hidden" name="action" value="process">
                    <button type="submit" class="btn btn-info w-100">
                        <i class="fas fa-check-double me-2"></i>Mark as Processed
                    </button>
                </form>

                <!-- Generate Receipt -->
                <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" class="btn btn-success w-100">
                    <i class="fas fa-receipt me-2"></i>Generate Receipt
                </a>

                {% elif payment.status == 'Processed' %}
                <div class="alert alert-success text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h6>Payment Processed</h6>
                    <p class="mb-0">This payment has been fully processed.</p>
                </div>

                {% if payment.receipt_generated %}
                <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" class="btn btn-outline-success w-100">
                    <i class="fas fa-download me-2"></i>Download Receipt
                </a>
                {% endif %}

                {% elif payment.status == 'Rejected' %}
                <div class="alert alert-danger text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h6>Payment Rejected</h6>
                    <p class="mb-0">This payment has been rejected.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Receipt Information -->
        {% if payment.status in ['Confirmed', 'Processed'] %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-receipt me-2"></i>Receipt</h6>
            </div>
            <div class="card-body">
                {% if payment.receipt_generated %}
                <div class="text-success">
                    <i class="fas fa-check-circle me-2"></i>Receipt generated
                </div>
                <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" class="btn btn-sm btn-outline-success mt-2">
                    <i class="fas fa-download me-1"></i>Download
                </a>
                {% else %}
                <div class="text-muted">
                    <i class="fas fa-clock me-2"></i>Receipt not generated yet
                </div>
                <a href="{{ url_for('generate_receipt', payment_id=payment.id) }}" class="btn btn-sm btn-success mt-2">
                    <i class="fas fa-plus me-1"></i>Generate
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
