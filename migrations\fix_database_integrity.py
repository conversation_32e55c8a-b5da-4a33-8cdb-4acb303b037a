#!/usr/bin/env python3
"""
Database Integrity Migration for EXLIPA
Fixes critical database integrity issues and adds proper constraints
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, db
from sqlalchemy import text
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseIntegrityMigration:
    """Handles database integrity fixes"""
    
    def __init__(self):
        self.app = app
        self.db = db
    
    def backup_database(self):
        """Create database backup before migration"""
        try:
            backup_filename = f"payment_system_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            # For SQLite, copy the file
            import shutil
            shutil.copy2('payment_system.db', backup_filename)
            
            logger.info(f"Database backed up to {backup_filename}")
            return backup_filename
            
        except Exception as e:
            logger.error(f"Backup failed: {str(e)}")
            raise
    
    def fix_payment_confirmation_constraints(self):
        """Fix PaymentConfirmation table constraints"""
        try:
            with self.app.app_context():
                # Check for orphaned payment confirmations
                orphaned_payments = self.db.session.execute(text("""
                    SELECT id FROM payment_confirmation 
                    WHERE invoice_id IS NULL AND client_company_id IS NULL
                """)).fetchall()
                
                if orphaned_payments:
                    logger.warning(f"Found {len(orphaned_payments)} orphaned payment confirmations")
                    
                    # Option 1: Delete orphaned records (safer)
                    # Option 2: Associate with a default company (if exists)
                    
                    # For now, let's delete orphaned records
                    self.db.session.execute(text("""
                        DELETE FROM payment_confirmation 
                        WHERE invoice_id IS NULL AND client_company_id IS NULL
                    """))
                    
                    logger.info(f"Deleted {len(orphaned_payments)} orphaned payment confirmations")
                
                # Add check constraint (SQLite doesn't support adding constraints directly)
                # We'll handle this in the model definition
                
                self.db.session.commit()
                logger.info("PaymentConfirmation constraints fixed")
                
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to fix PaymentConfirmation constraints: {str(e)}")
            raise
    
    def fix_user_roles(self):
        """Standardize user roles"""
        try:
            with self.app.app_context():
                # Update role names to standard format
                role_mappings = {
                    'admin': 'user_admin',  # Old admin role becomes user_admin
                    'super_admin': 'super_admin',  # Keep as is
                    'company_user': 'company_user'  # Keep as is
                }
                
                for old_role, new_role in role_mappings.items():
                    result = self.db.session.execute(text("""
                        UPDATE user SET role = :new_role WHERE role = :old_role
                    """), {'old_role': old_role, 'new_role': new_role})
                    
                    if result.rowcount > 0:
                        logger.info(f"Updated {result.rowcount} users from role '{old_role}' to '{new_role}'")
                
                # Ensure no users have invalid roles
                invalid_roles = self.db.session.execute(text("""
                    SELECT DISTINCT role FROM user 
                    WHERE role NOT IN ('super_admin', 'user_admin', 'company_user')
                """)).fetchall()
                
                if invalid_roles:
                    logger.warning(f"Found invalid roles: {[r[0] for r in invalid_roles]}")
                    
                    # Set invalid roles to user_admin as default
                    self.db.session.execute(text("""
                        UPDATE user SET role = 'user_admin' 
                        WHERE role NOT IN ('super_admin', 'user_admin', 'company_user')
                    """))
                    
                    logger.info("Fixed invalid user roles")
                
                self.db.session.commit()
                logger.info("User roles standardized")
                
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to fix user roles: {str(e)}")
            raise
    
    def fix_company_user_relationships(self):
        """Fix company-user ownership relationships"""
        try:
            with self.app.app_context():
                # Find companies without proper owner relationships
                companies_without_owners = self.db.session.execute(text("""
                    SELECT id, company_email FROM client_company 
                    WHERE owner_user_id IS NULL AND company_email IS NOT NULL
                """)).fetchall()
                
                for company_id, company_email in companies_without_owners:
                    # Try to find matching user by email
                    user = self.db.session.execute(text("""
                        SELECT id FROM user WHERE email = :email AND role = 'user_admin'
                    """), {'email': company_email}).fetchone()
                    
                    if user:
                        # Update company with owner relationship
                        self.db.session.execute(text("""
                            UPDATE client_company SET owner_user_id = :user_id WHERE id = :company_id
                        """), {'user_id': user[0], 'company_id': company_id})
                        
                        logger.info(f"Linked company {company_id} to user {user[0]}")
                
                self.db.session.commit()
                logger.info("Company-user relationships fixed")
                
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to fix company-user relationships: {str(e)}")
            raise
    
    def add_missing_indexes(self):
        """Add database indexes for performance"""
        try:
            with self.app.app_context():
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_payment_confirmation_status ON payment_confirmation(status)",
                    "CREATE INDEX IF NOT EXISTS idx_payment_confirmation_transaction_id ON payment_confirmation(transaction_id)",
                    "CREATE INDEX IF NOT EXISTS idx_payment_confirmation_submitted_at ON payment_confirmation(submitted_at)",
                    "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)",
                    "CREATE INDEX IF NOT EXISTS idx_user_role ON user(role)",
                    "CREATE INDEX IF NOT EXISTS idx_client_company_email ON client_company(company_email)",
                    "CREATE INDEX IF NOT EXISTS idx_invoice_status ON invoice(status)",
                    "CREATE INDEX IF NOT EXISTS idx_pos_sale_created_at ON pos_sale(created_at)"
                ]
                
                for index_sql in indexes:
                    try:
                        self.db.session.execute(text(index_sql))
                        logger.info(f"Created index: {index_sql}")
                    except Exception as e:
                        logger.warning(f"Index creation failed (may already exist): {str(e)}")
                
                self.db.session.commit()
                logger.info("Database indexes added")
                
        except Exception as e:
            self.db.session.rollback()
            logger.error(f"Failed to add indexes: {str(e)}")
            raise
    
    def validate_data_integrity(self):
        """Validate data integrity after migration"""
        try:
            with self.app.app_context():
                issues = []
                
                # Check for orphaned payment confirmations
                orphaned = self.db.session.execute(text("""
                    SELECT COUNT(*) FROM payment_confirmation 
                    WHERE invoice_id IS NULL AND client_company_id IS NULL
                """)).scalar()
                
                if orphaned > 0:
                    issues.append(f"{orphaned} orphaned payment confirmations")
                
                # Check for users with invalid roles
                invalid_roles = self.db.session.execute(text("""
                    SELECT COUNT(*) FROM user 
                    WHERE role NOT IN ('super_admin', 'user_admin', 'company_user')
                """)).scalar()
                
                if invalid_roles > 0:
                    issues.append(f"{invalid_roles} users with invalid roles")
                
                # Check for duplicate transaction IDs
                duplicates = self.db.session.execute(text("""
                    SELECT transaction_id, COUNT(*) as count 
                    FROM payment_confirmation 
                    WHERE transaction_id IS NOT NULL 
                    GROUP BY transaction_id 
                    HAVING COUNT(*) > 1
                """)).fetchall()
                
                if duplicates:
                    issues.append(f"{len(duplicates)} duplicate transaction IDs")
                
                if issues:
                    logger.warning(f"Data integrity issues found: {', '.join(issues)}")
                    return False
                else:
                    logger.info("Data integrity validation passed")
                    return True
                    
        except Exception as e:
            logger.error(f"Data integrity validation failed: {str(e)}")
            return False
    
    def run_migration(self):
        """Run the complete migration"""
        try:
            logger.info("Starting database integrity migration...")
            
            # 1. Backup database
            backup_file = self.backup_database()
            logger.info(f"Database backed up to {backup_file}")
            
            # 2. Fix payment confirmation constraints
            self.fix_payment_confirmation_constraints()
            
            # 3. Fix user roles
            self.fix_user_roles()
            
            # 4. Fix company-user relationships
            self.fix_company_user_relationships()
            
            # 5. Add missing indexes
            self.add_missing_indexes()
            
            # 6. Validate data integrity
            if self.validate_data_integrity():
                logger.info("✅ Database integrity migration completed successfully!")
                return True
            else:
                logger.error("❌ Migration completed but data integrity issues remain")
                return False
                
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            logger.info(f"Database backup available at: {backup_file}")
            raise

def main():
    """Run the migration"""
    logging.basicConfig(level=logging.INFO)
    
    migration = DatabaseIntegrityMigration()
    
    try:
        success = migration.run_migration()
        if success:
            print("✅ Database integrity migration completed successfully!")
        else:
            print("❌ Migration completed with issues. Check logs.")
            
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
