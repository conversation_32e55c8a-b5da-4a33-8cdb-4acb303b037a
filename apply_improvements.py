#!/usr/bin/env python3
"""
EXLIPA Codebase Improvement Script
Run this to apply recommended optimizations
"""

import re

def apply_datetime_fixes():
    """Apply datetime.utcnow() fixes"""
    print("🔧 Applying datetime fixes...")
    
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add timezone import if not present
    if 'timezone' not in content:
        content = content.replace(
            'from datetime import datetime, timedelta',
            'from datetime import datetime, timedelta, timezone'
        )
    
    # Replace datetime.utcnow() with datetime.now(timezone.utc)
    content = content.replace('datetime.utcnow()', 'datetime.now(timezone.utc)')
    
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Applied datetime fixes")

if __name__ == '__main__':
    print("🚀 EXLIPA Codebase Improvement")
    print("="*40)
    
    response = input("Apply datetime fixes? (y/n): ")
    if response.lower() == 'y':
        apply_datetime_fixes()
        print("✅ Improvements applied!")
    else:
        print("ℹ️  No changes made")
