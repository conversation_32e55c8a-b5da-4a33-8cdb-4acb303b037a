#!/usr/bin/env python3
"""
Debug login by creating a simple test route
"""

from flask import Flask, request, render_template_string, flash, redirect, url_for
from app import app, db, User, simple_rate_limit, sanitize_input
from werkzeug.security import check_password_hash
from datetime import datetime

# Add a debug route to test login
@app.route('/debug-login', methods=['GET', 'POST'])
def debug_login():
    if request.method == 'POST':
        print("=== DEBUG LOGIN ATTEMPT ===")
        
        # Get form data
        username = request.form.get('username', '')
        password = request.form.get('password', '')
        
        print(f"Form data - Username: '{username}', Password: '{password}'")
        
        # Rate limiting check
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        rate_limit_key = f"login_master_{client_ip}"
        
        print(f"Client IP: {client_ip}")
        print(f"Rate limit key: {rate_limit_key}")
        
        if not simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15):
            print("❌ Rate limit exceeded")
            flash('Too many login attempts. Please try again in 15 minutes.', 'danger')
            return render_template_string(DEBUG_TEMPLATE)
        
        print("✅ Rate limit OK")
        
        # Sanitize input
        username = sanitize_input(username, 80)
        print(f"Sanitized username: '{username}'")
        
        if not username or not password:
            print("❌ Missing username or password")
            flash('Username and password are required', 'danger')
            return render_template_string(DEBUG_TEMPLATE)
        
        print("✅ Username and password provided")
        
        # Find user
        user = User.query.filter_by(username=username, is_active=True, role='master_admin').first()
        print(f"User query result: {user}")
        
        if user:
            print(f"User found - ID: {user.id}, Username: {user.username}, Role: {user.role}")
            
            # Check if locked
            if user.is_account_locked():
                print("❌ Account is locked")
                flash('Account is temporarily locked due to failed login attempts', 'danger')
                return render_template_string(DEBUG_TEMPLATE)
            
            print("✅ Account not locked")
            
            # Check password
            password_valid = check_password_hash(user.password_hash, password)
            print(f"Password valid: {password_valid}")
            
            if password_valid:
                print("✅ LOGIN SUCCESS!")
                flash('Login successful! (Debug mode)', 'success')
                return render_template_string(DEBUG_TEMPLATE + "<p><strong>LOGIN WOULD SUCCEED</strong></p>")
            else:
                print("❌ Invalid password")
                flash('Invalid password', 'danger')
        else:
            print("❌ User not found")
            flash('User not found or not a master admin', 'danger')
        
        print("=== END DEBUG LOGIN ===")
    
    return render_template_string(DEBUG_TEMPLATE)

DEBUG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Debug Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5" style="max-width: 400px;">
        <h2>Debug Login Test</h2>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" value="admin" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" value="admin123" required>
            </div>
            <button type="submit" class="btn btn-primary">Test Login</button>
        </form>
        
        <hr>
        <p><a href="/admin-login">Try Real Login</a></p>
    </div>
</body>
</html>
'''

if __name__ == '__main__':
    with app.app_context():
        print("Debug login route added at: http://localhost:5000/debug-login")
        print("You can test this route to see detailed login debugging")
