#!/usr/bin/env python3
"""
Create admin users for EXLIPA system
"""

from app import app, db, User
from werkzeug.security import generate_password_hash

def create_admin_users():
    """Create admin users with correct credentials"""
    with app.app_context():
        print("=== CREATING ADMIN USERS ===")
        
        # Check existing users
        existing_users = User.query.all()
        print(f"Existing users: {len(existing_users)}")
        for user in existing_users:
            print(f"  - {user.username} ({user.role})")
        
        # Create master admin if doesn't exist
        master_admin = User.query.filter_by(username='admin').first()
        if not master_admin:
            master_admin = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                email='<EMAIL>',
                full_name='Master Administrator',
                role='master_admin',
                is_active=True
            )
            db.session.add(master_admin)
            print("✅ Created master admin: admin / admin123")
        else:
            # Update existing admin role if needed
            if master_admin.role != 'master_admin':
                master_admin.role = 'master_admin'
                print("✅ Updated admin role to master_admin")
        
        # Create user admin
        user_admin = User.query.filter_by(username='useradmin').first()
        if not user_admin:
            user_admin = User(
                username='useradmin',
                password_hash=generate_password_hash('useradmin123'),
                email='<EMAIL>',
                full_name='User Administrator',
                role='user_admin',
                is_active=True
            )
            db.session.add(user_admin)
            print("✅ Created user admin: useradmin / useradmin123")
        
        # Create company user
        company_user = User.query.filter_by(username='juma').first()
        if not company_user:
            company_user = User(
                username='juma',
                password_hash=generate_password_hash('juma123'),
                email='<EMAIL>',
                full_name='Juma Mohamed',
                role='company_user',
                is_active=True
            )
            db.session.add(company_user)
            print("✅ Created company user: juma / juma123")
        
        # Create a simple admin for testing
        simple_admin = User.query.filter_by(username='test').first()
        if not simple_admin:
            simple_admin = User(
                username='test',
                password_hash=generate_password_hash('test123'),
                email='<EMAIL>',
                full_name='Test User',
                role='admin',
                is_active=True
            )
            db.session.add(simple_admin)
            print("✅ Created test user: test / test123")
        
        try:
            db.session.commit()
            print("\n🎉 ADMIN USERS CREATED SUCCESSFULLY!")
            
            # Display final user list
            all_users = User.query.all()
            print(f"\n📊 TOTAL USERS: {len(all_users)}")
            for user in all_users:
                print(f"   👤 {user.username} ({user.role}) - {user.email}")
            
            print("\n🔐 LOGIN CREDENTIALS:")
            print("   Master Admin: admin / admin123")
            print("   User Admin: useradmin / useradmin123") 
            print("   Company User: juma / juma123")
            print("   Test User: test / test123")
            
            print("\n🌐 LOGIN URLS:")
            print("   Master Admin: http://localhost:5000/admin-login")
            print("   User Admin: http://localhost:5000/login")
            print("   Company User: http://localhost:5000/company-login")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error creating users: {e}")

if __name__ == '__main__':
    create_admin_users()
