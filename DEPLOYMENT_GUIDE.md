# 🚀 EXLIPA Payment Gateway - Production Deployment Guide

## 📋 **Deployment Overview**

This guide provides step-by-step instructions for deploying the EXLIPA Payment Gateway to a production environment with enterprise-grade security and performance.

---

## 🖥️ **Server Requirements**

### **Minimum Production Requirements**
- **CPU**: 2 cores (4 cores recommended)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 50GB SSD (100GB recommended)
- **OS**: Ubuntu 20.04 LTS or CentOS 8
- **Network**: Static IP address with domain name

### **Recommended Production Setup**
- **CPU**: 4 cores Intel/AMD
- **RAM**: 8GB DDR4
- **Storage**: 100GB NVMe SSD
- **Backup**: Automated daily backups
- **Monitoring**: 24/7 uptime monitoring

---

## 🔧 **Server Setup & Configuration**

### **1. Initial Server Setup**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common

# Configure firewall
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Create application user
sudo adduser exlipa
sudo usermod -aG sudo exlipa
```

### **2. Install Python & Dependencies**
```bash
# Install Python 3.8+
sudo apt install -y python3 python3-pip python3-venv python3-dev

# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Install Redis for caching
sudo apt install -y redis-server

# Install Nginx web server
sudo apt install -y nginx

# Install SSL certificate tool
sudo apt install -y certbot python3-certbot-nginx
```

### **3. Database Setup**
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE exlipa_prod;
CREATE USER exlipa_user WITH PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE exlipa_prod TO exlipa_user;
\q

# Configure PostgreSQL for production
sudo nano /etc/postgresql/12/main/postgresql.conf
# Set: shared_buffers = 256MB, effective_cache_size = 1GB

sudo systemctl restart postgresql
```

---

## 📦 **Application Deployment**

### **1. Deploy Application Code**
```bash
# Switch to application user
sudo su - exlipa

# Clone repository
git clone https://github.com/your-org/exlipa-payment-gateway.git
cd exlipa-payment-gateway

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
pip install gunicorn psycopg2-binary
```

### **2. Environment Configuration**
```bash
# Create production environment file
cp .env.example .env
nano .env
```

**Production .env Configuration**:
```env
# Security
SECRET_KEY=your-super-secure-production-key-minimum-32-characters
FLASK_ENV=production
FLASK_DEBUG=False

# Database
SQLALCHEMY_DATABASE_URI=postgresql://exlipa_user:your_secure_password_here@localhost/exlipa_prod

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-specific-password
MAIL_DEFAULT_SENDER=Exlipa Payment Gateway <<EMAIL>>

# Business Settings
COMPANY_NAME=Exlipa Payment Solutions Ltd
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+*********** 789
DOMAIN_NAME=yourdomain.com

# Security Headers
FORCE_HTTPS=True
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
```

### **3. Initialize Production Database**
```bash
# Run secure database initialization
python initialize_secure_database.py

# Verify security features
python comprehensive_security_test.py
```

---

## 🌐 **Web Server Configuration**

### **1. Nginx Configuration**
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/exlipa
```

**Nginx Configuration File**:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Application Proxy
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static Files
    location /static {
        alias /home/<USER>/exlipa-payment-gateway/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    location /admin-login {
        limit_req zone=login burst=3 nodelay;
        proxy_pass http://127.0.0.1:8000;
    }
}
```

### **2. Enable Nginx Configuration**
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/exlipa /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### **3. SSL Certificate Setup**
```bash
# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Set up automatic renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

---

## 🔄 **Process Management**

### **1. Gunicorn Configuration**
```bash
# Create Gunicorn configuration
nano /home/<USER>/exlipa-payment-gateway/gunicorn.conf.py
```

**Gunicorn Configuration**:
```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "exlipa"
group = "exlipa"
```

### **2. Systemd Service Configuration**
```bash
# Create systemd service
sudo nano /etc/systemd/system/exlipa.service
```

**Systemd Service File**:
```ini
[Unit]
Description=Exlipa Payment Gateway
After=network.target postgresql.service redis.service

[Service]
Type=notify
User=exlipa
Group=exlipa
WorkingDirectory=/home/<USER>/exlipa-payment-gateway
Environment=PATH=/home/<USER>/exlipa-payment-gateway/venv/bin
ExecStart=/home/<USER>/exlipa-payment-gateway/venv/bin/gunicorn -c gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### **3. Start Services**
```bash
# Reload systemd and start service
sudo systemctl daemon-reload
sudo systemctl enable exlipa
sudo systemctl start exlipa

# Check service status
sudo systemctl status exlipa

# View logs
sudo journalctl -u exlipa -f
```

---

## 📊 **Monitoring & Logging**

### **1. Application Logging**
```bash
# Create log directory
sudo mkdir -p /var/log/exlipa
sudo chown exlipa:exlipa /var/log/exlipa

# Configure log rotation
sudo nano /etc/logrotate.d/exlipa
```

**Log Rotation Configuration**:
```
/var/log/exlipa/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 exlipa exlipa
    postrotate
        systemctl reload exlipa
    endscript
}
```

### **2. System Monitoring**
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Set up basic monitoring script
nano /home/<USER>/monitor.sh
```

**Basic Monitoring Script**:
```bash
#!/bin/bash
# Basic system monitoring for EXLIPA

# Check disk space
df -h | grep -E "/$|/home" | awk '{print $5}' | sed 's/%//' | while read usage; do
    if [ $usage -gt 80 ]; then
        echo "WARNING: Disk usage is ${usage}%" | mail -s "Disk Space Alert" <EMAIL>
    fi
done

# Check service status
if ! systemctl is-active --quiet exlipa; then
    echo "CRITICAL: EXLIPA service is down" | mail -s "Service Alert" <EMAIL>
fi

# Check database connectivity
if ! pg_isready -h localhost -p 5432 -U exlipa_user; then
    echo "CRITICAL: Database connection failed" | mail -s "Database Alert" <EMAIL>
fi
```

---

## 🔒 **Security Hardening**

### **1. System Security**
```bash
# Disable root login
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no, PasswordAuthentication no

# Install fail2ban
sudo apt install -y fail2ban

# Configure fail2ban for Nginx
sudo nano /etc/fail2ban/jail.local
```

**Fail2ban Configuration**:
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
```

### **2. Database Security**
```bash
# Secure PostgreSQL
sudo nano /etc/postgresql/12/main/pg_hba.conf
# Change: local all all peer to local all all md5

# Restart PostgreSQL
sudo systemctl restart postgresql
```

---

## 🔄 **Backup & Recovery**

### **1. Database Backup**
```bash
# Create backup script
nano /home/<USER>/backup.sh
```

**Backup Script**:
```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U exlipa_user exlipa_prod > $BACKUP_DIR/db_backup_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /home/<USER>/exlipa-payment-gateway

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

### **2. Automated Backups**
```bash
# Make backup script executable
chmod +x /home/<USER>/backup.sh

# Add to crontab for daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup.sh >> /var/log/backup.log 2>&1
```

---

## ✅ **Deployment Verification**

### **1. Health Checks**
```bash
# Test application health
curl -f https://yourdomain.com/health

# Test SSL configuration
curl -I https://yourdomain.com

# Test payment form
curl -X POST https://yourdomain.com/confirm-payment \
  -d "customer_name=Test User" \
  -d "customer_email=<EMAIL>" \
  -d "mobile_money_sender_name=Test User" \
  -d "amount=5000" \
  -d "mobile_operator=M-Pesa" \
  -d "transaction_id=ND12345678"
```

### **2. Performance Testing**
```bash
# Install Apache Bench
sudo apt install -y apache2-utils

# Test concurrent users
ab -n 1000 -c 10 https://yourdomain.com/

# Test payment endpoint
ab -n 100 -c 5 -p payment_data.txt -T application/x-www-form-urlencoded https://yourdomain.com/confirm-payment
```

### **3. Security Testing**
```bash
# Run comprehensive security test
python comprehensive_security_test.py

# Test SSL configuration
curl -I https://yourdomain.com | grep -i security

# Verify rate limiting
for i in {1..10}; do curl https://yourdomain.com/admin-login; done
```

---

## 🎯 **Go-Live Checklist**

### **Pre-Launch**
- [ ] Server provisioned and configured
- [ ] SSL certificate installed and tested
- [ ] Database initialized with secure settings
- [ ] Application deployed and running
- [ ] Monitoring and logging configured
- [ ] Backup system operational
- [ ] Security hardening completed
- [ ] Performance testing passed

### **Launch Day**
- [ ] DNS records updated to point to production server
- [ ] Email configuration tested and working
- [ ] Payment processing tested with real mobile money accounts
- [ ] Admin team trained on SMS verification process
- [ ] Customer support procedures activated
- [ ] Monitoring alerts configured

### **Post-Launch**
- [ ] Monitor system performance and logs
- [ ] Verify payment processing workflow
- [ ] Check email delivery and registration links
- [ ] Review security logs for any issues
- [ ] Gather initial customer feedback
- [ ] Document any issues and resolutions

---

**🎉 Congratulations! Your EXLIPA Payment Gateway is now live and ready to process payments securely! 🇹🇿💰**

**© 2024 Exlipa Payment Solutions Ltd. All rights reserved.**
