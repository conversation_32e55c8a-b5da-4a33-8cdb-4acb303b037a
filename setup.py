#!/usr/bin/env python3
"""
Setup script for the Mobile Money Payment Confirmation System
"""

import os
import sys
import subprocess

def run_command(command):
    """Run a system command and return success status"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_dependencies():
    """Install required Python packages"""
    print("Installing Python dependencies...")
    
    packages = [
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5", 
        "Flask-Login==0.6.2",
        "Flask-WTF==1.1.1",
        "WTForms==3.0.1",
        "reportlab==4.0.4",
        "Werkzeug==2.3.7",
        "python-dotenv==1.0.0"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        success, stdout, stderr = run_command(f"python -m pip install {package}")
        if not success:
            print(f"Warning: Failed to install {package}")
            print(f"Error: {stderr}")
    
    print("Dependencies installation completed!")

def setup_environment():
    """Setup environment configuration"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("Creating .env file from template...")
            with open('.env.example', 'r') as src:
                content = src.read()
            with open('.env', 'w') as dst:
                dst.write(content)
            print("Created .env file. Please edit it with your company details.")
        else:
            print("Creating basic .env file...")
            env_content = """SECRET_KEY=change-this-secret-key-in-production
COMPANY_NAME=Your Company Name Ltd
COMPANY_ADDRESS=P.O. Box 12345, Dar es Salaam, Tanzania
COMPANY_PHONE=+255 XXX XXX XXX
COMPANY_EMAIL=<EMAIL>
"""
            with open('.env', 'w') as f:
                f.write(env_content)
    else:
        print(".env file already exists.")

def create_database():
    """Create the initial database"""
    print("Setting up database...")
    try:
        # Import and initialize the app to create database
        from app import app, db
        with app.app_context():
            db.create_all()
            print("Database created successfully!")
    except Exception as e:
        print(f"Database setup failed: {e}")
        print("You can run 'python app.py' to create the database automatically.")

def main():
    """Main setup function"""
    print("=" * 60)
    print("Mobile Money Payment Confirmation System - Setup")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required!")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    
    # Install dependencies
    try:
        install_dependencies()
    except Exception as e:
        print(f"Error installing dependencies: {e}")
        print("Please install manually using: pip install -r requirements.txt")
    
    # Setup environment
    setup_environment()
    
    # Create database
    create_database()
    
    print("\n" + "=" * 60)
    print("Setup completed!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Edit .env file with your company details")
    print("2. Run: python app.py")
    print("3. Open browser to: http://localhost:5000")
    print("4. Admin login: admin / admin123")
    print("\nFor production deployment, see README.md")

if __name__ == "__main__":
    main()
