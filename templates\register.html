{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Register - Exlipa
{%- else -%}
    Register - Exlipa
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header text-center bg-primary text-white">
                    <h3 class="mb-0">Create Your Company Account</h3>
                </div>
                <div class="card-body">
                    {% if existing_user %}
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Account Already Exists</h5>
                        <p class="mb-2">An account with the email <strong>{{ email }}</strong> already exists.</p>
                        <p class="mb-3">If this is your account, please login to access your dashboard.</p>
                        <a href="{{ login_url }}" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login to Existing Account
                        </a>
                    </div>
                    <hr>
                    <p class="text-muted small">If you believe this is an error or need to create a new account with a different email, please contact support.</p>
                    {% else %}
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                            <div class="invalid-feedback">Please enter your full name.</div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="invalid-feedback">Password must be at least 6 characters.</div>
                        </div>
                        <div class="mb-3">
                            <label for="company_name" class="form-label">Company Name *</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" required>
                            <div class="invalid-feedback">Please enter your company name.</div>
                        </div>
                        <div class="mb-3">
                            <label for="company_phone" class="form-label">{{ t('Company Phone') }}</label>
                            <input type="tel" class="form-control" id="company_phone" name="company_phone">
                        </div>
                        <div class="mb-3">
                            <label for="company_website" class="form-label">Company Website</label>
                            <input type="url" class="form-control" id="company_website" name="company_website" placeholder="https://www.example.com">
                        </div>
                        <button type="submit" class="btn btn-success w-100">Register & Start Onboarding</button>
                    </form>
                    {% endif %}
                </div>
                <div class="card-footer text-center">
                    Already have an account? <a href="{{ url_for('company_login') }}">{{ t('Login') }}</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
