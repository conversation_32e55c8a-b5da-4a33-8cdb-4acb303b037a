#!/usr/bin/env python3
"""
Priority Translation Fixer for EXLIPA
Focuses on high-impact templates that users see most frequently
"""

import os
import re
import glob
from typing import Dict, List, Tuple

class PriorityTranslationFixer:
    def __init__(self):
        # Enhanced translation mapping with all discovered words
        self.translations = {
            # Core UI Elements
            'Welcome': '<PERSON><PERSON><PERSON>', 'Login': 'Ingia', 'Logout': 'Toka', 'Dashboard': 'Dashibodi',
            'Company': '<PERSON><PERSON><PERSON>', 'Admin': '<PERSON><PERSON><PERSON><PERSON>', 'Master': '<PERSON>uu', 'User': 'Mtumiaji',
            
            # Payment & Business
            'Payment': '<PERSON>po', 'Payments': 'Malipo', 'Invoice': 'Ankara', 'Invoices': 'Ankara',
            'Amount': 'Kiasi', 'Customer': 'Mteja', 'Email': 'Barua pepe', 'Phone': 'Simu',
            'Billing': '<PERSON><PERSON>', 'Fee': '<PERSON>', 'Price': '<PERSON><PERSON>', 'Cost': '<PERSON><PERSON><PERSON>', 'Charge': '<PERSON><PERSON>',
            
            # Actions
            'Submit': '<PERSON><PERSON><PERSON>', 'Confirm': 'Thibitisha', 'Cancel': '<PERSON><PERSON><PERSON>', 'Edit': '<PERSON><PERSON>',
            'Delete': 'Futa', 'View': 'Ona', 'Manage': 'Simamia', 'Create': 'Unda', 'Add': 'Ongeza',
            'Update': 'Sasisha', 'Save': 'Hifadhi', 'Back': 'Rudi', 'Next': 'Ifuatayo',
            'Previous': 'Iliyotangulia', 'Continue': 'Endelea', 'Finish': 'Maliza', 'Complete': 'Kamili',
            
            # Status
            'Active': 'Hai', 'Inactive': 'Haifanyi Kazi', 'Pending': 'Inasubiri', 'Confirmed': 'Imethibitishwa',
            'Rejected': 'Imekataliwa', 'Approved': 'Imeidhinishwa', 'Locked': 'Imefungwa',
            'Success': 'Mafanikio', 'Error': 'Hitilafu', 'Warning': 'Onyo', 'Info': 'Taarifa',
            
            # Common Fields
            'Name': 'Jina', 'Description': 'Maelezo', 'Status': 'Hali', 'Features': 'Vipengele',
            'Actions': 'Vitendo', 'Settings': 'Mipangilio', 'Profile': 'Wasifu', 'Password': 'Nywila',
            'Username': 'Jina la Mtumiaji',
            
            # Operations
            'Search': 'Tafuta', 'Filter': 'Chuja', 'Sort': 'Panga', 'Export': 'Hamisha',
            'Import': 'Leta', 'Download': 'Pakua', 'Upload': 'Pakia',
            
            # Organization
            'Team': 'Timu', 'Members': 'Wanachama', 'Users': 'Watumiaji', 'Companies': 'Makampuni',
            'Organizations': 'Mashirika',
            
            # Analytics
            'Analytics': 'Uchambuzi', 'Reports': 'Ripoti', 'Statistics': 'Takwimu',
            'Metrics': 'Vipimo', 'Performance': 'Utendaji',
            
            # Support
            'Help': 'Msaada', 'Support': 'Msaada', 'Contact': 'Wasiliana', 'About': 'Kuhusu',
            'FAQ': 'Maswali Yanayoulizwa Mara kwa Mara', 'Documentation': 'Nyaraka',
            
            # Authentication
            'Remember': 'Kumbuka', 'Forgot': 'Umesahau', 'Reset': 'Weka upya', 'Change': 'Badilisha',
            
            # Messages
            'Message': 'Ujumbe', 'Notification': 'Arifa',
            
            # Navigation
            'Home': 'Nyumbani',
            
            # Numbers & Quantities
            'Total': 'Jumla', 'Count': 'Idadi', 'Number': 'Nambari', 'Quantity': 'Wingi',
            
            # Time
            'Date': 'Tarehe', 'Time': 'Muda', 'Today': 'Leo', 'Yesterday': 'Jana',
            'Tomorrow': 'Kesho', 'Week': 'Wiki', 'Month': 'Mwezi', 'Year': 'Mwaka',
            
            # Quantifiers
            'All': 'Yote', 'None': 'Hakuna', 'Any': 'Yoyote', 'Some': 'Baadhi',
            'Every': 'Kila', 'Each': 'Kila', 'Other': 'Nyingine', 'More': 'Zaidi', 'Less': 'Kidogo',
            
            # Additional business terms
            'System': 'Mfumo', 'Service': 'Huduma', 'Product': 'Bidhaa', 'Order': 'Agizo',
            'Transaction': 'Muamala', 'Reference': 'Rejea', 'Code': 'Msimbo', 'ID': 'Kitambulisho',
            'Method': 'Njia', 'Type': 'Aina', 'Category': 'Jamii', 'Group': 'Kundi',
            'List': 'Orodha', 'Table': 'Jedwali', 'Form': 'Fomu', 'Field': 'Uga',
            'Required': 'Inahitajika', 'Optional': 'Si Lazima', 'Available': 'Inapatikana',
            'Unavailable': 'Haipatikani', 'Processing': 'Inachakatwa', 'Loading': 'Inapakia',
        }
        
        # Priority templates (most user-facing)
        self.priority_templates = [
            'public_company_landing.html',  # Customer payment pages
            'check_payment_status.html',    # Payment status checking
            'payment_status_result.html',   # Payment results
            'admin_billing.html',           # Admin billing interface
            'admin_companies.html',         # Company management
            'company_dashboard.html',       # Company dashboard
            'user_admin_dashboard.html',    # User admin interface
            'analytics_dashboard.html',     # Analytics interface
            'pos_dashboard.html',           # POS system
            'admin_invoices.html',          # Invoice management
            'admin_payments.html',          # Payment management
        ]
    
    def fix_template_aggressively(self, filepath: str) -> bool:
        """Fix a template file with aggressive pattern matching"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            changes_made = 0
            
            # Fix each English word with aggressive pattern matching
            for english, swahili in self.translations.items():
                # Skip if already has conditional translation for this word
                if f"session.language == 'sw'" in content and english in content:
                    continue
                
                # Aggressive pattern matching for all contexts
                patterns = self._get_aggressive_patterns(english, swahili)
                
                for old_pattern, new_pattern in patterns:
                    if old_pattern in content and not self._is_inside_conditional(content, old_pattern):
                        content = content.replace(old_pattern, new_pattern)
                        changes_made += 1
            
            # Only write if changes were made
            if content != original_content:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Fixed {changes_made} translations in {filepath}")
                return True
            else:
                print(f"⚪ No changes needed in {filepath}")
                return False
                
        except Exception as e:
            print(f"❌ Error fixing {filepath}: {e}")
            return False
    
    def _get_aggressive_patterns(self, english: str, swahili: str) -> List[Tuple[str, str]]:
        """Get aggressive patterns for maximum coverage"""
        return [
            # HTML content patterns - more aggressive
            (f'>{english}<', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}<'),
            (f'>{english}</button>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</button>'),
            (f'>{english}</a>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</a>'),
            (f'>{english}</h1>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h1>'),
            (f'>{english}</h2>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h2>'),
            (f'>{english}</h3>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h3>'),
            (f'>{english}</h4>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h4>'),
            (f'>{english}</h5>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h5>'),
            (f'>{english}</h6>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</h6>'),
            (f'>{english}</span>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</span>'),
            (f'>{english}</div>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</div>'),
            (f'>{english}</p>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</p>'),
            (f'>{english}</label>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</label>'),
            (f'>{english}</th>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</th>'),
            (f'>{english}</td>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</td>'),
            (f'>{english}</li>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</li>'),
            (f'>{english}</option>', f'>{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}</option>'),
            
            # Attribute patterns
            (f'title="{english}"', f'title="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
            (f"title='{english}'", f"title='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
            (f'placeholder="{english}"', f'placeholder="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
            (f"placeholder='{english}'", f"placeholder='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
            (f'alt="{english}"', f'alt="{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
            (f"alt='{english}'", f"alt='{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
            
            # Comment patterns
            (f'<!-- {english}', f'<!-- {{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'),
            
            # Standalone text patterns (more aggressive)
            (f' {english} ', f' {{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}} '),
            (f'"{english}"', f'"{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}"'),
            (f"'{english}'", f"'{{%- if session.language == \"sw\" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'"),
            
            # Word boundary patterns for better matching
            (f'\\b{english}\\b', f'{{%- if session.language == "sw" -%}}{swahili}{{%- else -%}}{english}{{%- endif -%}}'),
        ]
    
    def _is_inside_conditional(self, content: str, pattern: str) -> bool:
        """Check if pattern is already inside a conditional translation block"""
        pattern_index = content.find(pattern)
        if pattern_index == -1:
            return False
        
        # Look backwards for conditional blocks
        before_pattern = content[:pattern_index]
        conditional_start = before_pattern.rfind('{% if session.language')
        conditional_end = before_pattern.rfind('{% endif %}')
        
        # If we found a conditional start after the last conditional end, we're inside one
        return conditional_start > conditional_end
    
    def fix_priority_templates(self) -> Dict[str, int]:
        """Fix priority templates first"""
        results = {'fixed': 0, 'skipped': 0, 'errors': 0}
        
        print(f"🎯 Starting PRIORITY translation fix for {len(self.priority_templates)} high-impact templates...")
        print("=" * 80)
        
        for template_name in self.priority_templates:
            template_path = f'templates/{template_name}'
            if os.path.exists(template_path):
                try:
                    if self.fix_template_aggressively(template_path):
                        results['fixed'] += 1
                    else:
                        results['skipped'] += 1
                except Exception as e:
                    print(f"❌ Error processing {template_path}: {e}")
                    results['errors'] += 1
            else:
                print(f"⚠️ Template not found: {template_path}")
        
        return results
    
    def fix_all_remaining_templates(self) -> Dict[str, int]:
        """Fix all remaining templates"""
        results = {'fixed': 0, 'skipped': 0, 'errors': 0}
        
        template_files = glob.glob('templates/*.html')
        remaining_templates = [f for f in template_files if os.path.basename(f) not in self.priority_templates]
        
        print(f"\n🔧 Starting REMAINING template fix for {len(remaining_templates)} templates...")
        print("=" * 80)
        
        for template_file in remaining_templates:
            try:
                if self.fix_template_aggressively(template_file):
                    results['fixed'] += 1
                else:
                    results['skipped'] += 1
            except Exception as e:
                print(f"❌ Error processing {template_file}: {e}")
                results['errors'] += 1
        
        return results

def main():
    """Main function to run the priority translation fixer"""
    print("🌍 EXLIPA PRIORITY TRANSLATION FIXER")
    print("=" * 50)
    print("🎯 GOAL: Complete high-impact template translations")
    print("=" * 50)
    
    fixer = PriorityTranslationFixer()
    
    # Fix priority templates first
    print("\n🎯 PHASE 1: High-Impact Templates")
    priority_results = fixer.fix_priority_templates()
    
    # Fix remaining templates
    print("\n🔧 PHASE 2: Remaining Templates")
    remaining_results = fixer.fix_all_remaining_templates()
    
    # Combined results
    total_fixed = priority_results['fixed'] + remaining_results['fixed']
    total_skipped = priority_results['skipped'] + remaining_results['skipped']
    total_errors = priority_results['errors'] + remaining_results['errors']
    
    # Summary
    print("\n" + "=" * 80)
    print("🎉 PRIORITY TRANSLATION FIX COMPLETE!")
    print(f"✅ Fixed: {total_fixed} files")
    print(f"⚪ Skipped: {total_skipped} files")
    print(f"❌ Errors: {total_errors} files")
    print("\n🌍 EXLIPA now has ENHANCED Swahili translation support!")
    print("🎯 High-impact templates are now fully bilingual!")

if __name__ == '__main__':
    main()
