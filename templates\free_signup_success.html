{% extends "base.html" %}

{% block title %}
{% if session.language == 'sw' %}Akaunti Imeundwa - EXLIPA{% else %}Account Created - EXLIPA{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="glass-card p-5 text-center">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-4x text-success"></i>
                </div>
                
                <h2 class="fw-bold gradient-text-success mb-3">
                    🎉 {% if session.language == 'sw' %}Hongera!{% else %}Congratulations!{% endif %}
                </h2>
                
                <h4 class="mb-4">
                    {% if session.language == 'sw' %}
                        Akaunti yako ya EXLIPA imeundwa kwa mafanikio!
                    {% else %}
                        Your EXLIPA account has been created successfully!
                    {% endif %}
                </h4>
                
                <div class="alert alert-success mb-4">
                    <h6 class="fw-bold">
                        {% if session.language == 'sw' %}Taarifa za Akaunti{% else %}Account Details{% endif %}
                    </h6>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <strong>
                                {% if session.language == 'sw' %}Kampuni{% else %}Company{% endif %}:
                            </strong> {{ company_name }}
                        </div>
                        <div class="col-md-6">
                            <strong>
                                {% if session.language == 'sw' %}Barua Pepe{% else %}Email{% endif %}:
                            </strong> {{ email }}
                        </div>
                        <div class="col-md-6">
                            <strong>
                                {% if session.language == 'sw' %}Kuingia{% else %}Login with{% endif %}:
                            </strong> {{ email }}
                        </div>
                        <div class="col-md-6">
                            <strong>
                                {% if session.language == 'sw' %}Mpango{% else %}Plan{% endif %}:
                            </strong> 
                            <span class="badge bg-success">
                                {% if session.language == 'sw' %}Bure{% else %}Free{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>
                        {% if session.language == 'sw' %}Akaunti Yako Imeundwa{% else %}Your Account is Ready{% endif %}
                    </strong><br>
                    {% if session.language == 'sw' %}
                        Unaweza kuingia moja kwa moja kwa kutumia barua pepe yako <strong>{{ email }}</strong> na nenosiri ulilochagua
                    {% else %}
                        You can login immediately using your email <strong>{{ email }}</strong> and the password you created
                    {% endif %}
                </div>
                
                <!-- What's Next -->
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <div class="border rounded p-3 h-100">
                            <i class="fas fa-sign-in-alt fa-2x text-primary mb-2"></i>
                            <h6 class="fw-bold">
                                {% if session.language == 'sw' %}1. Ingia{% else %}1. Login{% endif %}
                            </h6>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}
                                    Tumia barua pepe yako na nenosiri ulilochagua
                                {% else %}
                                    Use your email and the password you created
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 h-100">
                            <i class="fas fa-cog fa-2x text-warning mb-2"></i>
                            <h6 class="fw-bold">
                                {% if session.language == 'sw' %}2. Sanidi{% else %}2. Setup{% endif %}
                            </h6>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}
                                    Kamilisha mipangilio ya kampuni yako
                                {% else %}
                                    Complete your company setup
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 h-100">
                            <i class="fas fa-rocket fa-2x text-success mb-2"></i>
                            <h6 class="fw-bold">
                                {% if session.language == 'sw' %}3. Anza{% else %}3. Start{% endif %}
                            </h6>
                            <small class="text-muted">
                                {% if session.language == 'sw' %}
                                    Anza kupokea malipo mara moja!
                                {% else %}
                                    Start accepting payments immediately!
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Free Plan Benefits -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="fw-bold text-success mb-3">
                            {% if session.language == 'sw' %}Faida za Mpango wa Bure{% else %}Free Plan Benefits{% endif %}
                        </h5>
                        <div class="row g-2">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>
                                        {% if session.language == 'sw' %}Miamala 100 ya bure kila mwezi{% else %}100 free transactions monthly{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>
                                        {% if session.language == 'sw' %}Malipo ya simu{% else %}Mobile money payments{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>
                                        {% if session.language == 'sw' %}Dashibodi ya msingi{% else %}Basic dashboard{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>
                                        {% if session.language == 'sw' %}Msaada wa 24/7{% else %}24/7 support{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex flex-wrap gap-3 justify-content-center">
                    <a href="{{ url_for('user_admin_login') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        {% if session.language == 'sw' %}Ingia Sasa{% else %}Login Now{% endif %}
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-home me-2"></i>
                        {% if session.language == 'sw' %}Rudi Nyumbani{% else %}Back to Home{% endif %}
                    </a>
                </div>
                
                <!-- Upgrade Notice -->
                <div class="mt-5">
                    <div class="alert alert-light border">
                        <h6 class="fw-bold">
                            {% if session.language == 'sw' %}Unahitaji Zaidi?{% else %}Need More?{% endif %}
                        </h6>
                        <p class="mb-2">
                            {% if session.language == 'sw' %}
                                Boresha kwenda Business au Enterprise kwa vipengele zaidi na miamala isiyo na kikomo
                            {% else %}
                                Upgrade to Business or Enterprise for more features and unlimited transactions
                            {% endif %}
                        </p>
                        <a href="{{ url_for('pricing') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-arrow-up me-1"></i>
                            {% if session.language == 'sw' %}Ona Vipimo{% else %}View Plans{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-redirect to login after 30 seconds
setTimeout(function() {
    if (confirm('{% if session.language == "sw" %}Je, unataka kwenda kwenye ukurasa wa kuingia?{% else %}Would you like to go to the login page?{% endif %}')) {
        window.location.href = '{{ url_for("user_admin_login") }}';
    }
}, 30000);

// Confetti animation
function createConfetti() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
    
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${colors[Math.floor(Math.random() * colors.length)]};
            left: ${Math.random() * 100}vw;
            top: -10px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: fall ${Math.random() * 3 + 2}s linear forwards;
        `;
        
        document.body.appendChild(confetti);
        
        setTimeout(() => {
            confetti.remove();
        }, 5000);
    }
}

// Add confetti animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        to {
            transform: translateY(100vh) rotate(360deg);
        }
    }
`;
document.head.appendChild(style);

// Trigger confetti on page load
setTimeout(createConfetti, 500);
</script>
{% endblock %}
