#!/usr/bin/env python3
"""
Create useradmin user
"""

from app import app, db, User
from werkzeug.security import generate_password_hash

def create_useradmin():
    with app.app_context():
        # Remove existing useradmin
        existing = User.query.filter_by(username='useradmin').first()
        if existing:
            db.session.delete(existing)
            db.session.commit()
            print("Removed existing useradmin")
        
        # Create new useradmin
        useradmin = User(
            username='useradmin',
            password_hash=generate_password_hash('useradmin123'),
            role='admin',
            is_active=True
        )
        
        db.session.add(useradmin)
        db.session.commit()
        
        print("Created useradmin user")
        print("Username: useradmin")
        print("Password: useradmin123")
        print("Role: admin")
        
        # Verify
        test_user = User.query.filter_by(username='useradmin').first()
        if test_user:
            print(f"Verification: User exists with role '{test_user.role}' and active status: {test_user.is_active}")
        else:
            print("Verification failed")

if __name__ == '__main__':
    create_useradmin()
