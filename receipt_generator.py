from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import io

def generate_receipt_pdf(payment_data, company_info=None):
    """
    Generate a professional PDF receipt for a confirmed payment.
    
    Args:
        payment_data: Dictionary containing payment information
        company_info: Dictionary containing company information
    
    Returns:
        BytesIO object containing the PDF data
    """
    
    # Default company information
    if not company_info:
        company_info = {
            'name': 'Your Company Name Ltd',
            'address': 'P.O. Box 12345, Dar es Salaam, Tanzania',
            'phone': '+255 XXX XXX XXX',
            'email': '<EMAIL>',
            'website': 'www.yourcompany.com',
            'tin': 'TIN: ***********'
        }
    
    # Create PDF buffer
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=18)
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        textColor=colors.darkblue,
        alignment=TA_CENTER,
        spaceAfter=30
    )
    
    company_style = ParagraphStyle(
        'Company',
        parent=styles['Normal'],
        fontSize=12,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        spaceAfter=20
    )
    
    receipt_info_style = ParagraphStyle(
        'ReceiptInfo',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_RIGHT,
        spaceAfter=20
    )
    
    # Build document content
    content = []
    
    # Company Header
    content.append(Paragraph(f"<b>{company_info['name']}</b>", company_style))
    content.append(Paragraph(company_info['address'], company_style))
    content.append(Paragraph(f"Phone: {company_info['phone']} | Email: {company_info['email']}", company_style))
    content.append(Paragraph(f"Website: {company_info['website']} | {company_info['tin']}", company_style))
    
    # Add space
    content.append(Spacer(1, 20))
    
    # Receipt Title
    content.append(Paragraph("<b>PAYMENT RECEIPT</b>", title_style))
    
    # Receipt Information (right-aligned)
    receipt_date = datetime.now().strftime('%d/%m/%Y')
    receipt_time = datetime.now().strftime('%H:%M')
    receipt_number = f"RCP{payment_data['id']:06d}"
    
    receipt_info = f"""
    <b>Receipt No:</b> {receipt_number}<br/>
    <b>Date:</b> {receipt_date}<br/>
    <b>Time:</b> {receipt_time}
    """
    content.append(Paragraph(receipt_info, receipt_info_style))
    
    # Add space
    content.append(Spacer(1, 20))
    
    # Payment Details Table
    payment_details = [
        ['Description', 'Details'],
        ['Customer Name', payment_data['customer_name']],
        ['Reference ID', f"REF{payment_data['id']:06d}"],
        ['Amount Paid', f"TZS {payment_data['amount']:,.0f}"],
        ['Mobile Money Operator', payment_data['mobile_operator']],
        ['Transaction ID', payment_data['transaction_id']],
        ['Payment Date', payment_data['submitted_at'].strftime('%d/%m/%Y')],
        ['Payment Status', 'CONFIRMED'],
    ]
    
    # Add service description if available
    if payment_data.get('service_description'):
        payment_details.append(['Service/Product', payment_data['service_description']])
    
    # Create table
    table = Table(payment_details, colWidths=[2.5*inch, 4*inch])
    table.setStyle(TableStyle([
        # Header style
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        
        # Body style
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        
        # Grid and borders
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        
        # Highlight amount row
        ('BACKGROUND', (0, 3), (-1, 3), colors.lightgreen),
        ('FONTNAME', (0, 3), (-1, 3), 'Helvetica-Bold'),
        
        # Highlight status row
        ('BACKGROUND', (0, 7), (-1, 7), colors.lightblue),
        ('FONTNAME', (0, 7), (-1, 7), 'Helvetica-Bold'),
    ]))
    
    content.append(table)
    
    # Add space
    content.append(Spacer(1, 30))
    
    # Thank you message
    thank_you_style = ParagraphStyle(
        'ThankYou',
        parent=styles['Normal'],
        fontSize=12,
        alignment=TA_CENTER,
        textColor=colors.darkgreen,
        spaceAfter=20
    )
    
    content.append(Paragraph("<b>Thank you for your payment!</b>", thank_you_style))
    content.append(Paragraph("This receipt serves as confirmation of your successful payment.", styles['Normal']))
    
    # Add space
    content.append(Spacer(1, 20))
    
    # Footer information
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        alignment=TA_CENTER,
        textColor=colors.grey,
        spaceAfter=10
    )
    
    content.append(Paragraph("This is a computer-generated receipt and does not require a signature.", footer_style))
    content.append(Paragraph(f"Generated on {receipt_date} at {receipt_time}", footer_style))
    
    # Build PDF
    doc.build(content)
    
    # Get PDF data
    pdf_data = buffer.getvalue()
    buffer.close()
    
    return pdf_data

def generate_receipt_filename(payment_id, customer_name):
    """Generate a standardized filename for the receipt"""
    # Clean customer name for filename
    clean_name = "".join(c for c in customer_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    clean_name = clean_name.replace(' ', '_')
    
    date_str = datetime.now().strftime('%Y%m%d')
    return f"Receipt_REF{payment_id:06d}_{clean_name}_{date_str}.pdf"
