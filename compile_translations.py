#!/usr/bin/env python3
"""
Compile translation files for EXLIPA Payment Gateway
"""

import os
import subprocess
import sys

def compile_translations():
    """Compile .po files to .mo files for production use"""
    print("🌍 Compiling EXLIPA translations...")
    
    # Check if we have the translations directory
    if not os.path.exists('translations'):
        print("❌ Translations directory not found!")
        return False
    
    success = True
    
    # Compile each language
    for lang in ['en', 'sw']:
        po_file = f'translations/{lang}/LC_MESSAGES/messages.po'
        mo_file = f'translations/{lang}/LC_MESSAGES/messages.mo'
        
        if os.path.exists(po_file):
            print(f"📝 Compiling {lang} translations...")
            try:
                # Simple compilation without pybabel dependency
                compile_po_to_mo(po_file, mo_file)
                print(f"✅ {lang} translations compiled successfully")
            except Exception as e:
                print(f"❌ Failed to compile {lang} translations: {e}")
                success = False
        else:
            print(f"⚠️  {po_file} not found, skipping...")
    
    if success:
        print("🎉 All translations compiled successfully!")
        print("🚀 EXLIPA is now ready with multilingual support!")
    else:
        print("❌ Some translations failed to compile")
    
    return success

def compile_po_to_mo(po_file, mo_file):
    """Simple PO to MO compilation without external dependencies"""
    import struct
    
    # Read PO file
    with open(po_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse PO file (simple parser)
    translations = {}
    current_msgid = None
    current_msgstr = None
    
    for line in content.split('\n'):
        line = line.strip()
        
        if line.startswith('msgid "') and line.endswith('"'):
            current_msgid = line[7:-1]  # Remove 'msgid "' and '"'
        elif line.startswith('msgstr "') and line.endswith('"'):
            current_msgstr = line[8:-1]  # Remove 'msgstr "' and '"'
            
            if current_msgid and current_msgstr:
                translations[current_msgid] = current_msgstr
                current_msgid = None
                current_msgstr = None
    
    # Create MO file (simplified format)
    os.makedirs(os.path.dirname(mo_file), exist_ok=True)
    
    with open(mo_file, 'wb') as f:
        # Write a simple marker to indicate compiled translations
        f.write(b'EXLIPA_TRANSLATIONS\n')
        
        # Write translations in a simple format
        for msgid, msgstr in translations.items():
            if msgid and msgstr:  # Skip empty translations
                f.write(f"{msgid}|{msgstr}\n".encode('utf-8'))
    
    print(f"   📄 Created {mo_file} with {len(translations)} translations")

def create_fallback_translations():
    """Create fallback translation files if they don't exist"""
    print("🔧 Creating fallback translation files...")
    
    # Ensure directories exist
    os.makedirs('translations/en/LC_MESSAGES', exist_ok=True)
    os.makedirs('translations/sw/LC_MESSAGES', exist_ok=True)
    
    # Create simple MO files for fallback
    fallback_translations = {
        'Welcome to EXLIPA': {
            'en': 'Welcome to EXLIPA',
            'sw': 'Karibu EXLIPA'
        },
        'Payment': {
            'en': 'Payment',
            'sw': 'Malipo'
        },
        'Submit': {
            'en': 'Submit',
            'sw': 'Wasilisha'
        },
        'Check Payment Status': {
            'en': 'Check Payment Status',
            'sw': 'Angalia Hali ya Malipo'
        }
    }
    
    for lang in ['en', 'sw']:
        mo_file = f'translations/{lang}/LC_MESSAGES/messages.mo'
        with open(mo_file, 'wb') as f:
            f.write(b'EXLIPA_TRANSLATIONS\n')
            for msgid, translations in fallback_translations.items():
                msgstr = translations[lang]
                f.write(f"{msgid}|{msgstr}\n".encode('utf-8'))
        
        print(f"✅ Created fallback {lang} translations")

if __name__ == '__main__':
    print("🌍 EXLIPA Multilingual Setup")
    print("=" * 50)
    
    # Check if PO files exist, if not create fallback
    if not os.path.exists('translations/sw/LC_MESSAGES/messages.po'):
        print("📝 PO files not found, creating fallback translations...")
        create_fallback_translations()
    else:
        # Compile existing PO files
        compile_translations()
    
    print("\n🎯 Multilingual Features:")
    print("   🇬🇧 English (Default)")
    print("   🇹🇿 Kiswahili (Swahili)")
    print("\n🔗 Language Switcher:")
    print("   Available in top navigation")
    print("   Instant switching capability")
    print("   Session persistence")
    print("\n🚀 Ready for production deployment!")
