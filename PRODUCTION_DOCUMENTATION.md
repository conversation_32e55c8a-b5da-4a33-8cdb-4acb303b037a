# 🇹🇿 EXLIPA Payment Gateway - Production Documentation

## 📋 **System Overview**

EXLIPA is a production-ready payment gateway system specifically designed for Tanzania's mobile money ecosystem. It features SMS-based payment verification, enterprise-grade security, and multi-tenant architecture.

---

## 🚀 **Key Features & Capabilities**

### **Payment Processing**
- ✅ **Mobile Money Integration**: M-Pesa, Tigo Pesa, Airtel Money, CRDB Lipa
- ✅ **SMS-Based Verification**: Manual verification workflow for enhanced security
- ✅ **Transaction Limits**: TZS 1,000 minimum, TZS 10,000,000 maximum
- ✅ **Fraud Prevention**: Mobile money name verification and duplicate detection
- ✅ **Real-Time Processing**: Instant payment confirmation and status tracking

### **Security Architecture**
- 🔒 **Role-Based Authentication**: Master Admin, User Admin, Company User
- 🔒 **Account Security**: 5 failed attempts = 15-minute lockout
- 🔒 **Rate Limiting**: Protection against brute force and DDoS attacks
- 🔒 **Input Sanitization**: XSS and SQL injection prevention
- 🔒 **Transaction Security**: Unique transaction IDs and amount validation
- 🔒 **Data Encryption**: Secure password hashing and session management

### **Business Management**
- 🏢 **Multi-Tenant Architecture**: Complete data isolation per business
- 🏢 **Company Landing Pages**: Branded payment portals with custom styling
- 🏢 **POS System**: Point-of-sale with real-time inventory management
- 🏢 **Cash Drawer Management**: Secure cash handling with complete audit trails
- 🏢 **Subscription Tiers**: Starter, Business, and Enterprise packages

### **User Experience**
- 📱 **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- 📧 **Professional Communications**: Automated email templates and notifications
- 📊 **Status Tracking**: Real-time payment status checker for customers
- 🎨 **Custom Branding**: Company logos, colors, and personalized messaging

---

## 🛠 **Technical Architecture**

### **Backend Stack**
- **Framework**: Python 3.8+ with Flask
- **Database**: SQLite (development) / PostgreSQL (production)
- **Authentication**: Flask-Login with Werkzeug password hashing
- **Email**: Flask-Mail with SMTP support
- **PDF Generation**: ReportLab for receipts and reports
- **Security**: Input validation, rate limiting, CSRF protection

### **Frontend Stack**
- **UI Framework**: Bootstrap 5 with responsive design
- **Languages**: HTML5, CSS3, JavaScript (ES6+)
- **Icons**: Font Awesome for professional iconography
- **Charts**: Chart.js for analytics and reporting
- **Mobile**: Progressive Web App (PWA) capabilities

### **Database Schema**
```sql
-- Core user management with security features
User: id, username, password_hash, email, role, failed_login_attempts, account_locked_until

-- Payment processing with SMS verification
PaymentConfirmation: id, customer_name, customer_email, mobile_money_sender_name, 
                    amount, mobile_operator, transaction_id, sms_verification_data

-- Multi-tenant company management
ClientCompany: id, company_name, owner_user_id, branding_settings, payment_methods

-- POS system with inventory tracking
PosProduct: id, company_id, name, price, stock_quantity, track_inventory
PosSale: id, company_id, total_amount, payment_method, sale_items
```

---

## 📦 **Installation & Deployment**

### **Development Environment**
```bash
# Clone repository
git clone https://github.com/your-org/exlipa-payment-gateway.git
cd exlipa-payment-gateway

# Set up virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Initialize secure database
python initialize_secure_database.py

# Run development server
python app.py
```

### **Production Deployment**
```bash
# Install production server
sudo apt update && sudo apt upgrade -y
sudo apt install python3 python3-pip nginx postgresql redis-server

# Install Python dependencies
pip install gunicorn supervisor psycopg2-binary

# Configure environment
cp .env.example .env
# Edit .env with production settings

# Set up database
sudo -u postgres createdb exlipa_prod
python initialize_secure_database.py

# Configure Nginx
sudo cp nginx.conf /etc/nginx/sites-available/exlipa
sudo ln -s /etc/nginx/sites-available/exlipa /etc/nginx/sites-enabled/
sudo systemctl restart nginx

# Start application with Gunicorn
gunicorn -w 4 -b 127.0.0.1:8000 app:app
```

### **Environment Configuration**
```env
# Security Settings
SECRET_KEY=your-super-secure-production-key-here
FLASK_ENV=production

# Database Configuration
SQLALCHEMY_DATABASE_URI=postgresql://exlipa_user:secure_password@localhost/exlipa_prod

# Email Settings
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-specific-password
MAIL_DEFAULT_SENDER=Exlipa Payment Gateway <<EMAIL>>

# Business Configuration
COMPANY_NAME=Exlipa Payment Solutions Ltd
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+*********** 789
BUSINESS_HOURS=8 AM - 6 PM, Monday to Friday
```

---

## 👥 **User Management & Roles**

### **Master Admin (`master_admin`)**
**Capabilities:**
- Complete system administration and configuration
- Payment verification and approval workflow
- User and company management across all tenants
- System monitoring, reporting, and analytics
- Security settings and access control management

**Access URLs:**
- Login: `/admin-login`
- Dashboard: `/master-admin-dashboard`
- Payments: `/admin/payments`

### **User Admin (`user_admin`)**
**Capabilities:**
- Company profile management and branding customization
- Landing page creation and payment method configuration
- POS system access with inventory management
- Team member management within their company
- Business analytics and reporting for their company

**Access URLs:**
- Login: `/login`
- Dashboard: `/user-admin-dashboard`
- Company Settings: `/company-settings`

### **Company User (`company_user`)**
**Capabilities:**
- Basic company dashboard access and daily operations
- POS system for sales processing and inventory updates
- Customer management and payment processing
- Basic reporting and transaction history
- Cash drawer operations and receipt generation

**Access URLs:**
- Login: `/company-login`
- Dashboard: `/company-dashboard`
- POS: `/pos`

---

## 💳 **Payment Processing Workflow**

### **1. Customer Payment Submission**
```mermaid
graph LR
    A[Customer] --> B[Payment Form]
    B --> C[Validation]
    C --> D[SMS Queue]
    D --> E[Reference Number]
```

**Process:**
1. Customer visits company landing page or main payment portal
2. Fills payment form with mobile money registered name
3. System validates amount (TZS 1,000 - 10,000,000) and transaction ID format
4. Payment queued for SMS verification with unique reference number
5. Customer receives confirmation with reference number for tracking

### **2. SMS Verification Process**
```mermaid
graph LR
    A[Admin Receives SMS] --> B[Verify Amount]
    B --> C[Verify Sender Name]
    C --> D[Approve/Reject]
    D --> E[Registration Link]
```

**Process:**
1. Admin receives SMS from mobile money provider (M-Pesa, Tigo, etc.)
2. Admin compares SMS details with customer submission:
   - Amount matches payment form
   - Sender name matches mobile money registered name
   - Transaction ID matches SMS reference
3. Admin approves or rejects payment with verification notes
4. If approved, registration link automatically sent to customer email
5. Complete audit trail maintained for compliance

### **3. Customer Registration & Onboarding**
```mermaid
graph LR
    A[Email Link] --> B[Registration Form]
    B --> C[Account Creation]
    C --> D[Company Setup]
    D --> E[Dashboard Access]
```

**Process:**
1. Customer receives professional email with 24-hour registration link
2. Completes registration form with business details
3. System creates user account and company profile
4. Company dashboard and POS system become available
5. Welcome email sent with getting started guide

---

## 🔒 **Security Implementation**

### **Authentication Security**
```python
# Account lockout after failed attempts
def increment_failed_login(self):
    self.failed_login_attempts += 1
    if self.failed_login_attempts >= 5:
        self.lock_account(15)  # 15-minute lockout

# Rate limiting on login attempts
@simple_rate_limit(max_attempts=5, window_minutes=15)
def login_route():
    # Login processing with rate limiting
```

### **Payment Security**
```python
# Transaction ID uniqueness validation
def validate_transaction_id(transaction_id, mobile_operator):
    existing = PaymentConfirmation.query.filter_by(transaction_id=transaction_id).first()
    if existing:
        raise ValueError("Transaction ID already used")

# Amount validation with limits
def validate_payment_amount(amount):
    if amount < 1000 or amount > ********:
        raise ValueError("Amount outside allowed range")
```

### **Input Sanitization**
```python
# XSS prevention
def sanitize_input(text, max_length=None):
    text = re.sub(r'[<>"\']', '', str(text))
    return text.strip()[:max_length] if max_length else text.strip()
```

---

## 📊 **Monitoring & Analytics**

### **Key Performance Indicators (KPIs)**
- **Payment Success Rate**: Target >95%
- **SMS Verification Time**: Target <4 hours during business hours
- **Registration Conversion**: Target >80% from confirmed payments
- **System Uptime**: Target >99.5%
- **Customer Satisfaction**: Target >90% positive feedback

### **Monitoring Dashboard**
```python
# Real-time metrics tracking
- Total payments processed today
- Pending SMS verifications
- Registration completions
- Active company accounts
- POS transaction volume
- Cash drawer reconciliations
```

### **Alert System**
- **High Priority**: Payment processing failures, security incidents
- **Medium Priority**: SMS verification delays, system performance issues
- **Low Priority**: Registration reminders, maintenance notifications

---

## 🆘 **Support & Troubleshooting**

### **Common Issues & Solutions**

#### **Payment Not Appearing in Admin Dashboard**
**Symptoms**: Customer submitted payment but admin doesn't see it
**Solutions**:
1. Check transaction ID format matches mobile operator requirements
2. Verify mobile money sender name is exact match (case-sensitive)
3. Confirm amount is within limits (TZS 1,000 - 10,000,000)
4. Check for duplicate transaction ID in system

#### **Registration Link Not Received**
**Symptoms**: Customer payment confirmed but no registration email
**Solutions**:
1. Verify email address format and deliverability
2. Check payment status is "Confirmed" not just "Pending"
3. Look in spam/junk folder for email
4. Use admin resend functionality in payment details
5. Check email server configuration and logs

#### **Account Locked Error**
**Symptoms**: User cannot login due to account lockout
**Solutions**:
1. Wait 15 minutes for automatic unlock
2. Admin can manually unlock account in user management
3. Verify correct username and password
4. Check for caps lock or typing errors

#### **POS Inventory Issues**
**Symptoms**: Products showing incorrect stock levels
**Solutions**:
1. Verify inventory tracking is enabled for product
2. Check recent sales for stock deductions
3. Review cash drawer sessions for discrepancies
4. Perform manual inventory adjustment if needed

### **Contact Information**
- **Primary Support**: <EMAIL>
- **Technical Issues**: <EMAIL>
- **Business Hours**: 8 AM - 6 PM, Monday to Friday
- **Emergency Line**: +*********** 789 (24/7 for critical payment issues)
- **WhatsApp Support**: +255 987 654 321

### **Escalation Process**
1. **Level 1**: Customer support team (response within 2 hours)
2. **Level 2**: Technical team (response within 4 hours)
3. **Level 3**: Development team (response within 8 hours)
4. **Emergency**: Immediate response for payment processing failures

---

## 📈 **Business Operations Guide**

### **Daily Operations Checklist**
- [ ] Review pending payment confirmations
- [ ] Process SMS verifications within 4-hour target
- [ ] Monitor system performance and uptime
- [ ] Check customer support tickets
- [ ] Review cash drawer reconciliations
- [ ] Update inventory levels if needed

### **Weekly Operations**
- [ ] Generate payment processing reports
- [ ] Review customer feedback and satisfaction scores
- [ ] Analyze conversion rates and identify improvements
- [ ] Update mobile money till/paybill balances
- [ ] Backup database and system configurations

### **Monthly Operations**
- [ ] Comprehensive security audit and review
- [ ] Performance optimization and system updates
- [ ] Customer success reviews and account health checks
- [ ] Financial reconciliation and reporting
- [ ] Business development and growth planning

---

**© 2024 Exlipa Payment Solutions Ltd. All rights reserved.**
