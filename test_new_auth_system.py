#!/usr/bin/env python3
"""
Test the new authentication system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User
from services.auth_service import AuthenticationManager
from utils.validators import InputValidator, ValidationError
from werkzeug.security import generate_password_hash

def test_authentication_system():
    """Test the new authentication system"""
    print("🧪 Testing New Authentication System...")
    
    with app.app_context():
        # Test 1: Role validation
        print("\n1. Testing role validation...")
        try:
            valid_role = InputValidator.validate_role('super_admin')
            print(f"✅ Valid role: {valid_role}")
            
            invalid_role = InputValidator.validate_role('invalid_role')
            print("❌ Should have failed for invalid role")
        except ValidationError as e:
            print(f"✅ Correctly rejected invalid role: {e}")
        
        # Test 2: Email validation
        print("\n2. Testing email validation...")
        try:
            valid_email = InputValidator.validate_email('<EMAIL>')
            print(f"✅ Valid email: {valid_email}")
            
            invalid_email = InputValidator.validate_email('invalid-email')
            print("❌ Should have failed for invalid email")
        except ValidationError as e:
            print(f"✅ Correctly rejected invalid email: {e}")
        
        # Test 3: Amount validation
        print("\n3. Testing amount validation...")
        try:
            valid_amount = InputValidator.validate_amount('5000.50')
            print(f"✅ Valid amount: {valid_amount}")
            
            invalid_amount = InputValidator.validate_amount('500')  # Below minimum
            print("❌ Should have failed for amount below minimum")
        except ValidationError as e:
            print(f"✅ Correctly rejected invalid amount: {e}")
        
        # Test 4: Authentication Manager
        print("\n4. Testing Authentication Manager...")
        
        # Test role permissions
        has_permission = AuthenticationManager.has_permission('super_admin', 'payments')
        print(f"✅ Super admin has payments permission: {has_permission}")
        
        has_permission = AuthenticationManager.has_permission('company_user', 'company_management')
        print(f"✅ Company user lacks company_management permission: {not has_permission}")
        
        # Test dashboard routing
        dashboard = AuthenticationManager.get_dashboard_for_role('super_admin')
        print(f"✅ Super admin dashboard: {dashboard}")
        
        dashboard = AuthenticationManager.get_dashboard_for_role('user_admin')
        print(f"✅ User admin dashboard: {dashboard}")
        
        # Test 5: Check existing users
        print("\n5. Checking existing users...")
        users = User.query.all()
        for user in users:
            role_info = AuthenticationManager.get_role_info(user.role)
            print(f"✅ User: {user.username}, Role: {user.role}, Description: {role_info.get('description', 'Unknown')}")
        
        print("\n🎉 All authentication tests passed!")

def test_payment_validation():
    """Test payment validation"""
    print("\n🧪 Testing Payment Validation...")
    
    from services.payment_verifier import PaymentValidator, MobileMoneyVerifier
    
    validator = PaymentValidator()
    verifier = MobileMoneyVerifier()
    
    # Test 1: Transaction ID format validation
    print("\n1. Testing transaction ID validation...")
    
    valid_mpesa = verifier.validate_transaction_id_format('AB12345678', 'M-Pesa')
    print(f"✅ Valid M-Pesa transaction ID: {valid_mpesa}")
    
    invalid_mpesa = verifier.validate_transaction_id_format('123', 'M-Pesa')
    print(f"✅ Invalid M-Pesa transaction ID rejected: {not invalid_mpesa}")
    
    # Test 2: Payment submission validation
    print("\n2. Testing payment submission validation...")
    
    valid_payment = {
        'customer_name': 'John Doe',
        'mobile_money_sender_name': 'JOHN DOE',
        'amount': '5000.00',
        'mobile_operator': 'M-Pesa',
        'transaction_id': 'AB12345678'
    }
    
    is_valid, errors = validator.validate_payment_submission(valid_payment)
    print(f"✅ Valid payment submission: {is_valid}, Errors: {errors}")
    
    invalid_payment = {
        'customer_name': '',  # Missing required field
        'amount': '500',  # Below minimum
        'mobile_operator': 'Invalid Operator'
    }
    
    is_valid, errors = validator.validate_payment_submission(invalid_payment)
    print(f"✅ Invalid payment submission rejected: {not is_valid}, Errors: {errors}")
    
    print("\n🎉 All payment validation tests passed!")

def main():
    """Run all tests"""
    try:
        test_authentication_system()
        test_payment_validation()
        print("\n🎉 ALL TESTS PASSED! New authentication system is working correctly!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
