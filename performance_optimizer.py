# EXLIPA Performance Optimization Module

import time
import functools
from datetime import datetime, timedelta
from flask import request, g
import logging

# Performance monitoring
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
        self.slow_queries = []
        self.request_times = []
    
    def track_request_time(self, start_time, endpoint):
        """Track request processing time"""
        duration = time.time() - start_time
        self.request_times.append({
            'endpoint': endpoint,
            'duration': duration,
            'timestamp': datetime.now()
        })
        
        # Keep only last 1000 requests
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
        
        # Log slow requests
        if duration > 2.0:  # Slower than 2 seconds
            logging.warning(f"Slow request: {endpoint} took {duration:.2f}s")
    
    def track_query_time(self, query, duration):
        """Track database query time"""
        if duration > 0.5:  # Slower than 500ms
            self.slow_queries.append({
                'query': str(query)[:200],  # First 200 chars
                'duration': duration,
                'timestamp': datetime.now()
            })
            
            # Keep only last 100 slow queries
            if len(self.slow_queries) > 100:
                self.slow_queries = self.slow_queries[-100:]
    
    def get_performance_stats(self):
        """Get performance statistics"""
        if not self.request_times:
            return {}
        
        recent_requests = [r for r in self.request_times 
                          if r['timestamp'] > datetime.now() - timedelta(hours=1)]
        
        if not recent_requests:
            return {}
        
        durations = [r['duration'] for r in recent_requests]
        
        return {
            'total_requests': len(recent_requests),
            'avg_response_time': sum(durations) / len(durations),
            'max_response_time': max(durations),
            'min_response_time': min(durations),
            'slow_requests': len([d for d in durations if d > 2.0]),
            'slow_queries_count': len(self.slow_queries)
        }

# Global performance monitor
perf_monitor = PerformanceMonitor()

# Caching decorators
def cache_result(timeout=300):
    """Simple in-memory cache decorator"""
    cache = {}
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = f"{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
            
            # Check cache
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < timeout:
                    return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            
            # Clean old cache entries
            current_time = time.time()
            cache_keys_to_remove = [
                k for k, (_, ts) in cache.items() 
                if current_time - ts > timeout
            ]
            for k in cache_keys_to_remove:
                del cache[k]
            
            return result
        return wrapper
    return decorator

# Database query optimization
class QueryOptimizer:
    @staticmethod
    def optimize_company_queries():
        """Optimize common company-related queries"""
        # Add indexes for frequently queried fields
        optimizations = [
            "CREATE INDEX IF NOT EXISTS idx_company_email ON client_company(company_email);",
            "CREATE INDEX IF NOT EXISTS idx_company_tier ON client_company(pricing_tier_id);",
            "CREATE INDEX IF NOT EXISTS idx_payment_company ON payment_confirmation(client_company_id);",
            "CREATE INDEX IF NOT EXISTS idx_payment_status ON payment_confirmation(status);",
            "CREATE INDEX IF NOT EXISTS idx_payment_date ON payment_confirmation(submitted_at);",
            "CREATE INDEX IF NOT EXISTS idx_user_role ON user(role);",
            "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email);",
        ]
        
        from app import db
        for sql in optimizations:
            try:
                db.engine.execute(sql)
                print(f"✅ Applied optimization: {sql[:50]}...")
            except Exception as e:
                print(f"⚠️ Optimization failed: {e}")

# Asset optimization
class AssetOptimizer:
    @staticmethod
    def get_optimized_css():
        """Return minified CSS for critical styles"""
        return """
        :root{--primary-gradient:linear-gradient(135deg,#667eea 0%,#764ba2 100%);--glass-bg:rgba(255,255,255,0.1);--glass-border:rgba(255,255,255,0.2);--shadow-light:0 8px 32px rgba(0,0,0,0.1);--border-radius:15px;--transition:all 0.3s cubic-bezier(0.4,0,0.2,1)}
        body{font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:var(--primary-gradient);min-height:100vh;line-height:1.6}
        .glass-card{background:var(--glass-bg);backdrop-filter:blur(20px);border:1px solid var(--glass-border);border-radius:var(--border-radius);box-shadow:var(--shadow-light);transition:var(--transition)}
        .btn{border-radius:25px;padding:0.75rem 1.5rem;font-weight:500;transition:var(--transition);border:none}
        .btn-primary{background:var(--primary-gradient);box-shadow:0 4px 15px rgba(102,126,234,0.4);color:white}
        """
    
    @staticmethod
    def get_critical_js():
        """Return critical JavaScript for immediate loading"""
        return """
        // Critical performance optimizations
        (function(){
            // Preload critical resources
            const preloadLinks = [
                '/static/css/bootstrap.min.css',
                '/static/css/advanced-ui.css'
            ];
            preloadLinks.forEach(href => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = href;
                document.head.appendChild(link);
            });
            
            // Lazy load non-critical images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        })();
        """

# Request optimization middleware
def optimize_request():
    """Optimize request processing"""
    g.start_time = time.time()
    
    # Enable compression for text responses
    if request.endpoint and 'api' not in request.endpoint:
        # Add cache headers for static content
        if request.path.startswith('/static/'):
            from flask import make_response
            response = make_response()
            response.headers['Cache-Control'] = 'public, max-age=31536000'  # 1 year
            return response

def track_request_performance():
    """Track request performance after processing"""
    if hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        perf_monitor.track_request_time(g.start_time, request.endpoint or 'unknown')

# Database connection optimization
class DatabaseOptimizer:
    @staticmethod
    def optimize_connection_pool():
        """Optimize database connection pool settings"""
        return {
            'pool_size': 10,
            'max_overflow': 20,
            'pool_pre_ping': True,
            'pool_recycle': 3600,  # 1 hour
            'echo': False  # Disable SQL logging in production
        }
    
    @staticmethod
    def get_optimized_queries():
        """Return optimized versions of common queries"""
        return {
            'get_company_with_tier': """
                SELECT c.*, t.name as tier_name, t.monthly_fee, t.transaction_fee_percentage
                FROM client_company c
                LEFT JOIN pricing_tier t ON c.pricing_tier_id = t.id
                WHERE c.id = ?
            """,
            'get_recent_payments': """
                SELECT * FROM payment_confirmation 
                WHERE client_company_id = ? 
                ORDER BY submitted_at DESC 
                LIMIT ?
            """,
            'get_monthly_stats': """
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(amount) as total_amount,
                    AVG(amount) as avg_amount
                FROM payment_confirmation 
                WHERE client_company_id = ? 
                AND submitted_at >= date('now', '-30 days')
            """
        }

# Frontend optimization utilities
class FrontendOptimizer:
    @staticmethod
    def get_preload_hints():
        """Get resource preload hints for better performance"""
        return [
            {'rel': 'preconnect', 'href': 'https://fonts.googleapis.com'},
            {'rel': 'preconnect', 'href': 'https://cdn.jsdelivr.net'},
            {'rel': 'dns-prefetch', 'href': 'https://cdnjs.cloudflare.com'},
            {'rel': 'preload', 'href': '/static/css/advanced-ui.css', 'as': 'style'},
            {'rel': 'preload', 'href': '/static/js/smart-help.js', 'as': 'script'}
        ]
    
    @staticmethod
    def get_critical_css():
        """Get critical CSS that should be inlined"""
        return AssetOptimizer.get_optimized_css()
    
    @staticmethod
    def should_defer_script(script_path):
        """Determine if a script should be deferred"""
        non_critical_scripts = [
            'smart-help.js',
            'analytics.js',
            'charts.js'
        ]
        return any(script in script_path for script in non_critical_scripts)

# Memory optimization
class MemoryOptimizer:
    @staticmethod
    def cleanup_old_data():
        """Clean up old data to free memory"""
        from app import db, PaymentConfirmation
        from datetime import datetime, timedelta
        
        # Archive old payment confirmations (older than 1 year)
        cutoff_date = datetime.now() - timedelta(days=365)
        old_payments = PaymentConfirmation.query.filter(
            PaymentConfirmation.submitted_at < cutoff_date
        ).count()
        
        if old_payments > 0:
            print(f"Found {old_payments} old payment records for archival")
        
        return old_payments
    
    @staticmethod
    def optimize_session_storage():
        """Optimize session storage"""
        return {
            'SESSION_PERMANENT': False,
            'PERMANENT_SESSION_LIFETIME': timedelta(hours=2),
            'SESSION_COOKIE_SECURE': True,
            'SESSION_COOKIE_HTTPONLY': True,
            'SESSION_COOKIE_SAMESITE': 'Lax'
        }

# Performance testing utilities
class PerformanceTester:
    @staticmethod
    def run_performance_test():
        """Run basic performance tests"""
        tests = []
        
        # Test database connection
        start = time.time()
        from app import db
        db.engine.execute("SELECT 1")
        db_time = time.time() - start
        tests.append(('Database Connection', db_time, db_time < 0.1))
        
        # Test cache performance
        start = time.time()
        @cache_result(timeout=60)
        def test_cache():
            return "cached_result"
        
        test_cache()  # First call
        test_cache()  # Cached call
        cache_time = time.time() - start
        tests.append(('Cache Performance', cache_time, cache_time < 0.01))
        
        return tests

# Export main functions
__all__ = [
    'PerformanceMonitor',
    'perf_monitor',
    'cache_result',
    'QueryOptimizer',
    'AssetOptimizer',
    'DatabaseOptimizer',
    'FrontendOptimizer',
    'MemoryOptimizer',
    'PerformanceTester',
    'optimize_request',
    'track_request_performance'
]
