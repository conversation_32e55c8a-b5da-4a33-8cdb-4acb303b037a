{% extends "base.html" %}

{%- block title -%}
{%- if session.language == 'sw' -%}
    Set New Nywila - Exlipa
{%- else -%}
    Set New Password - Exlipa
{%- endif -%}
{%- endblock -%}

{% block content %}
<div class="row">
    <div class="col-md-4 mx-auto">
        <div class="card mt-4">
            <div class="card-header text-center">
                <h4><i class="fas fa-unlock-alt me-2"></i>
                    {% if session.language == 'sw' %}
                        Weka Nenosiri Jipya
                    {% else %}
                        Set New Password
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            {% if session.language == 'sw' %}
                                Nenosiri Jipya
                            {% else %}
                                New Password
                            {% endif %}
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ t('Confirm Password') }}</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            {% if session.language == 'sw' %}
                                Weka Nenosiri
                            {% else %}
                                Set Password
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="text-center mt-3">
            <a href="{{ url_for('user_admin_login') }}" class="text-decoration-none">
                {% if session.language == 'sw' %}
                    Rudi kwenye Kuingia
                {% else %}
                    Back to Login
                {% endif %}
            </a>
        </div>
    </div>
</div>
{% endblock %}
