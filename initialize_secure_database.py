#!/usr/bin/env python3
"""
Initialize database with security fixes and test data
"""

from app import app, db, User, PaymentConfirmation, ClientCompany, PricingTier
from werkzeug.security import generate_password_hash
import os

def initialize_database():
    """Initialize database with all security fixes"""
    with app.app_context():
        print("=== INITIALIZING SECURE DATABASE ===")
        
        # Drop and recreate all tables
        print("1. Recreating database tables...")
        db.drop_all()
        db.create_all()
        print("   ✅ Database tables created")
        
        # Create pricing tiers
        print("2. Creating pricing tiers...")
        tiers = [
            PricingTier(
                name="Starter",
                description="Perfect for small businesses",
                setup_fee=15000.0,
                monthly_fee=25000.0,
                max_transactions=100,
                features="Basic payment processing, Email support, Mobile money integration"
            ),
            PricingTier(
                name="Business",
                description="Ideal for growing companies",
                setup_fee=30000.0,
                monthly_fee=50000.0,
                max_transactions=500,
                features="Advanced analytics, Priority support, Custom branding, POS integration"
            ),
            PricingTier(
                name="Enterprise",
                description="For large organizations",
                setup_fee=50000.0,
                monthly_fee=100000.0,
                max_transactions=2000,
                features="Unlimited transactions, 24/7 support, API access, White-label solution"
            )
        ]
        
        for tier in tiers:
            db.session.add(tier)
        
        db.session.commit()
        print("   ✅ Created 3 pricing tiers")
        
        # Create admin users with new role names
        print("3. Creating admin users...")
        
        # Master admin
        master_admin = User(
            username='admin',
            password_hash=generate_password_hash('admin123'),
            email='<EMAIL>',
            full_name='Master Administrator',
            role='master_admin',
            is_active=True
        )
        db.session.add(master_admin)
        
        # User admin
        user_admin = User(
            username='useradmin',
            password_hash=generate_password_hash('useradmin123'),
            email='<EMAIL>',
            full_name='User Administrator',
            role='user_admin',
            is_active=True
        )
        db.session.add(user_admin)
        
        # Company user
        company_user = User(
            username='juma',
            password_hash=generate_password_hash('juma123'),
            email='<EMAIL>',
            full_name='Juma Mohamed',
            role='company_user',
            is_active=True
        )
        db.session.add(company_user)
        
        db.session.commit()
        print("   ✅ Created admin users with new role hierarchy")
        
        # Create a test company
        print("4. Creating test company...")
        
        company = ClientCompany(
            company_name='Tech Solutions Ltd',
            company_address='Dar es Salaam, Tanzania',
            company_phone='+*********** 789',
            company_email='<EMAIL>',
            company_website='https://techsolutions.co.tz',
            company_tin='***********',
            owner_user_id=company_user.id,  # Proper ownership
            pricing_tier_id=2,  # Business tier
            mpesa_till='123456',
            tigo_paybill='654321',
            airtel_merchant='789012',
            landing_page_title='Pay Tech Solutions',
            landing_page_description='Secure payment portal for Tech Solutions Ltd services',
            is_public_page_enabled=True,
            created_by=company_user.id
        )
        db.session.add(company)
        db.session.commit()
        print("   ✅ Created test company with proper ownership")
        
        # Create test payment confirmations with SMS verification fields
        print("5. Creating test payment confirmations...")
        
        # Confirmed payment with SMS verification
        confirmed_payment = PaymentConfirmation(
            customer_name='John Doe',
            customer_email='<EMAIL>',
            mobile_money_sender_name='John Doe',
            amount=50000.0,
            mobile_operator='M-Pesa',
            transaction_id='ND12345678',
            service_description='Business tier signup',
            status='Confirmed',
            sms_sender='MPESA',
            sms_amount=50000.0,
            sms_sender_name='John Doe',
            sms_transaction_ref='ND12345678',
            admin_verification_notes='SMS verified successfully',
            processed_by=master_admin.id
        )
        db.session.add(confirmed_payment)
        
        # Pending payment awaiting verification
        pending_payment = PaymentConfirmation(
            customer_name='Mary Smith',
            customer_email='<EMAIL>',
            mobile_money_sender_name='Mary Smith',
            amount=25000.0,
            mobile_operator='TigoPesa',
            transaction_id='TP87654321',
            service_description='Starter tier signup',
            status='Pending'
        )
        db.session.add(pending_payment)
        
        # Company payment
        company_payment = PaymentConfirmation(
            client_company_id=company.id,
            customer_name='Customer ABC',
            mobile_money_sender_name='Customer ABC',
            amount=15000.0,
            mobile_operator='Airtel Money',
            transaction_id='AM11223344',
            service_description='Service payment',
            status='Pending'
        )
        db.session.add(company_payment)
        
        db.session.commit()
        print("   ✅ Created test payment confirmations")
        
        print()
        print("🎉 SECURE DATABASE INITIALIZATION COMPLETE!")
        print()
        
        # Display summary
        print("📊 SUMMARY:")
        print(f"   👑 Master admins: {User.query.filter_by(role='master_admin').count()}")
        print(f"   👤 User admins: {User.query.filter_by(role='user_admin').count()}")
        print(f"   🏢 Company users: {User.query.filter_by(role='company_user').count()}")
        print(f"   💰 Pricing tiers: {PricingTier.query.count()}")
        print(f"   🏢 Companies: {ClientCompany.query.count()}")
        print(f"   💳 Payment confirmations: {PaymentConfirmation.query.count()}")
        print()
        
        print("🔐 LOGIN CREDENTIALS:")
        print("   Master Admin: admin / admin123")
        print("   User Admin: useradmin / useradmin123")
        print("   Company User: juma / juma123")
        print()
        
        print("🌐 TEST URLS:")
        print("   Master Admin: http://localhost:5000/admin-login")
        print("   User Admin: http://localhost:5000/login")
        print("   Company User: http://localhost:5000/company-login")
        print("   Company Landing: http://localhost:5000/company/1")
        print("   Payment Status: http://localhost:5000/check-payment-status")
        print()
        
        print("✅ SECURITY FEATURES ENABLED:")
        print("   🔒 Rate limiting on login attempts")
        print("   🔒 Account lockout after failed attempts")
        print("   🔒 Input validation and sanitization")
        print("   🔒 Transaction ID uniqueness")
        print("   🔒 Payment amount validation")
        print("   🔒 Mobile money name verification")
        print("   🔒 SMS-based payment verification")
        print("   🔒 Proper company ownership")
        print("   🔒 Role-based access control")

if __name__ == '__main__':
    initialize_database()
