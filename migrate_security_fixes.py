#!/usr/bin/env python3
"""
Database migration script for security fixes and new features
"""

from app import app, db, User, PaymentConfirmation, ClientCompany
from sqlalchemy import text
import sys

def migrate_database():
    """Apply all necessary database migrations"""
    with app.app_context():
        print("=== EXLIPA SECURITY MIGRATION ===")
        print()
        
        try:
            # 1. Add new columns to User table
            print("1. Updating User table...")
            
            # Check if columns exist before adding
            inspector = db.inspect(db.engine)
            user_columns = [col['name'] for col in inspector.get_columns('user')]
            
            if 'failed_login_attempts' not in user_columns:
                db.engine.execute(text('ALTER TABLE user ADD COLUMN failed_login_attempts INTEGER DEFAULT 0'))
                print("   ✅ Added failed_login_attempts column")
            
            if 'account_locked_until' not in user_columns:
                db.engine.execute(text('ALTER TABLE user ADD COLUMN account_locked_until DATETIME'))
                print("   ✅ Added account_locked_until column")
            
            # Update role names
            print("   🔄 Updating role names...")
            db.engine.execute(text("UPDATE user SET role = 'master_admin' WHERE role = 'super_admin'"))
            db.engine.execute(text("UPDATE user SET role = 'user_admin' WHERE role = 'admin' AND username != 'admin'"))
            print("   ✅ Updated role names")
            
            # 2. Add new columns to PaymentConfirmation table
            print("2. Updating PaymentConfirmation table...")
            
            payment_columns = [col['name'] for col in inspector.get_columns('payment_confirmation')]
            
            if 'mobile_money_sender_name' not in payment_columns:
                db.engine.execute(text('ALTER TABLE payment_confirmation ADD COLUMN mobile_money_sender_name VARCHAR(100)'))
                print("   ✅ Added mobile_money_sender_name column")
            
            # SMS verification fields
            sms_fields = [
                'sms_sender VARCHAR(20)',
                'sms_amount FLOAT',
                'sms_sender_name VARCHAR(100)',
                'sms_transaction_ref VARCHAR(100)',
                'admin_verification_notes TEXT'
            ]
            
            for field in sms_fields:
                field_name = field.split()[0]
                if field_name not in payment_columns:
                    db.engine.execute(text(f'ALTER TABLE payment_confirmation ADD COLUMN {field}'))
                    print(f"   ✅ Added {field_name} column")
            
            # Add unique constraint to transaction_id
            try:
                db.engine.execute(text('CREATE UNIQUE INDEX idx_transaction_id ON payment_confirmation(transaction_id)'))
                print("   ✅ Added unique constraint to transaction_id")
            except Exception as e:
                if "already exists" not in str(e).lower():
                    print(f"   ⚠️ Could not add unique constraint: {e}")
            
            # 3. Add owner_user_id to ClientCompany table
            print("3. Updating ClientCompany table...")
            
            company_columns = [col['name'] for col in inspector.get_columns('client_company')]
            
            if 'owner_user_id' not in company_columns:
                db.engine.execute(text('ALTER TABLE client_company ADD COLUMN owner_user_id INTEGER'))
                print("   ✅ Added owner_user_id column")
                
                # Migrate existing companies to have proper ownership
                print("   🔄 Migrating existing company ownership...")
                companies = ClientCompany.query.all()
                for company in companies:
                    if company.company_email:
                        # Find user with matching email
                        user = User.query.filter_by(email=company.company_email).first()
                        if user:
                            company.owner_user_id = user.id
                            print(f"   ✅ Linked company '{company.company_name}' to user '{user.username}'")
                
                db.session.commit()
            
            # 4. Update existing payment confirmations with default mobile money names
            print("4. Updating existing payment confirmations...")
            
            payments_without_names = PaymentConfirmation.query.filter(
                PaymentConfirmation.mobile_money_sender_name.is_(None)
            ).all()
            
            for payment in payments_without_names:
                # Set a default name based on customer name
                payment.mobile_money_sender_name = payment.customer_name
                print(f"   ✅ Updated payment {payment.id} with sender name")
            
            db.session.commit()
            
            # 5. Add database constraints
            print("5. Adding database constraints...")
            
            try:
                # Add check constraints for amount validation
                db.engine.execute(text('''
                    ALTER TABLE payment_confirmation 
                    ADD CONSTRAINT check_minimum_amount 
                    CHECK (amount >= 1000)
                '''))
                print("   ✅ Added minimum amount constraint")
            except Exception as e:
                if "already exists" not in str(e).lower():
                    print(f"   ⚠️ Could not add minimum amount constraint: {e}")
            
            try:
                db.engine.execute(text('''
                    ALTER TABLE payment_confirmation 
                    ADD CONSTRAINT check_maximum_amount 
                    CHECK (amount <= 10000000)
                '''))
                print("   ✅ Added maximum amount constraint")
            except Exception as e:
                if "already exists" not in str(e).lower():
                    print(f"   ⚠️ Could not add maximum amount constraint: {e}")
            
            # 6. Create indexes for performance
            print("6. Creating performance indexes...")
            
            indexes = [
                'CREATE INDEX IF NOT EXISTS idx_user_role ON user(role)',
                'CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)',
                'CREATE INDEX IF NOT EXISTS idx_payment_status ON payment_confirmation(status)',
                'CREATE INDEX IF NOT EXISTS idx_payment_submitted ON payment_confirmation(submitted_at)',
                'CREATE INDEX IF NOT EXISTS idx_company_owner ON client_company(owner_user_id)',
                'CREATE INDEX IF NOT EXISTS idx_company_email ON client_company(company_email)'
            ]
            
            for index_sql in indexes:
                try:
                    db.engine.execute(text(index_sql))
                    index_name = index_sql.split()[5]  # Extract index name
                    print(f"   ✅ Created index {index_name}")
                except Exception as e:
                    if "already exists" not in str(e).lower():
                        print(f"   ⚠️ Could not create index: {e}")
            
            print()
            print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            print()
            
            # 7. Verification
            print("7. Verifying migration...")
            
            # Check user table
            users = User.query.all()
            print(f"   📊 Total users: {len(users)}")
            
            master_admins = User.query.filter_by(role='master_admin').count()
            user_admins = User.query.filter_by(role='user_admin').count()
            company_users = User.query.filter_by(role='company_user').count()
            
            print(f"   👑 Master admins: {master_admins}")
            print(f"   👤 User admins: {user_admins}")
            print(f"   🏢 Company users: {company_users}")
            
            # Check payment confirmations
            payments = PaymentConfirmation.query.all()
            payments_with_names = PaymentConfirmation.query.filter(
                PaymentConfirmation.mobile_money_sender_name.isnot(None)
            ).count()
            
            print(f"   💰 Total payments: {len(payments)}")
            print(f"   📝 Payments with sender names: {payments_with_names}")
            
            # Check companies
            companies = ClientCompany.query.all()
            companies_with_owners = ClientCompany.query.filter(
                ClientCompany.owner_user_id.isnot(None)
            ).count()
            
            print(f"   🏢 Total companies: {len(companies)}")
            print(f"   👤 Companies with owners: {companies_with_owners}")
            
            print()
            print("✅ VERIFICATION COMPLETE - ALL SYSTEMS READY!")
            
        except Exception as e:
            print(f"❌ MIGRATION FAILED: {e}")
            db.session.rollback()
            sys.exit(1)

if __name__ == '__main__':
    migrate_database()
