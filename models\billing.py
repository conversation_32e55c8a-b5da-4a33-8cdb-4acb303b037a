#!/usr/bin/env python3
"""
Billing model for EXLIPA
"""

from datetime import datetime
from .base import db, BaseModel, ValidationMixin
from utils.validators import InputValidator, ValidationError

class Bill(BaseModel, ValidationMixin):
    """Bill model for tracking company billing"""
    
    __tablename__ = 'bill'
    
    # Bill details
    bill_number = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    
    # Dates
    bill_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    
    # Status
    status = db.Column(db.String(20), default='Unpaid')  # Unpaid, Paid, Overdue, Cancelled
    
    # Payment details
    paid_amount = db.Column(db.Float, default=0.0)
    paid_date = db.Column(db.DateTime)
    payment_method = db.Column(db.String(50))
    payment_reference = db.Column(db.String(100))
    
    # Associations
    company_id = db.Column(db.Integer, db.ForeignKey('client_company.id'), nullable=False)
    
    # Relationships
    company = db.relationship('ClientCompany', backref='bills')
    
    def __repr__(self):
        return f'<Bill {self.bill_number}>'
    
    def validate(self):
        """Validate bill data"""
        if not self.bill_number:
            raise ValidationError("Bill number is required")
        
        if not self.description:
            raise ValidationError("Description is required")
        
        if not self.amount or self.amount <= 0:
            raise ValidationError("Amount must be a positive number")
        
        if not self.due_date:
            raise ValidationError("Due date is required")
    
    def is_overdue(self):
        """Check if bill is overdue"""
        return self.status == 'Unpaid' and datetime.utcnow() > self.due_date
    
    def mark_as_paid(self, amount, payment_method, payment_reference):
        """Mark bill as paid"""
        self.status = 'Paid'
        self.paid_amount = amount
        self.paid_date = datetime.utcnow()
        self.payment_method = payment_method
        self.payment_reference = payment_reference
        return self.save()
    
    @classmethod
    def generate_bill_number(cls):
        """Generate unique bill number"""
        count = cls.query.count() + 1
        year = datetime.now().year
        return f"BILL-{year}-{count:06d}"
