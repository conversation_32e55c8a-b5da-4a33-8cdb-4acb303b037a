{% extends "base.html" %}

{% block title %}Analytics Dashboard - {{ company.company_name }}{% endblock %}

{% block head %}
<style>
/* Performance optimizations for charts */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
    will-change: transform; /* Optimize for animations */
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: #f8fafc;
    border-radius: 12px;
    color: #64748b;
    font-weight: 500;
}

.chart-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimize canvas rendering */
canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    image-rendering: crisp-edges;
}

/* Reduce repaints */
.analytics-card {
    contain: layout style paint;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">📊
                        {% if session.language == 'sw' %}
                            Dashibodi ya Uchambuzi
                        {% else %}
                            Analytics Dashboard
                        {% endif %}
                    </h2>
                    <p class="text-muted mb-0">{{ company.company_name }} -
                        {% if session.language == 'sw' %}
                            Siku {{ analytics.period_days }} zilizopita
                        {% else %}
                            Last {{ analytics.period_days }} days
                        {% endif %}
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('analytics_dashboard', company_id=company.id, days=7) }}"
                           class="btn btn-outline-primary {% if selected_period == 7 %}active{% endif %}">
                           {% if session.language == 'sw' %}Siku 7{% else %}7 Days{% endif %}
                        </a>
                        <a href="{{ url_for('analytics_dashboard', company_id=company.id, days=30) }}"
                           class="btn btn-outline-primary {% if selected_period == 30 %}active{% endif %}">
                           {% if session.language == 'sw' %}Siku 30{% else %}30 Days{% endif %}
                        </a>
                        <a href="{{ url_for('analytics_dashboard', company_id=company.id, days=90) }}"
                           class="btn btn-outline-primary {% if selected_period == 90 %}active{% endif %}">
                           {% if session.language == 'sw' %}Siku 90{% else %}90 Days{% endif %}
                        </a>
                        <a href="{{ url_for('analytics_dashboard', company_id=company.id, days=365) }}"
                           class="btn btn-outline-primary {% if selected_period == 365 %}active{% endif %}">
                           {% if session.language == 'sw' %}Mwaka 1{% else %}1 Year{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-primary">{{ analytics.total_transactions }}</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Jumla ya Miamala{% else %}Total Transactions{% endif %}
                </div>
                <div class="small text-success">
                    {{ analytics.confirmed_transactions }}
                    {% if session.language == 'sw' %}imehakikiwa{% else %}confirmed{% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-success">TSh {{ "{:,.0f}".format(analytics.total_revenue) }}</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Jumla ya Mapato{% else %}Total Revenue{% endif %}
                </div>
                <div class="small text-info">
                    TSh {{ "{:,.0f}".format(analytics.net_revenue) }}
                    {% if session.language == 'sw' %}safi{% else %}net{% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-info">TSh {{ "{:,.0f}".format(analytics.average_transaction) }}</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Wastani wa Muamala{% else %}Average Transaction{% endif %}
                </div>
                <div class="small text-warning">
                    TSh {{ "{:,.0f}".format(analytics.total_fees) }}
                    {% if session.language == 'sw' %}ada{% else %}in fees{% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="glass-card p-3 text-center">
                <div class="h3 mb-1 text-warning">{{ "{:.1f}".format(analytics.conversion_rate) }}%</div>
                <div class="small text-muted">
                    {% if session.language == 'sw' %}Kiwango cha Ubadilishaji{% else %}Conversion Rate{% endif %}
                </div>
                <div class="small text-danger">
                    {{ analytics.pending_transactions }}
                    {% if session.language == 'sw' %}inasubiri{% else %}pending{% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <!-- Transaction Trend Chart -->
        <div class="col-md-8">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-line me-2"></i>Transaction Trend
                </h5>
                <div class="chart-container">
                    <div class="chart-loading" id="transactionLoading">Loading chart...</div>
                    <canvas id="transactionChart" style="display: none;"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Payment Methods Breakdown -->
        <div class="col-md-4">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-pie me-2"></i>Payment Methods
                </h5>
                <div class="chart-container">
                    <div class="chart-loading" id="paymentMethodLoading">Loading chart...</div>
                    <canvas id="paymentMethodChart" style="display: none;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row g-3">
        <!-- Payment Methods Table -->
        <div class="col-md-6">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-mobile-alt me-2"></i>Payment Method Details
                </h5>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Count</th>
                                <th>{{ t('Amount') }}</th>
                                <th>%</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for method, data in analytics.payment_methods.items() %}
                            <tr>
                                <td>
                                    <i class="fas fa-mobile-alt me-1"></i>{{ method }}
                                </td>
                                <td>{{ data.count }}</td>
                                <td>TSh {{ "{:,.0f}".format(data.amount) }}</td>
                                <td>{{ "{:.1f}".format((data.amount / analytics.total_revenue * 100) if analytics.total_revenue > 0 else 0) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Usage Status -->
        <div class="col-md-6">
            <div class="glass-card p-4">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-chart-bar me-2"></i>Current Plan Usage
                </h5>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Plan: {{ usage_status.tier_name }}</span>
                        <span class="badge bg-primary">{{ usage_status.tier_name }}</span>
                    </div>
                    
                    {% if usage_status.tier_name == 'Free' %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>Free Transactions</small>
                            <small>{{ 100 - usage_status.free_transactions_remaining }}/100</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            {% set usage_percent = ((100 - usage_status.free_transactions_remaining) / 100 * 100) %}
                            <div class="progress-bar {% if usage_percent > 80 %}bg-danger{% elif usage_percent > 60 %}bg-warning{% else %}bg-success{% endif %}" 
                                 style="width: {{ usage_percent }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="h6 mb-0">{{ usage_status.transaction_fee_percentage }}%</div>
                            <div class="small text-muted">Transaction Fee</div>
                        </div>
                        <div class="col-4">
                            <div class="h6 mb-0">{{ usage_status.transactions_used }}</div>
                            <div class="small text-muted">This Month</div>
                        </div>
                        <div class="col-4">
                            <div class="h6 mb-0">
                                {% if usage_status.transactions_limit == 0 %}∞{% else %}{{ usage_status.transactions_limit }}{% endif %}
                            </div>
                            <div class="small text-muted">Monthly Limit</div>
                        </div>
                    </div>
                    
                    {% if usage_status.should_show_upgrade %}
                    <div class="mt-3">
                        <a href="{{ url_for('pricing') }}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-arrow-up me-1"></i>Upgrade to {{ usage_status.next_tier }}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js with Performance Optimizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Performance optimized chart initialization
document.addEventListener('DOMContentLoaded', function() {
    // Use requestAnimationFrame to defer chart creation
    requestAnimationFrame(() => {
        initializeCharts();
    });
});

function initializeCharts() {
    // Transaction Trend Chart with Performance Optimizations
    const transactionCtx = document.getElementById('transactionChart');
    const transactionLoading = document.getElementById('transactionLoading');
    if (!transactionCtx) return;

    const ctx = transactionCtx.getContext('2d');
    const dailyData = {{ analytics.daily_data | tojson }};

    // Optimize data preparation
    const dates = Object.keys(dailyData).sort();
    const transactionCounts = [];
    const transactionAmounts = [];

    // Process data more efficiently
    for (let i = 0; i < dates.length; i++) {
        const date = dates[i];
        transactionCounts.push(dailyData[date].count);
        transactionAmounts.push(dailyData[date].amount);
    }

    // Create chart with performance optimizations
    const transactionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates.map(date => new Date(date).toLocaleDateString()),
            datasets: [{
                label: '{% if session.language == "sw" %}Idadi ya Miamala{% else %}Transaction Count{% endif %}',
                data: transactionCounts,
                borderColor: '#6366f1',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                borderWidth: 2,
                pointRadius: 3,
                pointHoverRadius: 5,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '{% if session.language == "sw" %}Kiasi (TSh){% else %}Amount (TSh){% endif %}',
                data: transactionAmounts,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 2,
                pointRadius: 3,
                pointHoverRadius: 5,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            // Performance optimizations
            animation: {
                duration: 750, // Reduced animation time
                easing: 'easeOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#6366f1',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxTicksLimit: 10 // Limit number of x-axis labels
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 6 // Limit y-axis ticks
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        maxTicksLimit: 6 // Limit y1-axis ticks
                    }
                }
            },
            // Performance settings
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });

    // Show chart and hide loading
    transactionLoading.style.display = 'none';
    transactionCtx.style.display = 'block';

    // Payment Methods Chart with Performance Optimizations
    const paymentMethodCtx = document.getElementById('paymentMethodChart');
    const paymentMethodLoading = document.getElementById('paymentMethodLoading');
    if (!paymentMethodCtx) return;

    const pmCtx = paymentMethodCtx.getContext('2d');
    const paymentMethods = {{ analytics.payment_methods | tojson }};

    // Optimize data preparation
    const methodLabels = Object.keys(paymentMethods);
    const methodAmounts = [];

    // Process data efficiently
    for (let i = 0; i < methodLabels.length; i++) {
        methodAmounts.push(paymentMethods[methodLabels[i]].amount);
    }

    // Create optimized doughnut chart
    const paymentMethodChart = new Chart(pmCtx, {
        type: 'doughnut',
        data: {
            labels: methodLabels,
            datasets: [{
                data: methodAmounts,
                backgroundColor: [
                    '#6366f1', // Primary purple
                    '#10b981', // Success green
                    '#f59e0b', // Warning amber
                    '#ef4444', // Danger red
                    '#3b82f6', // Info blue
                    '#8b5cf6'  // Secondary purple
                ],
                borderWidth: 2,
                borderColor: '#ffffff',
                hoverBorderWidth: 3,
                hoverOffset: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            // Performance optimizations
            animation: {
                duration: 500, // Faster animation
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#6366f1',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: TSh ${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            },
            // Performance settings
            elements: {
                arc: {
                    borderWidth: 2
                }
            }
        }
    });

    // Show chart and hide loading
    paymentMethodLoading.style.display = 'none';
    paymentMethodCtx.style.display = 'block';
}

// Error handling for chart initialization
window.addEventListener('error', function(e) {
    if (e.message.includes('Chart')) {
        console.warn('Chart initialization error handled:', e.message);
    }
});

// Debounced resize handler for performance
let resizeTimeout;
window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
        // Charts will auto-resize due to responsive: true
        // This just ensures we don't trigger too many resize events
    }, 250);
});

// Intersection Observer for lazy chart loading (if charts are below fold)
if ('IntersectionObserver' in window) {
    const chartObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const chartContainer = entry.target;
                const canvas = chartContainer.querySelector('canvas');
                if (canvas && canvas.style.display === 'none') {
                    // Chart is already initialized, just show it
                    const loading = chartContainer.querySelector('.chart-loading');
                    if (loading) loading.style.display = 'none';
                    canvas.style.display = 'block';
                }
                chartObserver.unobserve(chartContainer);
            }
        });
    }, {
        rootMargin: '50px'
    });

    // Observe chart containers
    document.querySelectorAll('.chart-container').forEach(container => {
        chartObserver.observe(container);
    });
}
</script>
{% endblock %}
