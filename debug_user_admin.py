#!/usr/bin/env python3
"""
Debug user admin login by adding a test route
"""

from app import app

# Add a debug route for user admin
@app.route('/debug-user-admin', methods=['GET', 'POST'])
def debug_user_admin():
    from flask import request, render_template_string, flash
    from app import User, simple_rate_limit, sanitize_input
    from werkzeug.security import check_password_hash
    
    if request.method == 'POST':
        print("=== DEBUG USER ADMIN LOGIN ===")
        
        username = request.form.get('username', '')
        password = request.form.get('password', '')
        
        print(f"Form data - Username: '{username}', Password: '{password}'")
        
        # Rate limiting
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        rate_limit_key = f"login_user_{client_ip}"
        
        if not simple_rate_limit(rate_limit_key, max_attempts=5, window_minutes=15):
            print("❌ Rate limit exceeded")
            flash('Too many login attempts. Please try again in 15 minutes.', 'danger')
            return render_template_string(DEBUG_TEMPLATE)
        
        print("✅ Rate limit OK")
        
        # Sanitize
        username = sanitize_input(username, 80)
        print(f"Sanitized username: '{username}'")
        
        if not username or not password:
            print("❌ Missing credentials")
            flash('Username and password are required', 'danger')
            return render_template_string(DEBUG_TEMPLATE)
        
        # Find user
        user = User.query.filter_by(username=username, is_active=True, role='user_admin').first()
        print(f"User query result: {user}")
        
        if user:
            print(f"User found - ID: {user.id}, Username: {user.username}, Role: {user.role}")
            
            if user.is_account_locked():
                print("❌ Account locked")
                flash('Account is temporarily locked', 'danger')
                return render_template_string(DEBUG_TEMPLATE)
            
            print("✅ Account not locked")
            
            password_valid = check_password_hash(user.password_hash, password)
            print(f"Password valid: {password_valid}")
            
            if password_valid:
                print("✅ USER ADMIN LOGIN SUCCESS!")
                flash('Login successful! (Debug mode)', 'success')
                return render_template_string(DEBUG_TEMPLATE + "<p><strong>USER ADMIN LOGIN WOULD SUCCEED</strong></p>")
            else:
                print("❌ Invalid password")
                flash('Invalid password', 'danger')
        else:
            print("❌ User not found")
            flash('User not found or not a user admin', 'danger')
        
        print("=== END DEBUG USER ADMIN ===")
    
    return render_template_string(DEBUG_TEMPLATE)

DEBUG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Debug User Admin Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5" style="max-width: 400px;">
        <h2>Debug User Admin Login</h2>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" value="useradmin" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" value="useradmin123" required>
            </div>
            <button type="submit" class="btn btn-primary">Test User Admin Login</button>
        </form>
        
        <hr>
        <p><a href="/login">Try Real User Admin Login</a></p>
        <p><a href="/admin-login">Try Master Admin Login</a></p>
    </div>
</body>
</html>
'''

if __name__ == '__main__':
    with app.app_context():
        print("Debug user admin route added at: http://localhost:5000/debug-user-admin")
