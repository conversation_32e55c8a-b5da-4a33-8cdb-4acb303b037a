#!/usr/bin/env python3
"""
Fee Calculator Service for EXLIPA
Standardizes all fee calculations across the platform
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class FeeCalculator:
    """Centralized fee calculation service"""
    
    # Default fee structure
    DEFAULT_FEES = {
        'transaction_fee_percentage': 2.5,  # 2.5% default transaction fee
        'minimum_fee': 100.0,  # Minimum TZS 100 fee
        'maximum_fee': 5000.0,  # Maximum TZS 5,000 fee
        'mobile_money_fees': {
            'M-Pesa': {
                'percentage': 0.0,  # No additional fee for M-Pesa
                'fixed': 0.0
            },
            'Tigo Pesa': {
                'percentage': 0.0,
                'fixed': 0.0
            },
            'Airtel Money': {
                'percentage': 0.0,
                'fixed': 0.0
            },
            'CRDB Lipa': {
                'percentage': 0.5,  # Additional 0.5% for CRDB
                'fixed': 50.0
            },
            'Halo Pesa': {
                'percentage': 0.3,
                'fixed': 30.0
            }
        }
    }
    
    @classmethod
    def calculate_transaction_fee(cls, amount: float, company=None, mobile_operator: str = None) -> Dict:
        """
        Calculate transaction fee for a payment
        
        Args:
            amount: Transaction amount
            company: ClientCompany instance (optional)
            mobile_operator: Mobile money operator (optional)
            
        Returns:
            Dict with fee breakdown
        """
        try:
            # Convert to Decimal for precise calculations
            amount_decimal = Decimal(str(amount))
            
            # Get company-specific fee rate or use default
            fee_percentage = cls._get_company_fee_percentage(company)
            
            # Calculate base transaction fee
            base_fee = amount_decimal * (Decimal(str(fee_percentage)) / 100)
            
            # Apply minimum and maximum limits
            min_fee = Decimal(str(cls.DEFAULT_FEES['minimum_fee']))
            max_fee = Decimal(str(cls.DEFAULT_FEES['maximum_fee']))
            
            base_fee = max(min_fee, min(base_fee, max_fee))
            
            # Add mobile money operator fees if applicable
            operator_fee = cls._calculate_operator_fee(amount_decimal, mobile_operator)
            
            # Calculate total fee
            total_fee = base_fee + operator_fee
            
            # Calculate net amount (amount - fees)
            net_amount = amount_decimal - total_fee
            
            # Round to 2 decimal places
            total_fee = total_fee.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            net_amount = net_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            return {
                'gross_amount': float(amount_decimal),
                'base_fee': float(base_fee),
                'operator_fee': float(operator_fee),
                'total_fee': float(total_fee),
                'net_amount': float(net_amount),
                'fee_percentage': fee_percentage,
                'mobile_operator': mobile_operator,
                'breakdown': {
                    'base_fee_percentage': fee_percentage,
                    'operator_fee_details': cls._get_operator_fee_details(mobile_operator)
                }
            }
            
        except Exception as e:
            logger.error(f"Fee calculation error: {str(e)}")
            # Return safe defaults
            return {
                'gross_amount': amount,
                'base_fee': 0.0,
                'operator_fee': 0.0,
                'total_fee': 0.0,
                'net_amount': amount,
                'fee_percentage': 0.0,
                'mobile_operator': mobile_operator,
                'error': str(e)
            }
    
    @classmethod
    def _get_company_fee_percentage(cls, company) -> float:
        """Get fee percentage for a company based on their subscription tier"""
        try:
            if not company:
                return cls.DEFAULT_FEES['transaction_fee_percentage']
            
            # Get company's current subscription tier
            subscription = company.get_active_subscription()
            if not subscription:
                return cls.DEFAULT_FEES['transaction_fee_percentage']
            
            # Return tier-specific fee percentage
            tier = subscription.pricing_tier
            return tier.transaction_fee_percentage if tier else cls.DEFAULT_FEES['transaction_fee_percentage']
            
        except Exception as e:
            logger.error(f"Error getting company fee percentage: {str(e)}")
            return cls.DEFAULT_FEES['transaction_fee_percentage']
    
    @classmethod
    def _calculate_operator_fee(cls, amount: Decimal, mobile_operator: str) -> Decimal:
        """Calculate mobile money operator-specific fees"""
        try:
            if not mobile_operator:
                return Decimal('0.0')
            
            operator_fees = cls.DEFAULT_FEES['mobile_money_fees'].get(mobile_operator, {})
            
            # Calculate percentage fee
            percentage_fee = amount * (Decimal(str(operator_fees.get('percentage', 0.0))) / 100)
            
            # Add fixed fee
            fixed_fee = Decimal(str(operator_fees.get('fixed', 0.0)))
            
            return percentage_fee + fixed_fee
            
        except Exception as e:
            logger.error(f"Error calculating operator fee: {str(e)}")
            return Decimal('0.0')
    
    @classmethod
    def _get_operator_fee_details(cls, mobile_operator: str) -> Dict:
        """Get operator fee details for transparency"""
        if not mobile_operator:
            return {}
        
        return cls.DEFAULT_FEES['mobile_money_fees'].get(mobile_operator, {})
    
    @classmethod
    def calculate_subscription_fee(cls, pricing_tier, duration_months: int = 1) -> Dict:
        """Calculate subscription fee for a pricing tier"""
        try:
            if not pricing_tier:
                raise ValueError("Pricing tier is required")
            
            base_price = Decimal(str(pricing_tier.price))
            duration = Decimal(str(duration_months))
            
            # Calculate total subscription cost
            total_cost = base_price * duration
            
            # Apply discounts for longer subscriptions
            discount_percentage = cls._get_subscription_discount(duration_months)
            discount_amount = total_cost * (Decimal(str(discount_percentage)) / 100)
            
            final_cost = total_cost - discount_amount
            
            # Round to 2 decimal places
            total_cost = total_cost.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            discount_amount = discount_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            final_cost = final_cost.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            return {
                'tier_name': pricing_tier.name,
                'base_price': float(base_price),
                'duration_months': duration_months,
                'subtotal': float(total_cost),
                'discount_percentage': discount_percentage,
                'discount_amount': float(discount_amount),
                'final_cost': float(final_cost),
                'currency': pricing_tier.currency
            }
            
        except Exception as e:
            logger.error(f"Subscription fee calculation error: {str(e)}")
            return {
                'error': str(e),
                'final_cost': 0.0
            }
    
    @classmethod
    def _get_subscription_discount(cls, duration_months: int) -> float:
        """Get discount percentage based on subscription duration"""
        if duration_months >= 12:
            return 15.0  # 15% discount for annual subscriptions
        elif duration_months >= 6:
            return 10.0  # 10% discount for 6-month subscriptions
        elif duration_months >= 3:
            return 5.0   # 5% discount for quarterly subscriptions
        else:
            return 0.0   # No discount for monthly subscriptions
    
    @classmethod
    def calculate_pos_transaction_fee(cls, amount: float, payment_method: str, company=None) -> Dict:
        """Calculate fees for POS transactions"""
        try:
            amount_decimal = Decimal(str(amount))
            
            # POS transactions have different fee structure
            if payment_method == 'Cash':
                # No fees for cash transactions
                return {
                    'gross_amount': float(amount_decimal),
                    'total_fee': 0.0,
                    'net_amount': float(amount_decimal),
                    'payment_method': payment_method
                }
            
            elif payment_method in ['Mobile Money', 'M-Pesa', 'Tigo Pesa', 'Airtel Money']:
                # Use standard mobile money fees
                return cls.calculate_transaction_fee(amount, company, payment_method)
            
            elif payment_method == 'Card':
                # Card transaction fees (typically higher)
                card_fee_percentage = 3.5  # 3.5% for card transactions
                fee = amount_decimal * (Decimal(str(card_fee_percentage)) / 100)
                
                # Apply minimum fee
                min_fee = Decimal('200.0')  # Minimum TZS 200 for card transactions
                fee = max(fee, min_fee)
                
                net_amount = amount_decimal - fee
                
                return {
                    'gross_amount': float(amount_decimal),
                    'total_fee': float(fee),
                    'net_amount': float(net_amount),
                    'fee_percentage': card_fee_percentage,
                    'payment_method': payment_method
                }
            
            else:
                # Unknown payment method, no fees
                return {
                    'gross_amount': float(amount_decimal),
                    'total_fee': 0.0,
                    'net_amount': float(amount_decimal),
                    'payment_method': payment_method
                }
                
        except Exception as e:
            logger.error(f"POS fee calculation error: {str(e)}")
            return {
                'gross_amount': amount,
                'total_fee': 0.0,
                'net_amount': amount,
                'payment_method': payment_method,
                'error': str(e)
            }
    
    @classmethod
    def get_fee_summary_for_company(cls, company) -> Dict:
        """Get fee summary for a company"""
        try:
            subscription = company.get_active_subscription() if company else None
            tier = subscription.pricing_tier if subscription else None
            
            return {
                'company_name': company.company_name if company else 'Unknown',
                'current_tier': tier.name if tier else 'No Subscription',
                'transaction_fee_percentage': tier.transaction_fee_percentage if tier else cls.DEFAULT_FEES['transaction_fee_percentage'],
                'minimum_fee': cls.DEFAULT_FEES['minimum_fee'],
                'maximum_fee': cls.DEFAULT_FEES['maximum_fee'],
                'mobile_money_fees': cls.DEFAULT_FEES['mobile_money_fees'],
                'subscription_active': subscription.is_active if subscription else False,
                'subscription_expires': subscription.end_date.isoformat() if subscription and subscription.end_date else None
            }
            
        except Exception as e:
            logger.error(f"Error getting fee summary: {str(e)}")
            return {
                'error': str(e)
            }
