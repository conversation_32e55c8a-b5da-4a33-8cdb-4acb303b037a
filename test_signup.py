#!/usr/bin/env python3
"""
Test signup functionality
"""

from app import app, db, PricingTier

def test_signup_route():
    """Test the signup route functionality"""
    with app.app_context():
        print("=== TESTING SIGNUP ROUTE ===")
        
        # Test tier 2 specifically
        tier_id = 2
        print(f"Testing tier ID: {tier_id}")
        
        # Check if tier exists
        tier = PricingTier.query.get(tier_id)
        print(f"Tier found: {tier is not None}")
        
        if tier:
            print(f"Tier details:")
            print(f"  ID: {tier.id}")
            print(f"  Name: {tier.name}")
            print(f"  Setup Fee: {tier.setup_fee}")
            print(f"  Monthly Fee: {tier.monthly_fee}")
            print(f"  Description: {tier.description}")
            
            # Test the calculation that happens in the route
            try:
                amount = tier.setup_fee + tier.monthly_fee
                print(f"  Total amount: {amount}")
                print("✅ Amount calculation works")
            except Exception as e:
                print(f"❌ Amount calculation error: {e}")
            
            # Test template variables
            try:
                template_vars = {
                    'tier': tier,
                    'total_amount': tier.setup_fee + tier.monthly_fee
                }
                print("✅ Template variables prepared successfully")
            except Exception as e:
                print(f"❌ Template variables error: {e}")
        
        else:
            print("❌ Tier not found!")

if __name__ == '__main__':
    test_signup_route()
