#!/usr/bin/env python3
"""
Check database structure
"""

import sqlite3
import os

def check_database():
    db_path = 'payment_system.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file does not exist")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 Database: {db_path}")
        print(f"📊 Tables found: {len(tables)}")
        
        if tables:
            for table in tables:
                table_name = table[0]
                print(f"\n🔍 Table: {table_name}")
                
                # Get columns for this table
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")
        else:
            print("❌ No tables found in database")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")

if __name__ == '__main__':
    check_database()
